try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="da6d565c-16ce-4b17-83ed-96922814df83",e._sentryDebugIdIdentifier="sentry-dbid-da6d565c-16ce-4b17-83ed-96922814df83")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4850],{29695:(e,t,r)=>{let n,i,s,o,a,l;r.d(t,{w:()=>rz});var c,u=r(49636),d=r(2332),h=r(73314),p=r(60567),f=r(87624),m=r(81611),y=r(43275),g=(r(12398),r(27122)),v=r(28385),S=r(2257),_=r(51290),w=r(89135),b=r(10160),k=r(35868),E=r(54058),M=r(31209),I=r(31271),C=r(43115),R=r(42106),T=r(88722),x=r(97684),D=r(40364),O=r(7755),N=r(86851),A=r(97309),L=r(62234),P=r(7916),F=r(79152);let U=u.O,B="sentryReplaySession",z="Unable to send Replay";var W=Object.defineProperty,j=(e,t,r)=>t in e?W(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,H=(e,t,r)=>j(e,"symbol"!=typeof t?t+"":t,r),$=(e=>(e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment",e))($||{});function q(e){let t=e?.host;return t?.shadowRoot===e}function K(e){return"[object ShadowRoot]"===Object.prototype.toString.call(e)}function V(e){try{var t;let r=e.rules||e.cssRules;return r?((t=Array.from(r,J).join("")).includes(" background-clip: text;")&&!t.includes(" -webkit-background-clip: text;")&&(t=t.replace(/\sbackground-clip:\s*text;/g," -webkit-background-clip: text; background-clip: text;")),t):null}catch(e){return null}}function J(e){let t;if("styleSheet"in e)try{t=V(e.styleSheet)||function(e){let{cssText:t}=e;if(t.split('"').length<3)return t;let r=["@import",`url(${JSON.stringify(e.href)})`];return""===e.layerName?r.push("layer"):e.layerName&&r.push(`layer(${e.layerName})`),e.supportsText&&r.push(`supports(${e.supportsText})`),e.media.length&&r.push(e.media.mediaText),r.join(" ")+";"}(e)}catch(e){}else if("selectorText"in e){let t=e.cssText,r=e.selectorText.includes(":"),n="string"==typeof e.style.all&&e.style.all;if(n&&(t=function(e){let t="";for(let r=0;r<e.style.length;r++){let n=e.style,i=n[r],s=n.getPropertyPriority(i);t+=`${i}:${n.getPropertyValue(i)}${s?" !important":""};`}return`${e.selectorText} { ${t} }`}(e)),r&&(t=t.replace(/(\[(?:[\w-]+)[^\\])(:(?:[\w-]+)\])/gm,"$1\\$2")),r||n)return t}return t||e.cssText}class G{constructor(){H(this,"idNodeMap",new Map),H(this,"nodeMetaMap",new WeakMap)}getId(e){return e?this.getMeta(e)?.id??-1:-1}getNode(e){return this.idNodeMap.get(e)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(e){return this.nodeMetaMap.get(e)||null}removeNodeFromMap(e){let t=this.getId(e);this.idNodeMap.delete(t),e.childNodes&&e.childNodes.forEach(e=>this.removeNodeFromMap(e))}has(e){return this.idNodeMap.has(e)}hasNode(e){return this.nodeMetaMap.has(e)}add(e,t){let r=t.id;this.idNodeMap.set(r,e),this.nodeMetaMap.set(e,t)}replace(e,t){let r=this.getNode(e);if(r){let e=this.nodeMetaMap.get(r);e&&this.nodeMetaMap.set(t,e)}this.idNodeMap.set(e,t)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function X({maskInputOptions:e,tagName:t,type:r}){return"OPTION"===t&&(t="SELECT"),!!(e[t.toLowerCase()]||r&&e[r]||"password"===r||"INPUT"===t&&!r&&e.text)}function Y({isMasked:e,element:t,value:r,maskInputFn:n}){let i=r||"";return e?(n&&(i=n(i,t)),"*".repeat(i.length)):i}function Q(e){return e.toLowerCase()}function Z(e){return e.toUpperCase()}let ee="__rrweb_original__";function et(e){let t=e.type;return e.hasAttribute("data-rr-is-password")?"password":t?Q(t):null}function er(e,t,r){return"INPUT"===t&&("radio"===r||"checkbox"===r)?e.getAttribute("value")||"":e.value}function en(e,t){let r;try{r=new URL(e,t??window.location.href)}catch(e){return null}let n=r.pathname.match(/\.([0-9a-z]+)(?:$)/i);return n?.[1]??null}let ei={};function es(e){let t=ei[e];if(t)return t;let r=window.document,n=window[e];if(r&&"function"==typeof r.createElement)try{let t=r.createElement("iframe");t.hidden=!0,r.head.appendChild(t);let i=t.contentWindow;i&&i[e]&&(n=i[e]),r.head.removeChild(t)}catch(e){}return ei[e]=n.bind(window)}function eo(...e){return es("setTimeout")(...e)}function ea(...e){return es("clearTimeout")(...e)}function el(e){try{return e.contentDocument}catch(e){}}let ec=1,eu=RegExp("[^a-z0-9-_:]");function ed(){return ec++}let eh=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,ep=/^(?:[a-z+]+:)?\/\//i,ef=/^www\..*/i,em=/^(data:)([^,]*),(.*)/i;function ey(e,t){return(e||"").replace(eh,(e,r,n,i,s,o)=>{let a=n||s||o,l=r||i||"";if(!a)return e;if(ep.test(a)||ef.test(a)||em.test(a))return`url(${l}${a}${l})`;if("/"===a[0]){let e;return`url(${l}${(t.indexOf("//")>-1?t.split("/").slice(0,3).join("/"):t.split("/")[0]).split("?")[0]+a}${l})`}let c=t.split("/"),u=a.split("/");for(let e of(c.pop(),u))if("."===e)continue;else".."===e?c.pop():c.push(e);return`url(${l}${c.join("/")}${l})`})}let eg=/^[^ \t\n\r\u000c]+/,ev=/^[, \t\n\r\u000c]+/,eS=new WeakMap;function e_(e,t){return t&&""!==t.trim()?ew(e,t):t}function ew(e,t){let r=eS.get(e);if(r||(r=e.createElement("a"),eS.set(e,r)),t){if(t.startsWith("blob:")||t.startsWith("data:"))return t}else t="";return r.setAttribute("href",t),r.href}function eb(e,t,r,n,i,s){if(!n)return n;if("src"===r||"href"===r&&("use"!==t||"#"!==n[0])||"xlink:href"===r&&"#"!==n[0])return e_(e,n);if("background"===r&&("table"===t||"td"===t||"th"===t))return e_(e,n);if("srcset"===r)return function(e,t){if(""===t.trim())return t;let r=0;function n(e){let n,i=e.exec(t.substring(r));return i?(n=i[0],r+=n.length,n):""}let i=[];for(;n(ev),!(r>=t.length);){let s=n(eg);if(","===s.slice(-1))s=e_(e,s.substring(0,s.length-1)),i.push(s);else{let n="";s=e_(e,s);let o=!1;for(;;){let e=t.charAt(r);if(""===e){i.push((s+n).trim());break}if(o)")"===e&&(o=!1);else if(","===e){r+=1,i.push((s+n).trim());break}else"("===e&&(o=!0);n+=e,r+=1}}}return i.join(", ")}(e,n);if("style"===r)return ey(n,ew(e));else if("object"===t&&"data"===r)return e_(e,n);return"function"==typeof s?s(r,n,i):n}function ek(e,t,r){return("video"===e||"audio"===e)&&"autoplay"===t}function eE(e,t,r,n){try{if(n&&e.matches(n))return!1;if("string"==typeof t){if(e.classList.contains(t))return!0}else for(let r=e.classList.length;r--;){let n=e.classList[r];if(t.test(n))return!0}if(r)return e.matches(r)}catch(e){}return!1}function eM(e,t,r=1/0,n=0){return!e||e.nodeType!==e.ELEMENT_NODE||n>r?-1:t(e)?n:eM(e.parentNode,t,r,n+1)}function eI(e,t){return r=>{if(null===r)return!1;try{if(e){if("string"==typeof e){if(r.matches(`.${e}`))return!0}else if(function(e,t){for(let r=e.classList.length;r--;){let n=e.classList[r];if(t.test(n))return!0}return!1}(r,e))return!0}if(t&&r.matches(t))return!0;return!1}catch{return!1}}}function eC(e,t,r,n,i,s){try{let o=e.nodeType===e.ELEMENT_NODE?e:e.parentElement;if(null===o)return!1;if("INPUT"===o.tagName){let e=o.getAttribute("autocomplete");if(["current-password","new-password","cc-number","cc-exp","cc-exp-month","cc-exp-year","cc-csc"].includes(e))return!0}let a=-1,l=-1;if(s){if((l=eM(o,eI(n,i)))<0)return!0;a=eM(o,eI(t,r),l>=0?l:1/0)}else{if((a=eM(o,eI(t,r)))<0)return!1;l=eM(o,eI(n,i),a>=0?a:1/0)}return a>=0?!(l>=0)||a<=l:!(l>=0)&&!!s}catch(e){}return!!s}function eR(e){return null==e?"":e.toLowerCase()}function eT(e,t){let r,{doc:s,mirror:o,blockClass:a,blockSelector:l,unblockSelector:c,maskAllText:u,maskTextClass:d,unmaskTextClass:h,maskTextSelector:p,unmaskTextSelector:f,skipChild:m=!1,inlineStylesheet:y=!0,maskInputOptions:g={},maskAttributeFn:v,maskTextFn:S,maskInputFn:_,slimDOMOptions:w,dataURLOptions:b={},inlineImages:k=!1,recordCanvas:E=!1,onSerialize:M,onIframeLoad:I,iframeLoadTimeout:C=5e3,onStylesheetLoad:R,stylesheetLoadTimeout:T=5e3,keepIframeSrcFn:x=()=>!1,newlyAddedElement:D=!1}=t,{preserveWhiteSpace:O=!0}=t,N=function(e,t){let{doc:r,mirror:s,blockClass:o,blockSelector:a,unblockSelector:l,maskAllText:c,maskAttributeFn:u,maskTextClass:d,unmaskTextClass:h,maskTextSelector:p,unmaskTextSelector:f,inlineStylesheet:m,maskInputOptions:y={},maskTextFn:g,maskInputFn:v,dataURLOptions:S={},inlineImages:_,recordCanvas:w,keepIframeSrcFn:b,newlyAddedElement:k=!1}=t,E=function(e,t){if(!t.hasNode(e))return;let r=t.getId(e);return 1===r?void 0:r}(r,s);switch(e.nodeType){case e.DOCUMENT_NODE:if("CSS1Compat"!==e.compatMode)return{type:$.Document,childNodes:[],compatMode:e.compatMode};return{type:$.Document,childNodes:[]};case e.DOCUMENT_TYPE_NODE:return{type:$.DocumentType,name:e.name,publicId:e.publicId,systemId:e.systemId,rootId:E};case e.ELEMENT_NODE:return function(e,t){let r,{doc:s,blockClass:o,blockSelector:a,unblockSelector:l,inlineStylesheet:c,maskInputOptions:u={},maskAttributeFn:d,maskInputFn:h,dataURLOptions:p={},inlineImages:f,recordCanvas:m,keepIframeSrcFn:y,newlyAddedElement:g=!1,rootId:v,maskTextClass:S,unmaskTextClass:_,maskTextSelector:w,unmaskTextSelector:b}=t,k=eE(e,o,a,l),E=function(e){if(e instanceof HTMLFormElement)return"form";let t=Q(e.tagName);return eu.test(t)?"div":t}(e),M={},I=e.attributes.length;for(let t=0;t<I;t++){let r=e.attributes[t];r.name&&!ek(E,r.name,r.value)&&(M[r.name]=eb(s,E,Q(r.name),r.value,e,d))}if("link"===E&&c){let t=Array.from(s.styleSheets).find(t=>t.href===e.href),r=null;t&&(r=V(t)),r&&(M.rel=null,M.href=null,M.crossorigin=null,M._cssText=ey(r,t.href))}if("style"===E&&e.sheet&&!(e.innerText||e.textContent||"").trim().length){let t=V(e.sheet);t&&(M._cssText=ey(t,ew(s)))}if("input"===E||"textarea"===E||"select"===E||"option"===E){let t=et(e),r=er(e,Z(E),t),n=e.checked;if("submit"!==t&&"button"!==t&&r){let n=eC(e,S,w,_,b,X({type:t,tagName:Z(E),maskInputOptions:u}));M.value=Y({isMasked:n,element:e,value:r,maskInputFn:h})}n&&(M.checked=n)}if("option"===E&&(e.selected&&!u.select?M.selected=!0:delete M.selected),"canvas"===E&&m){if("2d"===e.__context)!function(e){let t=e.getContext("2d");if(!t)return!0;for(let r=0;r<e.width;r+=50)for(let n=0;n<e.height;n+=50){let i=t.getImageData;if(new Uint32Array((ee in i?i[ee]:i).call(t,r,n,Math.min(50,e.width-r),Math.min(50,e.height-n)).data.buffer).some(e=>0!==e))return!1}return!0}(e)&&(M.rr_dataURL=e.toDataURL(p.type,p.quality));else if(!("__context"in e)){let t=e.toDataURL(p.type,p.quality),r=s.createElement("canvas");r.width=e.width,r.height=e.height,t!==r.toDataURL(p.type,p.quality)&&(M.rr_dataURL=t)}}if("img"===E&&f){n||(i=(n=s.createElement("canvas")).getContext("2d"));let t=e.currentSrc||e.getAttribute("src")||"<unknown-src>",r=e.crossOrigin,o=()=>{e.removeEventListener("load",o);try{n.width=e.naturalWidth,n.height=e.naturalHeight,i.drawImage(e,0,0),M.rr_dataURL=n.toDataURL(p.type,p.quality)}catch(r){if("anonymous"!==e.crossOrigin){e.crossOrigin="anonymous",e.complete&&0!==e.naturalWidth?o():e.addEventListener("load",o);return}console.warn(`Cannot inline img src=${t}! Error: ${r}`)}"anonymous"===e.crossOrigin&&(r?M.crossOrigin=r:e.removeAttribute("crossorigin"))};e.complete&&0!==e.naturalWidth?o():e.addEventListener("load",o)}if(("audio"===E||"video"===E)&&(M.rr_mediaState=e.paused?"paused":"played",M.rr_mediaCurrentTime=e.currentTime),!g&&(e.scrollLeft&&(M.rr_scrollLeft=e.scrollLeft),e.scrollTop&&(M.rr_scrollTop=e.scrollTop)),k){let{width:t,height:r}=e.getBoundingClientRect();M={class:M.class,rr_width:`${t}px`,rr_height:`${r}px`}}"iframe"!==E||y(M.src)||(k||el(e)||(M.rr_src=M.src),delete M.src);try{customElements.get(E)&&(r=!0)}catch(e){}return{type:$.Element,tagName:E,attributes:M,childNodes:[],isSVG:!!("svg"===e.tagName||e.ownerSVGElement)||void 0,needBlock:k,rootId:v,isCustom:r}}(e,{doc:r,blockClass:o,blockSelector:a,unblockSelector:l,inlineStylesheet:m,maskAttributeFn:u,maskInputOptions:y,maskInputFn:v,dataURLOptions:S,inlineImages:_,recordCanvas:w,keepIframeSrcFn:b,newlyAddedElement:k,rootId:E,maskTextClass:d,unmaskTextClass:h,maskTextSelector:p,unmaskTextSelector:f});case e.TEXT_NODE:return function(e,t){let{maskAllText:r,maskTextClass:n,unmaskTextClass:i,maskTextSelector:s,unmaskTextSelector:o,maskTextFn:a,maskInputOptions:l,maskInputFn:c,rootId:u}=t,d=e.parentNode&&e.parentNode.tagName,h=e.textContent,p="STYLE"===d||void 0,f="SCRIPT"===d||void 0,m="TEXTAREA"===d||void 0;if(p&&h){try{e.nextSibling||e.previousSibling||e.parentNode.sheet?.cssRules&&(h=V(e.parentNode.sheet))}catch(t){console.warn(`Cannot get CSS styles from text's parentNode. Error: ${t}`,e)}h=ey(h,ew(t.doc))}f&&(h="SCRIPT_PLACEHOLDER");let y=eC(e,n,s,i,o,r);return p||f||m||!h||!y||(h=a?a(h,e.parentElement):h.replace(/[\S]/g,"*")),m&&h&&(l.textarea||y)&&(h=c?c(h,e.parentNode):h.replace(/[\S]/g,"*")),"OPTION"===d&&h&&(h=Y({isMasked:eC(e,n,s,i,o,X({type:null,tagName:d,maskInputOptions:l})),element:e,value:h,maskInputFn:c})),{type:$.Text,textContent:h||"",isStyle:p,rootId:u}}(e,{doc:r,maskAllText:c,maskTextClass:d,unmaskTextClass:h,maskTextSelector:p,unmaskTextSelector:f,maskTextFn:g,maskInputOptions:y,maskInputFn:v,rootId:E});case e.CDATA_SECTION_NODE:return{type:$.CDATA,textContent:"",rootId:E};case e.COMMENT_NODE:return{type:$.Comment,textContent:e.textContent||"",rootId:E};default:return!1}}(e,{doc:s,mirror:o,blockClass:a,blockSelector:l,maskAllText:u,unblockSelector:c,maskTextClass:d,unmaskTextClass:h,maskTextSelector:p,unmaskTextSelector:f,inlineStylesheet:y,maskInputOptions:g,maskAttributeFn:v,maskTextFn:S,maskInputFn:_,dataURLOptions:b,inlineImages:k,recordCanvas:E,keepIframeSrcFn:x,newlyAddedElement:D});if(!N)return console.warn(e,"not serialized"),null;r=o.hasNode(e)?o.getId(e):!function(e,t){if(t.comment&&e.type===$.Comment)return!0;if(e.type===$.Element){if(t.script&&("script"===e.tagName||"link"===e.tagName&&("preload"===e.attributes.rel||"modulepreload"===e.attributes.rel)||"link"===e.tagName&&"prefetch"===e.attributes.rel&&"string"==typeof e.attributes.href&&"js"===en(e.attributes.href)))return!0;else if(t.headFavicon&&("link"===e.tagName&&"shortcut icon"===e.attributes.rel||"meta"===e.tagName&&(eR(e.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===eR(e.attributes.name)||"icon"===eR(e.attributes.rel)||"apple-touch-icon"===eR(e.attributes.rel)||"shortcut icon"===eR(e.attributes.rel))))return!0;else if("meta"===e.tagName){if(t.headMetaDescKeywords&&eR(e.attributes.name).match(/^description|keywords$/))return!0;else if(t.headMetaSocial&&(eR(e.attributes.property).match(/^(og|twitter|fb):/)||eR(e.attributes.name).match(/^(og|twitter):/)||"pinterest"===eR(e.attributes.name)))return!0;else if(t.headMetaRobots&&("robots"===eR(e.attributes.name)||"googlebot"===eR(e.attributes.name)||"bingbot"===eR(e.attributes.name)))return!0;else if(t.headMetaHttpEquiv&&void 0!==e.attributes["http-equiv"])return!0;else if(t.headMetaAuthorship&&("author"===eR(e.attributes.name)||"generator"===eR(e.attributes.name)||"framework"===eR(e.attributes.name)||"publisher"===eR(e.attributes.name)||"progid"===eR(e.attributes.name)||eR(e.attributes.property).match(/^article:/)||eR(e.attributes.property).match(/^product:/)))return!0;else if(t.headMetaVerification&&("google-site-verification"===eR(e.attributes.name)||"yandex-verification"===eR(e.attributes.name)||"csrf-token"===eR(e.attributes.name)||"p:domain_verify"===eR(e.attributes.name)||"verify-v1"===eR(e.attributes.name)||"verification"===eR(e.attributes.name)||"shopify-checkout-api-token"===eR(e.attributes.name)))return!0}}return!1}(N,w)&&(O||N.type!==$.Text||N.isStyle||N.textContent.replace(/^\s+|\s+$/gm,"").length)?ed():-2;let A=Object.assign(N,{id:r});if(o.add(e,A),-2===r)return null;M&&M(e);let L=!m;if(A.type===$.Element){L=L&&!A.needBlock,delete A.needBlock;let t=e.shadowRoot;t&&K(t)&&(A.isShadowHost=!0)}if((A.type===$.Document||A.type===$.Element)&&L){w.headWhitespace&&A.type===$.Element&&"head"===A.tagName&&(O=!1);let t={doc:s,mirror:o,blockClass:a,blockSelector:l,maskAllText:u,unblockSelector:c,maskTextClass:d,unmaskTextClass:h,maskTextSelector:p,unmaskTextSelector:f,skipChild:m,inlineStylesheet:y,maskInputOptions:g,maskAttributeFn:v,maskTextFn:S,maskInputFn:_,slimDOMOptions:w,dataURLOptions:b,inlineImages:k,recordCanvas:E,preserveWhiteSpace:O,onSerialize:M,onIframeLoad:I,iframeLoadTimeout:C,onStylesheetLoad:R,stylesheetLoadTimeout:T,keepIframeSrcFn:x};for(let r of Array.from(e.childNodes)){let e=eT(r,t);e&&A.childNodes.push(e)}if(e.nodeType===e.ELEMENT_NODE&&e.shadowRoot)for(let r of Array.from(e.shadowRoot.childNodes)){let n=eT(r,t);n&&(K(e.shadowRoot)&&(n.isShadow=!0),A.childNodes.push(n))}}return e.parentNode&&q(e.parentNode)&&K(e.parentNode)&&(A.isShadow=!0),A.type!==$.Element||"iframe"!==A.tagName||eE(e,a,l,c)||function(e,t,r){let n,i=e.contentWindow;if(!i)return;let s=!1;try{n=i.document.readyState}catch(e){return}if("complete"!==n){let n=eo(()=>{s||(t(),s=!0)},r);e.addEventListener("load",()=>{ea(n),s=!0,t()});return}let o="about:blank";if(i.location.href!==o||e.src===o||""===e.src)return eo(t,0),e.addEventListener("load",t);e.addEventListener("load",t)}(e,()=>{let t=el(e);if(t&&I){let r=eT(t,{doc:t,mirror:o,blockClass:a,blockSelector:l,unblockSelector:c,maskAllText:u,maskTextClass:d,unmaskTextClass:h,maskTextSelector:p,unmaskTextSelector:f,skipChild:!1,inlineStylesheet:y,maskInputOptions:g,maskAttributeFn:v,maskTextFn:S,maskInputFn:_,slimDOMOptions:w,dataURLOptions:b,inlineImages:k,recordCanvas:E,preserveWhiteSpace:O,onSerialize:M,onIframeLoad:I,iframeLoadTimeout:C,onStylesheetLoad:R,stylesheetLoadTimeout:T,keepIframeSrcFn:x});r&&I(e,r)}},C),A.type===$.Element&&"link"===A.tagName&&"string"==typeof A.attributes.rel&&("stylesheet"===A.attributes.rel||"preload"===A.attributes.rel&&"string"==typeof A.attributes.href&&"css"===en(A.attributes.href))&&function(e,t,r){let n,i=!1;try{n=e.sheet}catch(e){return}if(n)return;let s=eo(()=>{i||(t(),i=!0)},r);e.addEventListener("load",()=>{ea(s),i=!0,t()})}(e,()=>{if(R){let t=eT(e,{doc:s,mirror:o,blockClass:a,blockSelector:l,unblockSelector:c,maskAllText:u,maskTextClass:d,unmaskTextClass:h,maskTextSelector:p,unmaskTextSelector:f,skipChild:!1,inlineStylesheet:y,maskInputOptions:g,maskAttributeFn:v,maskTextFn:S,maskInputFn:_,slimDOMOptions:w,dataURLOptions:b,inlineImages:k,recordCanvas:E,preserveWhiteSpace:O,onSerialize:M,onIframeLoad:I,iframeLoadTimeout:C,onStylesheetLoad:R,stylesheetLoadTimeout:T,keepIframeSrcFn:x});t&&R(e,t)}},T),A}function ex(e,t,r=document){let n={capture:!0,passive:!0};return r.addEventListener(e,t,n),()=>r.removeEventListener(e,t,n)}let eD="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.",eO={map:{},getId:()=>(console.error(eD),-1),getNode:()=>(console.error(eD),null),removeNodeFromMap(){console.error(eD)},has:()=>(console.error(eD),!1),reset(){console.error(eD)}};function eN(e,t,r={}){let n=null,i=0;return function(...s){let o=Date.now();i||!1!==r.leading||(i=o);let a=t-(o-i),l=this;a<=0||a>t?(n&&(function(...e){eX("clearTimeout")(...e)}(n),n=null),i=o,e.apply(l,s)):n||!1===r.trailing||(n=eY(()=>{i=!1===r.leading?0:Date.now(),n=null,e.apply(l,s)},a))}}function eA(e,t,r){try{if(!(t in e))return()=>{};let n=e[t],i=r(n);return"function"==typeof i&&(i.prototype=i.prototype||{},Object.defineProperties(i,{__rrweb_original__:{enumerable:!1,value:n}})),e[t]=i,()=>{e[t]=n}}catch{return()=>{}}}"undefined"!=typeof window&&window.Proxy&&window.Reflect&&(eO=new Proxy(eO,{get:(e,t,r)=>("map"===t&&console.error(eD),Reflect.get(e,t,r))}));let eL=Date.now;function eP(e){let t=e.document;return{left:t.scrollingElement?t.scrollingElement.scrollLeft:void 0!==e.pageXOffset?e.pageXOffset:t?.documentElement.scrollLeft||t?.body?.parentElement?.scrollLeft||t?.body?.scrollLeft||0,top:t.scrollingElement?t.scrollingElement.scrollTop:void 0!==e.pageYOffset?e.pageYOffset:t?.documentElement.scrollTop||t?.body?.parentElement?.scrollTop||t?.body?.scrollTop||0}}function eF(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function eU(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function eB(e){if(!e)return null;try{return e.nodeType===e.ELEMENT_NODE?e:e.parentElement}catch(e){return null}}function ez(e,t,r,n,i){if(!e)return!1;let s=eB(e);if(!s)return!1;let o=eI(t,r);if(!i){let e=n&&s.matches(n);return o(s)&&!e}let a=eM(s,o),l=-1;return!(a<0)&&(n&&(l=eM(s,eI(null,n))),a>-1&&l<0||a<l)}function eW(e,t){return -2===t.getId(e)}function ej(e){return!!e.changedTouches}function eH(e,t){return!!("IFRAME"===e.nodeName&&t.getMeta(e))}function e$(e,t){return!!("LINK"===e.nodeName&&e.nodeType===e.ELEMENT_NODE&&e.getAttribute&&"stylesheet"===e.getAttribute("rel")&&t.getMeta(e))}function eq(e){return!!e?.shadowRoot}/[1-9][0-9]{12}/.test(Date.now().toString())||(eL=()=>new Date().getTime());class eK{constructor(){this.id=1,this.styleIDMap=new WeakMap,this.idStyleMap=new Map}getId(e){return this.styleIDMap.get(e)??-1}has(e){return this.styleIDMap.has(e)}add(e,t){let r;return this.has(e)?this.getId(e):(r=void 0===t?this.id++:t,this.styleIDMap.set(e,r),this.idStyleMap.set(r,e),r)}getStyle(e){return this.idStyleMap.get(e)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}}function eV(e){let t=null;return e.getRootNode?.()?.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&e.getRootNode().host&&(t=e.getRootNode().host),t}function eJ(e){let t=e.ownerDocument;return!!t&&(t.contains(e)||function(e){let t=e.ownerDocument;if(!t)return!1;let r=function(e){let t,r=e;for(;t=eV(r);)r=t;return r}(e);return t.contains(r)}(e))}let eG={};function eX(e){let t=eG[e];if(t)return t;let r=window.document,n=window[e];if(r&&"function"==typeof r.createElement)try{let t=r.createElement("iframe");t.hidden=!0,r.head.appendChild(t);let i=t.contentWindow;i&&i[e]&&(n=i[e]),r.head.removeChild(t)}catch(e){}return eG[e]=n.bind(window)}function eY(...e){return eX("setTimeout")(...e)}var eQ=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(eQ||{}),eZ=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e[e.CustomElement=16]="CustomElement",e))(eZ||{}),e0=(e=>(e[e.MouseUp=0]="MouseUp",e[e.MouseDown=1]="MouseDown",e[e.Click=2]="Click",e[e.ContextMenu=3]="ContextMenu",e[e.DblClick=4]="DblClick",e[e.Focus=5]="Focus",e[e.Blur=6]="Blur",e[e.TouchStart=7]="TouchStart",e[e.TouchMove_Departed=8]="TouchMove_Departed",e[e.TouchEnd=9]="TouchEnd",e[e.TouchCancel=10]="TouchCancel",e))(e0||{}),e1=(e=>(e[e.Mouse=0]="Mouse",e[e.Pen=1]="Pen",e[e.Touch=2]="Touch",e))(e1||{}),e2=(e=>(e[e.Play=0]="Play",e[e.Pause=1]="Pause",e[e.Seeked=2]="Seeked",e[e.VolumeChange=3]="VolumeChange",e[e.RateChange=4]="RateChange",e))(e2||{});function e3(e){try{return e.contentDocument}catch(e){}}class e5{constructor(){this.length=0,this.head=null,this.tail=null}get(e){if(e>=this.length)throw Error("Position outside of list range");let t=this.head;for(let r=0;r<e;r++)t=t?.next||null;return t}addNode(e){let t={value:e,previous:null,next:null};if(e.__ln=t,e.previousSibling&&"__ln"in e.previousSibling){let r=e.previousSibling.__ln.next;t.next=r,t.previous=e.previousSibling.__ln,e.previousSibling.__ln.next=t,r&&(r.previous=t)}else if(e.nextSibling&&"__ln"in e.nextSibling&&e.nextSibling.__ln.previous){let r=e.nextSibling.__ln.previous;t.previous=r,t.next=e.nextSibling.__ln,e.nextSibling.__ln.previous=t,r&&(r.next=t)}else this.head&&(this.head.previous=t),t.next=this.head,this.head=t;null===t.next&&(this.tail=t),this.length++}removeNode(e){let t=e.__ln;this.head&&(t.previous?(t.previous.next=t.next,t.next?t.next.previous=t.previous:this.tail=t.previous):(this.head=t.next,this.head?this.head.previous=null:this.tail=null),e.__ln&&delete e.__ln,this.length--)}}let e8=(e,t)=>`${e}@${t}`;class e6{constructor(){this.frozen=!1,this.locked=!1,this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.mapRemoves=[],this.movedMap={},this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.processMutations=e=>{e.forEach(this.processMutation),this.emit()},this.emit=()=>{if(this.frozen||this.locked)return;let e=[],t=new Set,r=new e5,n=e=>{let t=e,r=-2;for(;-2===r;)r=(t=t&&t.nextSibling)&&this.mirror.getId(t);return r},i=i=>{if(!i.parentNode||!eJ(i))return;let s=q(i.parentNode)?this.mirror.getId(eV(i)):this.mirror.getId(i.parentNode),o=n(i);if(-1===s||-1===o)return r.addNode(i);let a=eT(i,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskAllText:this.maskAllText,unblockSelector:this.unblockSelector,maskTextClass:this.maskTextClass,unmaskTextClass:this.unmaskTextClass,maskTextSelector:this.maskTextSelector,unmaskTextSelector:this.unmaskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskAttributeFn:this.maskAttributeFn,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:e=>{eH(e,this.mirror)&&!ez(e,this.blockClass,this.blockSelector,this.unblockSelector,!1)&&this.iframeManager.addIframe(e),e$(e,this.mirror)&&this.stylesheetManager.trackLinkElement(e),eq(i)&&this.shadowDomManager.addShadowRoot(i.shadowRoot,this.doc)},onIframeLoad:(e,t)=>{ez(e,this.blockClass,this.blockSelector,this.unblockSelector,!1)||(this.iframeManager.attachIframe(e,t),e.contentWindow&&this.canvasManager.addWindow(e.contentWindow),this.shadowDomManager.observeAttachShadow(e))},onStylesheetLoad:(e,t)=>{this.stylesheetManager.attachLinkElement(e,t)}});a&&(e.push({parentId:s,nextId:o,node:a}),t.add(a.id))};for(;this.mapRemoves.length;)this.mirror.removeNodeFromMap(this.mapRemoves.shift());for(let e of this.movedSet)(!e7(this.removes,e,this.mirror)||this.movedSet.has(e.parentNode))&&i(e);for(let e of this.addedSet)e9(this.droppedSet,e)||e7(this.removes,e,this.mirror)?e9(this.movedSet,e)?i(e):this.droppedSet.add(e):i(e);let s=null;for(;r.length;){let e=null;if(s){let t=this.mirror.getId(s.value.parentNode),r=n(s.value);-1!==t&&-1!==r&&(e=s)}if(!e){let t=r.tail;for(;t;){let r=t;if(t=t.previous,r){let t=this.mirror.getId(r.value.parentNode);if(-1===n(r.value))continue;if(-1!==t){e=r;break}{let t=r.value;if(t.parentNode&&t.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE){let n=t.parentNode.host;if(-1!==this.mirror.getId(n)){e=r;break}}}}}}if(!e){for(;r.head;)r.removeNode(r.head.value);break}s=e.previous,r.removeNode(e.value),i(e.value)}let o={texts:this.texts.map(e=>({id:this.mirror.getId(e.node),value:e.value})).filter(e=>!t.has(e.id)).filter(e=>this.mirror.has(e.id)),attributes:this.attributes.map(e=>{let{attributes:t}=e;if("string"==typeof t.style){let r=JSON.stringify(e.styleDiff),n=JSON.stringify(e._unchangedStyles);r.length<t.style.length&&(r+n).split("var(").length===t.style.split("var(").length&&(t.style=e.styleDiff)}return{id:this.mirror.getId(e.node),attributes:t}}).filter(e=>!t.has(e.id)).filter(e=>this.mirror.has(e.id)),removes:this.removes,adds:e};(o.texts.length||o.attributes.length||o.removes.length||o.adds.length)&&(this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.movedMap={},this.mutationCb(o))},this.processMutation=e=>{if(!eW(e.target,this.mirror))switch(e.type){case"characterData":{let t=e.target.textContent;ez(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||t===e.oldValue||this.texts.push({value:eC(e.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,this.maskAllText)&&t?this.maskTextFn?this.maskTextFn(t,eB(e.target)):t.replace(/[\S]/g,"*"):t,node:e.target});break}case"attributes":{let t=e.target,r=e.attributeName,n=e.target.getAttribute(r);if("value"===r){let r=et(t),i=t.tagName;n=er(t,i,r);let s=X({maskInputOptions:this.maskInputOptions,tagName:i,type:r});n=Y({isMasked:eC(e.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,s),element:t,value:n,maskInputFn:this.maskInputFn})}if(ez(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||n===e.oldValue)return;let i=this.attributeMap.get(e.target);if("IFRAME"===t.tagName&&"src"===r&&!this.keepIframeSrcFn(n)){if(e3(t))return;r="rr_src"}if(i||(i={node:e.target,attributes:{},styleDiff:{},_unchangedStyles:{}},this.attributes.push(i),this.attributeMap.set(e.target,i)),"type"===r&&"INPUT"===t.tagName&&"password"===(e.oldValue||"").toLowerCase()&&t.setAttribute("data-rr-is-password","true"),!ek(t.tagName,r)&&(i.attributes[r]=eb(this.doc,Q(t.tagName),Q(r),n,t,this.maskAttributeFn),"style"===r)){if(!this.unattachedDoc)try{this.unattachedDoc=document.implementation.createHTMLDocument()}catch(e){this.unattachedDoc=this.doc}let r=this.unattachedDoc.createElement("span");for(let n of(e.oldValue&&r.setAttribute("style",e.oldValue),Array.from(t.style))){let e=t.style.getPropertyValue(n),s=t.style.getPropertyPriority(n);e!==r.style.getPropertyValue(n)||s!==r.style.getPropertyPriority(n)?""===s?i.styleDiff[n]=e:i.styleDiff[n]=[e,s]:i._unchangedStyles[n]=[e,s]}for(let e of Array.from(r.style))""===t.style.getPropertyValue(e)&&(i.styleDiff[e]=!1)}break}case"childList":if(ez(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!0))return;e.addedNodes.forEach(t=>this.genAdds(t,e.target)),e.removedNodes.forEach(t=>{let r=this.mirror.getId(t),n=q(e.target)?this.mirror.getId(e.target.host):this.mirror.getId(e.target);ez(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||eW(t,this.mirror)||-1===this.mirror.getId(t)||(this.addedSet.has(t)?(e4(this.addedSet,t),this.droppedSet.add(t)):this.addedSet.has(e.target)&&-1===r||function e(t,r){if(q(t))return!1;let n=r.getId(t);return!r.has(n)||(!t.parentNode||t.parentNode.nodeType!==t.DOCUMENT_NODE)&&(!t.parentNode||e(t.parentNode,r))}(e.target,this.mirror)||(this.movedSet.has(t)&&this.movedMap[e8(r,n)]?e4(this.movedSet,t):this.removes.push({parentId:n,id:r,isShadow:!!(q(e.target)&&K(e.target))||void 0})),this.mapRemoves.push(t))})}},this.genAdds=(e,t)=>{if(!this.processedNodeManager.inOtherBuffer(e,this)&&!(this.addedSet.has(e)||this.movedSet.has(e))){if(this.mirror.hasNode(e)){if(eW(e,this.mirror))return;this.movedSet.add(e);let r=null;t&&this.mirror.hasNode(t)&&(r=this.mirror.getId(t)),r&&-1!==r&&(this.movedMap[e8(this.mirror.getId(e),r)]=!0)}else this.addedSet.add(e),this.droppedSet.delete(e);!ez(e,this.blockClass,this.blockSelector,this.unblockSelector,!1)&&(e.childNodes.forEach(e=>this.genAdds(e)),eq(e)&&e.shadowRoot.childNodes.forEach(t=>{this.processedNodeManager.add(t,this),this.genAdds(t,e)}))}}}init(e){["mutationCb","blockClass","blockSelector","unblockSelector","maskAllText","maskTextClass","unmaskTextClass","maskTextSelector","unmaskTextSelector","inlineStylesheet","maskInputOptions","maskAttributeFn","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach(t=>{this[t]=e[t]})}freeze(){this.frozen=!0,this.canvasManager.freeze()}unfreeze(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}isFrozen(){return this.frozen}lock(){this.locked=!0,this.canvasManager.lock()}unlock(){this.locked=!1,this.canvasManager.unlock(),this.emit()}reset(){this.shadowDomManager.reset(),this.canvasManager.reset()}}function e4(e,t){e.delete(t),t.childNodes.forEach(t=>e4(e,t))}function e7(e,t,r){return 0!==e.length&&function(e,t,r){let n=t.parentNode;for(;n;){let t=r.getId(n);if(e.some(e=>e.id===t))return!0;n=n.parentNode}return!1}(e,t,r)}function e9(e,t){return 0!==e.size&&function e(t,r){let{parentNode:n}=r;return!!n&&(!!t.has(n)||e(t,n))}(e,t)}let te=e=>s?(...t)=>{try{return e(...t)}catch(e){if(s&&!0===s(e))return()=>{};throw e}}:e,tt=[];function tr(e){try{if("composedPath"in e){let t=e.composedPath();if(t.length)return t[0]}else if("path"in e&&e.path.length)return e.path[0]}catch{}return e&&e.target}function tn(e,t){let r=new e6;tt.push(r),r.init(e);let n=window.MutationObserver||window.__rrMutationObserver,i=window?.Zone?.__symbol__?.("MutationObserver");i&&window[i]&&(n=window[i]);let s=new n(te(t=>{e.onMutation&&!1===e.onMutation(t)||r.processMutations.bind(r)(t)}));return s.observe(t,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),s}function ti({scrollCb:e,doc:t,mirror:r,blockClass:n,blockSelector:i,unblockSelector:s,sampling:o}){return ex("scroll",te(eN(te(o=>{let a=tr(o);if(!a||ez(a,n,i,s,!0))return;let l=r.getId(a);if(a===t&&t.defaultView){let r=eP(t.defaultView);e({id:l,x:r.left,y:r.top})}else e({id:l,x:a.scrollLeft,y:a.scrollTop})}),o.scroll||100)),t)}let ts=["INPUT","TEXTAREA","SELECT"],to=new WeakMap;function ta(e){var t=[];if(td("CSSGroupingRule")&&e.parentRule instanceof CSSGroupingRule||td("CSSMediaRule")&&e.parentRule instanceof CSSMediaRule||td("CSSSupportsRule")&&e.parentRule instanceof CSSSupportsRule||td("CSSConditionRule")&&e.parentRule instanceof CSSConditionRule){let r=Array.from(e.parentRule.cssRules).indexOf(e);t.unshift(r)}else if(e.parentStyleSheet){let r=Array.from(e.parentStyleSheet.cssRules).indexOf(e);t.unshift(r)}return t}function tl(e,t,r){let n,i;return e?(e.ownerNode?n=t.getId(e.ownerNode):i=r.getId(e),{styleId:i,id:n}):{}}function tc({mirror:e,stylesheetManager:t},r){let n=null;n="#document"===r.nodeName?e.getId(r):e.getId(r.host);let i="#document"===r.nodeName?r.defaultView?.Document:r.ownerDocument?.defaultView?.ShadowRoot,s=i?.prototype?Object.getOwnPropertyDescriptor(i?.prototype,"adoptedStyleSheets"):void 0;return null!==n&&-1!==n&&i&&s?(Object.defineProperty(r,"adoptedStyleSheets",{configurable:s.configurable,enumerable:s.enumerable,get(){return s.get?.call(this)},set(e){let r=s.set?.call(this,e);if(null!==n&&-1!==n)try{t.adoptStyleSheets(e,n)}catch(e){}return r}}),te(()=>{Object.defineProperty(r,"adoptedStyleSheets",{configurable:s.configurable,enumerable:s.enumerable,get:s.get,set:s.set})})):()=>{}}function tu(e,t={}){let r,n=e.doc.defaultView;if(!n)return()=>{};e.recordDOM&&(r=tn(e,e.doc));let i=function({mousemoveCb:e,sampling:t,doc:r,mirror:n}){let i;if(!1===t.mousemove)return()=>{};let s="number"==typeof t.mousemove?t.mousemove:50,o="number"==typeof t.mousemoveCallback?t.mousemoveCallback:500,a=[],l=eN(te(t=>{let r=Date.now()-i;e(a.map(e=>(e.timeOffset-=r,e)),t),a=[],i=null}),o),c=te(eN(te(e=>{let t=tr(e),{clientX:r,clientY:s}=ej(e)?e.changedTouches[0]:e;i||(i=eL()),a.push({x:r,y:s,id:n.getId(t),timeOffset:eL()-i}),l("undefined"!=typeof DragEvent&&e instanceof DragEvent?eZ.Drag:e instanceof MouseEvent?eZ.MouseMove:eZ.TouchMove)}),s,{trailing:!1})),u=[ex("mousemove",c,r),ex("touchmove",c,r),ex("drag",c,r)];return te(()=>{u.forEach(e=>e())})}(e),s=function({mouseInteractionCb:e,doc:t,mirror:r,blockClass:n,blockSelector:i,unblockSelector:s,sampling:o}){if(!1===o.mouseInteraction)return()=>{};let a=!0===o.mouseInteraction||void 0===o.mouseInteraction?{}:o.mouseInteraction,l=[],c=null,u=t=>o=>{let a=tr(o);if(ez(a,n,i,s,!0))return;let l=null,u=t;if("pointerType"in o){switch(o.pointerType){case"mouse":l=e1.Mouse;break;case"touch":l=e1.Touch;break;case"pen":l=e1.Pen}l===e1.Touch?e0[t]===e0.MouseDown?u="TouchStart":e0[t]===e0.MouseUp&&(u="TouchEnd"):e1.Pen}else ej(o)&&(l=e1.Touch);null!==l?(c=l,(u.startsWith("Touch")&&l===e1.Touch||u.startsWith("Mouse")&&l===e1.Mouse)&&(l=null)):e0[t]===e0.Click&&(l=c,c=null);let d=ej(o)?o.changedTouches[0]:o;if(!d)return;let h=r.getId(a),{clientX:p,clientY:f}=d;te(e)({type:e0[u],id:h,x:p,y:f,...null!==l&&{pointerType:l}})};return Object.keys(e0).filter(e=>Number.isNaN(Number(e))&&!e.endsWith("_Departed")&&!1!==a[e]).forEach(e=>{let r=Q(e),n=u(e);if(window.PointerEvent)switch(e0[e]){case e0.MouseDown:case e0.MouseUp:r=r.replace("mouse","pointer");break;case e0.TouchStart:case e0.TouchEnd:return}l.push(ex(r,n,t))}),te(()=>{l.forEach(e=>e())})}(e),o=ti(e),a=function({viewportResizeCb:e},{win:t}){let r=-1,n=-1;return ex("resize",te(eN(te(()=>{let t=eF(),i=eU();(r!==t||n!==i)&&(e({width:Number(i),height:Number(t)}),r=t,n=i)}),200)),t)}(e,{win:n}),l=function({inputCb:e,doc:t,mirror:r,blockClass:n,blockSelector:i,unblockSelector:s,ignoreClass:o,ignoreSelector:a,maskInputOptions:l,maskInputFn:c,sampling:u,userTriggeredOnInput:d,maskTextClass:h,unmaskTextClass:p,maskTextSelector:f,unmaskTextSelector:m}){function y(e){let r=tr(e),u=e.isTrusted,y=r&&Z(r.tagName);if("OPTION"===y&&(r=r.parentElement),!r||!y||0>ts.indexOf(y)||ez(r,n,i,s,!0))return;let v=r;if(v.classList.contains(o)||a&&v.matches(a))return;let S=et(r),_=er(v,y,S),w=!1,b=X({maskInputOptions:l,tagName:y,type:S}),k=eC(r,h,f,p,m,b);("radio"===S||"checkbox"===S)&&(w=r.checked),_=Y({isMasked:k,element:r,value:_,maskInputFn:c}),g(r,d?{text:_,isChecked:w,userTriggered:u}:{text:_,isChecked:w});let E=r.name;"radio"===S&&E&&w&&t.querySelectorAll(`input[type="radio"][name="${E}"]`).forEach(e=>{if(e!==r){let t=Y({isMasked:k,element:e,value:er(e,y,S),maskInputFn:c});g(e,d?{text:t,isChecked:!w,userTriggered:!1}:{text:t,isChecked:!w})}})}function g(t,n){let i=to.get(t);if(!i||i.text!==n.text||i.isChecked!==n.isChecked){to.set(t,n);let i=r.getId(t);te(e)({...n,id:i})}}let v=("last"===u.input?["change"]:["input","change"]).map(e=>ex(e,te(y),t)),S=t.defaultView;if(!S)return()=>{v.forEach(e=>e())};let _=S.Object.getOwnPropertyDescriptor(S.HTMLInputElement.prototype,"value"),w=[[S.HTMLInputElement.prototype,"value"],[S.HTMLInputElement.prototype,"checked"],[S.HTMLSelectElement.prototype,"value"],[S.HTMLTextAreaElement.prototype,"value"],[S.HTMLSelectElement.prototype,"selectedIndex"],[S.HTMLOptionElement.prototype,"selected"]];return _&&_.set&&v.push(...w.map(e=>(function e(t,r,n,i,s=window){let o=s.Object.getOwnPropertyDescriptor(t,r);return s.Object.defineProperty(t,r,i?n:{set(e){eY(()=>{n.set.call(this,e)},0),o&&o.set&&o.set.call(this,e)}}),()=>e(t,r,o||{},!0)})(e[0],e[1],{set(){te(y)({target:this,isTrusted:!1})}},!1,S))),te(()=>{v.forEach(e=>e())})}(e),c=function({mediaInteractionCb:e,blockClass:t,blockSelector:r,unblockSelector:n,mirror:i,sampling:s,doc:o}){let a=te(o=>eN(te(s=>{let a=tr(s);if(!a||ez(a,t,r,n,!0))return;let{currentTime:l,volume:c,muted:u,playbackRate:d}=a;e({type:o,id:i.getId(a),currentTime:l,volume:c,muted:u,playbackRate:d})}),s.media||500)),l=[ex("play",a(e2.Play),o),ex("pause",a(e2.Pause),o),ex("seeked",a(e2.Seeked),o),ex("volumechange",a(e2.VolumeChange),o),ex("ratechange",a(e2.RateChange),o)];return te(()=>{l.forEach(e=>e())})}(e),u=()=>{},d=()=>{},h=()=>{},p=()=>{};e.recordDOM&&(u=function({styleSheetRuleCb:e,mirror:t,stylesheetManager:r},{win:n}){let i,s;if(!n.CSSStyleSheet||!n.CSSStyleSheet.prototype)return()=>{};let o=n.CSSStyleSheet.prototype.insertRule;n.CSSStyleSheet.prototype.insertRule=new Proxy(o,{apply:te((n,i,s)=>{let[o,a]=s,{id:l,styleId:c}=tl(i,t,r.styleMirror);return(l&&-1!==l||c&&-1!==c)&&e({id:l,styleId:c,adds:[{rule:o,index:a}]}),n.apply(i,s)})});let a=n.CSSStyleSheet.prototype.deleteRule;n.CSSStyleSheet.prototype.deleteRule=new Proxy(a,{apply:te((n,i,s)=>{let[o]=s,{id:a,styleId:l}=tl(i,t,r.styleMirror);return(a&&-1!==a||l&&-1!==l)&&e({id:a,styleId:l,removes:[{index:o}]}),n.apply(i,s)})}),n.CSSStyleSheet.prototype.replace&&(i=n.CSSStyleSheet.prototype.replace,n.CSSStyleSheet.prototype.replace=new Proxy(i,{apply:te((n,i,s)=>{let[o]=s,{id:a,styleId:l}=tl(i,t,r.styleMirror);return(a&&-1!==a||l&&-1!==l)&&e({id:a,styleId:l,replace:o}),n.apply(i,s)})})),n.CSSStyleSheet.prototype.replaceSync&&(s=n.CSSStyleSheet.prototype.replaceSync,n.CSSStyleSheet.prototype.replaceSync=new Proxy(s,{apply:te((n,i,s)=>{let[o]=s,{id:a,styleId:l}=tl(i,t,r.styleMirror);return(a&&-1!==a||l&&-1!==l)&&e({id:a,styleId:l,replaceSync:o}),n.apply(i,s)})}));let l={};th("CSSGroupingRule")?l.CSSGroupingRule=n.CSSGroupingRule:(th("CSSMediaRule")&&(l.CSSMediaRule=n.CSSMediaRule),th("CSSConditionRule")&&(l.CSSConditionRule=n.CSSConditionRule),th("CSSSupportsRule")&&(l.CSSSupportsRule=n.CSSSupportsRule));let c={};return Object.entries(l).forEach(([n,i])=>{c[n]={insertRule:i.prototype.insertRule,deleteRule:i.prototype.deleteRule},i.prototype.insertRule=new Proxy(c[n].insertRule,{apply:te((n,i,s)=>{let[o,a]=s,{id:l,styleId:c}=tl(i.parentStyleSheet,t,r.styleMirror);return(l&&-1!==l||c&&-1!==c)&&e({id:l,styleId:c,adds:[{rule:o,index:[...ta(i),a||0]}]}),n.apply(i,s)})}),i.prototype.deleteRule=new Proxy(c[n].deleteRule,{apply:te((n,i,s)=>{let[o]=s,{id:a,styleId:l}=tl(i.parentStyleSheet,t,r.styleMirror);return(a&&-1!==a||l&&-1!==l)&&e({id:a,styleId:l,removes:[{index:[...ta(i),o]}]}),n.apply(i,s)})})}),te(()=>{n.CSSStyleSheet.prototype.insertRule=o,n.CSSStyleSheet.prototype.deleteRule=a,i&&(n.CSSStyleSheet.prototype.replace=i),s&&(n.CSSStyleSheet.prototype.replaceSync=s),Object.entries(l).forEach(([e,t])=>{t.prototype.insertRule=c[e].insertRule,t.prototype.deleteRule=c[e].deleteRule})})}(e,{win:n}),d=tc(e,e.doc),h=function({styleDeclarationCb:e,mirror:t,ignoreCSSAttributes:r,stylesheetManager:n},{win:i}){let s=i.CSSStyleDeclaration.prototype.setProperty;i.CSSStyleDeclaration.prototype.setProperty=new Proxy(s,{apply:te((i,o,a)=>{let[l,c,u]=a;if(r.has(l))return s.apply(o,[l,c,u]);let{id:d,styleId:h}=tl(o.parentRule?.parentStyleSheet,t,n.styleMirror);return(d&&-1!==d||h&&-1!==h)&&e({id:d,styleId:h,set:{property:l,value:c,priority:u},index:ta(o.parentRule)}),i.apply(o,a)})});let o=i.CSSStyleDeclaration.prototype.removeProperty;return i.CSSStyleDeclaration.prototype.removeProperty=new Proxy(o,{apply:te((i,s,a)=>{let[l]=a;if(r.has(l))return o.apply(s,[l]);let{id:c,styleId:u}=tl(s.parentRule?.parentStyleSheet,t,n.styleMirror);return(c&&-1!==c||u&&-1!==u)&&e({id:c,styleId:u,remove:{property:l},index:ta(s.parentRule)}),i.apply(s,a)})}),te(()=>{i.CSSStyleDeclaration.prototype.setProperty=s,i.CSSStyleDeclaration.prototype.removeProperty=o})}(e,{win:n}),e.collectFonts&&(p=function({fontCb:e,doc:t}){let r=t.defaultView;if(!r)return()=>{};let n=[],i=new WeakMap,s=r.FontFace;r.FontFace=function(e,t,r){let n=new s(e,t,r);return i.set(n,{family:e,buffer:"string"!=typeof t,descriptors:r,fontSource:"string"==typeof t?t:JSON.stringify(Array.from(new Uint8Array(t)))}),n};let o=eA(t.fonts,"add",function(t){return function(r){return eY(te(()=>{let t=i.get(r);t&&(e(t),i.delete(r))}),0),t.apply(this,[r])}});return n.push(()=>{r.FontFace=s}),n.push(o),te(()=>{n.forEach(e=>e())})}(e)));let f=function(e){let{doc:t,mirror:r,blockClass:n,blockSelector:i,unblockSelector:s,selectionCb:o}=e,a=!0,l=te(()=>{let e=t.getSelection();if(!e||a&&e?.isCollapsed)return;a=e.isCollapsed||!1;let l=[],c=e.rangeCount||0;for(let t=0;t<c;t++){let{startContainer:o,startOffset:a,endContainer:c,endOffset:u}=e.getRangeAt(t);ez(o,n,i,s,!0)||ez(c,n,i,s,!0)||l.push({start:r.getId(o),startOffset:a,end:r.getId(c),endOffset:u})}o({ranges:l})});return l(),ex("selectionchange",l)}(e),m=function({doc:e,customElementCb:t}){let r=e.defaultView;return r&&r.customElements?eA(r.customElements,"define",function(e){return function(r,n,i){try{t({define:{name:r}})}catch(e){}return e.apply(this,[r,n,i])}}):()=>{}}(e),y=[];for(let t of e.plugins)y.push(t.observer(t.callback,n,t.options));return te(()=>{tt.forEach(e=>e.reset()),r?.disconnect(),i(),s(),o(),a(),l(),c(),u(),d(),h(),p(),f(),m(),y.forEach(e=>e())})}function td(e){return void 0!==window[e]}function th(e){return!!(void 0!==window[e]&&window[e].prototype&&"insertRule"in window[e].prototype&&"deleteRule"in window[e].prototype)}class tp{constructor(e){this.generateIdFn=e,this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap}getId(e,t,r,n){let i=r||this.getIdToRemoteIdMap(e),s=n||this.getRemoteIdToIdMap(e),o=i.get(t);return o||(o=this.generateIdFn(),i.set(t,o),s.set(o,t)),o}getIds(e,t){let r=this.getIdToRemoteIdMap(e),n=this.getRemoteIdToIdMap(e);return t.map(t=>this.getId(e,t,r,n))}getRemoteId(e,t,r){let n=r||this.getRemoteIdToIdMap(e);if("number"!=typeof t)return t;let i=n.get(t);return i||-1}getRemoteIds(e,t){let r=this.getRemoteIdToIdMap(e);return t.map(t=>this.getRemoteId(e,t,r))}reset(e){if(!e){this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap;return}this.iframeIdToRemoteIdMap.delete(e),this.iframeRemoteIdToIdMap.delete(e)}getIdToRemoteIdMap(e){let t=this.iframeIdToRemoteIdMap.get(e);return t||(t=new Map,this.iframeIdToRemoteIdMap.set(e,t)),t}getRemoteIdToIdMap(e){let t=this.iframeRemoteIdToIdMap.get(e);return t||(t=new Map,this.iframeRemoteIdToIdMap.set(e,t)),t}}class tf{constructor(){this.crossOriginIframeMirror=new tp(ed),this.crossOriginIframeRootIdMap=new WeakMap}addIframe(){}addLoadListener(){}attachIframe(){}}class tm{constructor(e){this.iframes=new WeakMap,this.crossOriginIframeMap=new WeakMap,this.crossOriginIframeMirror=new tp(ed),this.crossOriginIframeRootIdMap=new WeakMap,this.mutationCb=e.mutationCb,this.wrappedEmit=e.wrappedEmit,this.stylesheetManager=e.stylesheetManager,this.recordCrossOriginIframes=e.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new tp(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=e.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}addIframe(e){this.iframes.set(e,!0),e.contentWindow&&this.crossOriginIframeMap.set(e.contentWindow,e)}addLoadListener(e){this.loadListener=e}attachIframe(e,t){this.mutationCb({adds:[{parentId:this.mirror.getId(e),nextId:null,node:t}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),this.recordCrossOriginIframes&&e.contentWindow?.addEventListener("message",this.handleMessage.bind(this)),this.loadListener?.(e);let r=e3(e);r&&r.adoptedStyleSheets&&r.adoptedStyleSheets.length>0&&this.stylesheetManager.adoptStyleSheets(r.adoptedStyleSheets,this.mirror.getId(r))}handleMessage(e){if("rrweb"!==e.data.type||e.origin!==e.data.origin||!e.source)return;let t=this.crossOriginIframeMap.get(e.source);if(!t)return;let r=this.transformCrossOriginEvent(t,e.data.event);r&&this.wrappedEmit(r,e.data.isCheckout)}transformCrossOriginEvent(e,t){switch(t.type){case eQ.FullSnapshot:{this.crossOriginIframeMirror.reset(e),this.crossOriginIframeStyleMirror.reset(e),this.replaceIdOnNode(t.data.node,e);let r=t.data.node.id;return this.crossOriginIframeRootIdMap.set(e,r),this.patchRootIdOnNode(t.data.node,r),{timestamp:t.timestamp,type:eQ.IncrementalSnapshot,data:{source:eZ.Mutation,adds:[{parentId:this.mirror.getId(e),nextId:null,node:t.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}}}case eQ.Meta:case eQ.Load:case eQ.DomContentLoaded:break;case eQ.Plugin:return t;case eQ.Custom:return this.replaceIds(t.data.payload,e,["id","parentId","previousId","nextId"]),t;case eQ.IncrementalSnapshot:switch(t.data.source){case eZ.Mutation:return t.data.adds.forEach(t=>{this.replaceIds(t,e,["parentId","nextId","previousId"]),this.replaceIdOnNode(t.node,e);let r=this.crossOriginIframeRootIdMap.get(e);r&&this.patchRootIdOnNode(t.node,r)}),t.data.removes.forEach(t=>{this.replaceIds(t,e,["parentId","id"])}),t.data.attributes.forEach(t=>{this.replaceIds(t,e,["id"])}),t.data.texts.forEach(t=>{this.replaceIds(t,e,["id"])}),t;case eZ.Drag:case eZ.TouchMove:case eZ.MouseMove:return t.data.positions.forEach(t=>{this.replaceIds(t,e,["id"])}),t;case eZ.ViewportResize:return!1;case eZ.MediaInteraction:case eZ.MouseInteraction:case eZ.Scroll:case eZ.CanvasMutation:case eZ.Input:return this.replaceIds(t.data,e,["id"]),t;case eZ.StyleSheetRule:case eZ.StyleDeclaration:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleId"]),t;case eZ.Font:return t;case eZ.Selection:return t.data.ranges.forEach(t=>{this.replaceIds(t,e,["start","end"])}),t;case eZ.AdoptedStyleSheet:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleIds"]),t.data.styles?.forEach(t=>{this.replaceStyleIds(t,e,["styleId"])}),t}}return!1}replace(e,t,r,n){for(let i of n)(Array.isArray(t[i])||"number"==typeof t[i])&&(Array.isArray(t[i])?t[i]=e.getIds(r,t[i]):t[i]=e.getId(r,t[i]));return t}replaceIds(e,t,r){return this.replace(this.crossOriginIframeMirror,e,t,r)}replaceStyleIds(e,t,r){return this.replace(this.crossOriginIframeStyleMirror,e,t,r)}replaceIdOnNode(e,t){this.replaceIds(e,t,["id","rootId"]),"childNodes"in e&&e.childNodes.forEach(e=>{this.replaceIdOnNode(e,t)})}patchRootIdOnNode(e,t){e.type===$.Document||e.rootId||(e.rootId=t),"childNodes"in e&&e.childNodes.forEach(e=>{this.patchRootIdOnNode(e,t)})}}class ty{init(){}addShadowRoot(){}observeAttachShadow(){}reset(){}}class tg{constructor(e){this.shadowDoms=new WeakSet,this.restoreHandlers=[],this.mutationCb=e.mutationCb,this.scrollCb=e.scrollCb,this.bypassOptions=e.bypassOptions,this.mirror=e.mirror,this.init()}init(){this.reset(),this.patchAttachShadow(Element,document)}addShadowRoot(e,t){if(!K(e)||this.shadowDoms.has(e))return;this.shadowDoms.add(e),this.bypassOptions.canvasManager.addShadowRoot(e);let r=tn({...this.bypassOptions,doc:t,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this},e);this.restoreHandlers.push(()=>r.disconnect()),this.restoreHandlers.push(ti({...this.bypassOptions,scrollCb:this.scrollCb,doc:e,mirror:this.mirror})),eY(()=>{e.adoptedStyleSheets&&e.adoptedStyleSheets.length>0&&this.bypassOptions.stylesheetManager.adoptStyleSheets(e.adoptedStyleSheets,this.mirror.getId(e.host)),this.restoreHandlers.push(tc({mirror:this.mirror,stylesheetManager:this.bypassOptions.stylesheetManager},e))},0)}observeAttachShadow(e){let t=e3(e),r=function(e){try{return e.contentWindow}catch(e){}}(e);t&&r&&this.patchAttachShadow(r.Element,t)}patchAttachShadow(e,t){let r=this;this.restoreHandlers.push(eA(e.prototype,"attachShadow",function(e){return function(n){let i=e.call(this,n);return this.shadowRoot&&eJ(this)&&r.addShadowRoot(this.shadowRoot,t),i}}))}reset(){this.restoreHandlers.forEach(e=>{try{e()}catch(e){}}),this.restoreHandlers=[],this.shadowDoms=new WeakSet,this.bypassOptions.canvasManager.resetShadowRoots()}}for(var tv="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",tS="undefined"==typeof Uint8Array?[]:new Uint8Array(256),t_=0;t_<tv.length;t_++)tS[tv.charCodeAt(t_)]=t_;class tw{reset(){}freeze(){}unfreeze(){}lock(){}unlock(){}snapshot(){}addWindow(){}addShadowRoot(){}resetShadowRoots(){}}class tb{constructor(e){this.trackedLinkElements=new WeakSet,this.styleMirror=new eK,this.mutationCb=e.mutationCb,this.adoptedStyleSheetCb=e.adoptedStyleSheetCb}attachLinkElement(e,t){"_cssText"in t.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:t.id,attributes:t.attributes}]}),this.trackLinkElement(e)}trackLinkElement(e){this.trackedLinkElements.has(e)||(this.trackedLinkElements.add(e),this.trackStylesheetInLinkElement(e))}adoptStyleSheets(e,t){if(0===e.length)return;let r={id:t,styleIds:[]},n=[];for(let t of e){let e;this.styleMirror.has(t)?e=this.styleMirror.getId(t):(e=this.styleMirror.add(t),n.push({styleId:e,rules:Array.from(t.rules||CSSRule,(e,t)=>({rule:J(e),index:t}))})),r.styleIds.push(e)}n.length>0&&(r.styles=n),this.adoptedStyleSheetCb(r)}reset(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(e){}}class tk{constructor(){this.nodeMap=new WeakMap,this.active=!1}inOtherBuffer(e,t){let r=this.nodeMap.get(e);return r&&Array.from(r).some(e=>e!==t)}add(e,t){this.active||(this.active=!0,function(...e){eX("requestAnimationFrame")(...e)}(()=>{this.nodeMap=new WeakMap,this.active=!1})),this.nodeMap.set(e,(this.nodeMap.get(e)||new Set).add(t))}destroy(){}}try{if(2!==Array.from([1],e=>2*e)[0]){let e=document.createElement("iframe");document.body.appendChild(e),Array.from=e.contentWindow?.Array.from||Array.from,document.body.removeChild(e)}}catch(e){console.debug("Unable to override Array.from",e)}let tE=new G;function tM(e={}){let t,{emit:r,checkoutEveryNms:n,checkoutEveryNth:i,blockClass:l="rr-block",blockSelector:c=null,unblockSelector:u=null,ignoreClass:d="rr-ignore",ignoreSelector:h=null,maskAllText:p=!1,maskTextClass:f="rr-mask",unmaskTextClass:m=null,maskTextSelector:y=null,unmaskTextSelector:g=null,inlineStylesheet:v=!0,maskAllInputs:S,maskInputOptions:_,slimDOMOptions:w,maskAttributeFn:b,maskInputFn:k,maskTextFn:E,maxCanvasSize:M=null,packFn:I,sampling:C={},dataURLOptions:R={},mousemoveWait:T,recordDOM:x=!0,recordCanvas:D=!1,recordCrossOriginIframes:O=!1,recordAfter:N="DOMContentLoaded"===e.recordAfter?e.recordAfter:"load",userTriggeredOnInput:A=!1,collectFonts:L=!1,inlineImages:P=!1,plugins:F,keepIframeSrcFn:U=()=>!1,ignoreCSSAttributes:B=new Set([]),errorHandler:z,onMutation:W,getCanvasManager:j}=e;s=z;let H=!O||window.parent===window,$=!1;if(!H)try{window.parent.document&&($=!1)}catch(e){$=!0}if(H&&!r)throw Error("emit function is required");if(!H&&!$)return()=>{};void 0!==T&&void 0===C.mousemove&&(C.mousemove=T),tE.reset();let q=!0===S?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,radio:!0,checkbox:!0}:void 0!==_?_:{},K=!0===w||"all"===w?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:"all"===w,headMetaDescKeywords:"all"===w}:w||{};!function(e=window){"NodeList"in e&&!e.NodeList.prototype.forEach&&(e.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in e&&!e.DOMTokenList.prototype.forEach&&(e.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=(...e)=>{let t=e[0];if(!(0 in e))throw TypeError("1 argument is required");do if(this===t)return!0;while(t=t&&t.parentNode);return!1})}();let V=0,J=e=>{for(let t of F||[])t.eventProcessor&&(e=t.eventProcessor(e));return I&&!$&&(e=I(e)),e};o=(e,s)=>{if(e.timestamp=eL(),tt[0]?.isFrozen()&&e.type!==eQ.FullSnapshot&&(e.type!==eQ.IncrementalSnapshot||e.data.source!==eZ.Mutation)&&tt.forEach(e=>e.unfreeze()),H)r?.(J(e),s);else if($){let t={type:"rrweb",event:J(e),origin:window.location.origin,isCheckout:s};window.parent.postMessage(t,"*")}if(e.type===eQ.FullSnapshot)t=e,V=0;else if(e.type===eQ.IncrementalSnapshot){if(e.data.source===eZ.Mutation&&e.data.isAttachIframe)return;V++;let r=i&&V>=i,s=n&&t&&e.timestamp-t.timestamp>n;(r||s)&&ei(!0)}};let X=e=>{o({type:eQ.IncrementalSnapshot,data:{source:eZ.Mutation,...e}})},Y=e=>o({type:eQ.IncrementalSnapshot,data:{source:eZ.Scroll,...e}}),Q=e=>o({type:eQ.IncrementalSnapshot,data:{source:eZ.CanvasMutation,...e}}),Z=new tb({mutationCb:X,adoptedStyleSheetCb:e=>o({type:eQ.IncrementalSnapshot,data:{source:eZ.AdoptedStyleSheet,...e}})}),ee="boolean"==typeof __RRWEB_EXCLUDE_IFRAME__&&__RRWEB_EXCLUDE_IFRAME__?new tf:new tm({mirror:tE,mutationCb:X,stylesheetManager:Z,recordCrossOriginIframes:O,wrappedEmit:o});for(let e of F||[])e.getMirror&&e.getMirror({nodeMirror:tE,crossOriginIframeMirror:ee.crossOriginIframeMirror,crossOriginIframeStyleMirror:ee.crossOriginIframeStyleMirror});let et=new tk,er=function(e,t){try{return e?e(t):new tw}catch{return console.warn("Unable to initialize CanvasManager"),new tw}}(j,{mirror:tE,win:window,mutationCb:e=>o({type:eQ.IncrementalSnapshot,data:{source:eZ.CanvasMutation,...e}}),recordCanvas:D,blockClass:l,blockSelector:c,unblockSelector:u,maxCanvasSize:M,sampling:C.canvas,dataURLOptions:R,errorHandler:z}),en="boolean"==typeof __RRWEB_EXCLUDE_SHADOW_DOM__&&__RRWEB_EXCLUDE_SHADOW_DOM__?new ty:new tg({mutationCb:X,scrollCb:Y,bypassOptions:{onMutation:W,blockClass:l,blockSelector:c,unblockSelector:u,maskAllText:p,maskTextClass:f,unmaskTextClass:m,maskTextSelector:y,unmaskTextSelector:g,inlineStylesheet:v,maskInputOptions:q,dataURLOptions:R,maskAttributeFn:b,maskTextFn:E,maskInputFn:k,recordCanvas:D,inlineImages:P,sampling:C,slimDOMOptions:K,iframeManager:ee,stylesheetManager:Z,canvasManager:er,keepIframeSrcFn:U,processedNodeManager:et},mirror:tE}),ei=(e=!1)=>{if(!x)return;o({type:eQ.Meta,data:{href:window.location.href,width:eU(),height:eF()}},e),Z.reset(),en.init(),tt.forEach(e=>e.lock());let t=function(e,t){let{mirror:r=new G,blockClass:n="rr-block",blockSelector:i=null,unblockSelector:s=null,maskAllText:o=!1,maskTextClass:a="rr-mask",unmaskTextClass:l=null,maskTextSelector:c=null,unmaskTextSelector:u=null,inlineStylesheet:d=!0,inlineImages:h=!1,recordCanvas:p=!1,maskAllInputs:f=!1,maskAttributeFn:m,maskTextFn:y,maskInputFn:g,slimDOM:v=!1,dataURLOptions:S,preserveWhiteSpace:_,onSerialize:w,onIframeLoad:b,iframeLoadTimeout:k,onStylesheetLoad:E,stylesheetLoadTimeout:M,keepIframeSrcFn:I=()=>!1}=t||{};return eT(e,{doc:e,mirror:r,blockClass:n,blockSelector:i,unblockSelector:s,maskAllText:o,maskTextClass:a,unmaskTextClass:l,maskTextSelector:c,unmaskTextSelector:u,skipChild:!1,inlineStylesheet:d,maskInputOptions:!0===f?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0}:!1===f?{}:f,maskAttributeFn:m,maskTextFn:y,maskInputFn:g,slimDOMOptions:!0===v||"all"===v?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:"all"===v,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:!1===v?{}:v,dataURLOptions:S,inlineImages:h,recordCanvas:p,preserveWhiteSpace:_,onSerialize:w,onIframeLoad:b,iframeLoadTimeout:k,onStylesheetLoad:E,stylesheetLoadTimeout:M,keepIframeSrcFn:I,newlyAddedElement:!1})}(document,{mirror:tE,blockClass:l,blockSelector:c,unblockSelector:u,maskAllText:p,maskTextClass:f,unmaskTextClass:m,maskTextSelector:y,unmaskTextSelector:g,inlineStylesheet:v,maskAllInputs:q,maskAttributeFn:b,maskInputFn:k,maskTextFn:E,slimDOM:K,dataURLOptions:R,recordCanvas:D,inlineImages:P,onSerialize:e=>{eH(e,tE)&&ee.addIframe(e),e$(e,tE)&&Z.trackLinkElement(e),eq(e)&&en.addShadowRoot(e.shadowRoot,document)},onIframeLoad:(e,t)=>{ee.attachIframe(e,t),e.contentWindow&&er.addWindow(e.contentWindow),en.observeAttachShadow(e)},onStylesheetLoad:(e,t)=>{Z.attachLinkElement(e,t)},keepIframeSrcFn:U});if(!t)return console.warn("Failed to snapshot the document");o({type:eQ.FullSnapshot,data:{node:t,initialOffset:eP(window)}}),tt.forEach(e=>e.unlock()),document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0&&Z.adoptStyleSheets(document.adoptedStyleSheets,tE.getId(document))};a=ei;try{let e=[],t=e=>te(tu)({onMutation:W,mutationCb:X,mousemoveCb:(e,t)=>o({type:eQ.IncrementalSnapshot,data:{source:t,positions:e}}),mouseInteractionCb:e=>o({type:eQ.IncrementalSnapshot,data:{source:eZ.MouseInteraction,...e}}),scrollCb:Y,viewportResizeCb:e=>o({type:eQ.IncrementalSnapshot,data:{source:eZ.ViewportResize,...e}}),inputCb:e=>o({type:eQ.IncrementalSnapshot,data:{source:eZ.Input,...e}}),mediaInteractionCb:e=>o({type:eQ.IncrementalSnapshot,data:{source:eZ.MediaInteraction,...e}}),styleSheetRuleCb:e=>o({type:eQ.IncrementalSnapshot,data:{source:eZ.StyleSheetRule,...e}}),styleDeclarationCb:e=>o({type:eQ.IncrementalSnapshot,data:{source:eZ.StyleDeclaration,...e}}),canvasMutationCb:Q,fontCb:e=>o({type:eQ.IncrementalSnapshot,data:{source:eZ.Font,...e}}),selectionCb:e=>{o({type:eQ.IncrementalSnapshot,data:{source:eZ.Selection,...e}})},customElementCb:e=>{o({type:eQ.IncrementalSnapshot,data:{source:eZ.CustomElement,...e}})},blockClass:l,ignoreClass:d,ignoreSelector:h,maskAllText:p,maskTextClass:f,unmaskTextClass:m,maskTextSelector:y,unmaskTextSelector:g,maskInputOptions:q,inlineStylesheet:v,sampling:C,recordDOM:x,recordCanvas:D,inlineImages:P,userTriggeredOnInput:A,collectFonts:L,doc:e,maskAttributeFn:b,maskInputFn:k,maskTextFn:E,keepIframeSrcFn:U,blockSelector:c,unblockSelector:u,slimDOMOptions:K,dataURLOptions:R,mirror:tE,iframeManager:ee,stylesheetManager:Z,shadowDomManager:en,processedNodeManager:et,canvasManager:er,ignoreCSSAttributes:B,plugins:F?.filter(e=>e.observer)?.map(e=>({observer:e.observer,options:e.options,callback:t=>o({type:eQ.Plugin,data:{plugin:e.name,payload:t}})}))||[]},{});ee.addLoadListener(r=>{try{e.push(t(r.contentDocument))}catch(e){console.warn(e)}});let r=()=>{ei(),e.push(t(document))};return"interactive"===document.readyState||"complete"===document.readyState?r():(e.push(ex("DOMContentLoaded",()=>{o({type:eQ.DomContentLoaded,data:{}}),"DOMContentLoaded"===N&&r()})),e.push(ex("load",()=>{o({type:eQ.Load,data:{}}),"load"===N&&r()},window))),()=>{e.forEach(e=>e()),et.destroy(),a=void 0,s=void 0}}catch(e){console.warn(e)}}function tI(e){return e>0x2540be3ff?e:1e3*e}function tC(e){return e>0x2540be3ff?e/1e3:e}function tR(e,t){"sentry.transaction"!==t.category&&(["ui.click","ui.input"].includes(t.category)?e.triggerUserActivity():e.checkAndHandleExpiredSession(),e.addUpdate(()=>(e.throttledAddEvent({type:eQ.Custom,timestamp:1e3*(t.timestamp||0),data:{tag:"breadcrumb",payload:(0,d.S8)(t,10,1e3)}}),"console"===t.category)))}function tT(e){return e.closest("button,a")||e}function tx(e){let t=tD(e);return t&&t instanceof Element?tT(t):t}function tD(e){var t;return"object"==typeof(t=e)&&t&&"target"in t?e.target:e}tM.mirror=tE,tM.takeFullSnapshot=function(e){if(!a)throw Error("please take full snapshot after start recording");a(e)},!function(e){e[e.NotStarted=0]="NotStarted",e[e.Running=1]="Running",e[e.Stopped=2]="Stopped"}(c||(c={}));let tO=new Set([eZ.Mutation,eZ.StyleSheetRule,eZ.StyleDeclaration,eZ.AdoptedStyleSheet,eZ.CanvasMutation,eZ.Selection,eZ.MediaInteraction]);class tN{constructor(e,t,r=tR){this._lastMutation=0,this._lastScroll=0,this._clicks=[],this._timeout=t.timeout/1e3,this._threshold=t.threshold/1e3,this._scrollTimeout=t.scrollTimeout/1e3,this._replay=e,this._ignoreSelector=t.ignoreSelector,this._addBreadcrumbEvent=r}addListeners(){var e;let t=(e=()=>{this._lastMutation=tL()},l||(l=[],(0,h.GS)(U,"open",function(e){return function(...t){if(l)try{l.forEach(e=>e())}catch{}return e.apply(U,t)}})),l.push(e),()=>{let t=l?l.indexOf(e):-1;t>-1&&l.splice(t,1)});this._teardown=()=>{t(),this._clicks=[],this._lastMutation=0,this._lastScroll=0}}removeListeners(){this._teardown&&this._teardown(),this._checkClickTimeout&&clearTimeout(this._checkClickTimeout)}handleClick(e,t){var r,n,i;if(r=t,n=this._ignoreSelector,!tA.includes(r.tagName)||"INPUT"===r.tagName&&!["submit","button"].includes(r.getAttribute("type")||"")||"A"===r.tagName&&(r.hasAttribute("download")||r.hasAttribute("target")&&"_self"!==r.getAttribute("target"))||n&&r.matches(n)||!((i=e).data&&"number"==typeof i.data.nodeId&&i.timestamp))return;let s={timestamp:tC(e.timestamp),clickBreadcrumb:e,clickCount:0,node:t};this._clicks.some(e=>e.node===s.node&&1>Math.abs(e.timestamp-s.timestamp))||(this._clicks.push(s),1===this._clicks.length&&this._scheduleCheckClicks())}registerMutation(e=Date.now()){this._lastMutation=tC(e)}registerScroll(e=Date.now()){this._lastScroll=tC(e)}registerClick(e){let t=tT(e);this._handleMultiClick(t)}_handleMultiClick(e){this._getClicks(e).forEach(e=>{e.clickCount++})}_getClicks(e){return this._clicks.filter(t=>t.node===e)}_checkClicks(){let e=[],t=tL();for(let r of(this._clicks.forEach(r=>{!r.mutationAfter&&this._lastMutation&&(r.mutationAfter=r.timestamp<=this._lastMutation?this._lastMutation-r.timestamp:void 0),!r.scrollAfter&&this._lastScroll&&(r.scrollAfter=r.timestamp<=this._lastScroll?this._lastScroll-r.timestamp:void 0),r.timestamp+this._timeout<=t&&e.push(r)}),e)){let e=this._clicks.indexOf(r);e>-1&&(this._generateBreadcrumbs(r),this._clicks.splice(e,1))}this._clicks.length&&this._scheduleCheckClicks()}_generateBreadcrumbs(e){let t=this._replay,r=e.scrollAfter&&e.scrollAfter<=this._scrollTimeout,n=e.mutationAfter&&e.mutationAfter<=this._threshold,{clickCount:i,clickBreadcrumb:s}=e;if(!r&&!n){let r=1e3*Math.min(e.mutationAfter||this._timeout,this._timeout),n=r<1e3*this._timeout?"mutation":"timeout",o={type:"default",message:s.message,timestamp:s.timestamp,category:"ui.slowClickDetected",data:{...s.data,url:U.location.href,route:t.getCurrentRoute(),timeAfterClickMs:r,endReason:n,clickCount:i||1}};this._addBreadcrumbEvent(t,o);return}if(i>1){let e={type:"default",message:s.message,timestamp:s.timestamp,category:"ui.multiClick",data:{...s.data,url:U.location.href,route:t.getCurrentRoute(),clickCount:i,metric:!0}};this._addBreadcrumbEvent(t,e)}}_scheduleCheckClicks(){this._checkClickTimeout&&clearTimeout(this._checkClickTimeout),this._checkClickTimeout=(0,O.wg)(()=>this._checkClicks(),1e3)}}let tA=["A","BUTTON","INPUT"];function tL(){return Date.now()/1e3}function tP(e){return{timestamp:Date.now()/1e3,type:"default",...e}}var tF=(e=>(e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment",e))(tF||{});let tU=new Set(["id","class","aria-label","role","name","alt","title","data-test-id","data-testid","disabled","aria-disabled","data-sentry-component"]),tB=e=>t=>{var r,n;if(!e.isEnabled())return;let i=function(e){let{target:t,message:r}=function(e){let t,r="click"===e.name,n=null;try{n=r?tx(e.event):tD(e.event),t=(0,p.Hd)(n,{maxStringLength:200})||"<unknown>"}catch{t="<unknown>"}return{target:n,message:t}}(e);return tP({category:`ui.${e.name}`,...tz(t,r)})}(t);if(!i)return;let s="click"===t.name,o=s?t.event:void 0;s&&e.clickDetector&&o?.target&&!o.altKey&&!o.metaKey&&!o.ctrlKey&&!o.shiftKey&&(r=e.clickDetector,n=tx(t.event),r.handleClick(i,n)),tR(e,i)};function tz(e,t){let r=tM.mirror.getId(e),n=r&&tM.mirror.getNode(r),i=n&&tM.mirror.getMeta(n),s=i&&i.type===tF.Element?i:null;return{message:t,data:s?{nodeId:r,node:{id:r,tagName:s.tagName,textContent:Array.from(s.childNodes).map(e=>e.type===tF.Text&&e.textContent).filter(Boolean).map(e=>e.trim()).join(""),attributes:function(e){let t={};for(let r in!e["data-sentry-component"]&&e["data-sentry-element"]&&(e["data-sentry-component"]=e["data-sentry-element"]),e)if(tU.has(r)){let n=r;("data-testid"===r||"data-test-id"===r)&&(n="testId"),t[n]=e[r]}return t}(s.attributes)}}:{}}}let tW={resource:function(e){let{entryType:t,initiatorType:r,name:n,responseEnd:i,startTime:s,decodedBodySize:o,encodedBodySize:a,responseStatus:l,transferSize:c}=e;return["fetch","xmlhttprequest"].includes(r)?null:{type:`${t}.${r}`,start:t$(s),end:t$(i),name:n,data:{size:c,statusCode:l,decodedBodySize:o,encodedBodySize:a}}},paint:function(e){let{duration:t,entryType:r,name:n,startTime:i}=e,s=t$(i);return{type:r,name:n,start:s,end:s+t,data:void 0}},navigation:function(e){let{entryType:t,name:r,decodedBodySize:n,duration:i,domComplete:s,encodedBodySize:o,domContentLoadedEventStart:a,domContentLoadedEventEnd:l,domInteractive:c,loadEventStart:u,loadEventEnd:d,redirectCount:h,startTime:p,transferSize:f,type:m}=e;return 0===i?null:{type:`${t}.${m}`,start:t$(p),end:t$(s),name:r,data:{size:f,decodedBodySize:n,encodedBodySize:o,duration:i,domInteractive:c,domContentLoadedEventStart:a,domContentLoadedEventEnd:l,loadEventStart:u,loadEventEnd:d,domComplete:s,redirectCount:h}}}};function tj(e,t){return({metric:r})=>void t.replayPerformanceEntries.push(e(r))}function tH(e){let t=tW[e.entryType];return t?t(e):null}function t$(e){return(((0,f.k3)()||U.performance.timeOrigin)+e)/1e3}function tq(e){let t=e.entries[e.entries.length-1];return tG(e,"largest-contentful-paint",t?.element?[t.element]:void 0)}function tK(e){let t=[],r=[];for(let n of e.entries)if(void 0!==n.sources){let e=[];for(let t of n.sources)if(t.node){r.push(t.node);let n=tM.mirror.getId(t.node);n&&e.push(n)}t.push({value:n.value,nodeIds:e.length?e:void 0})}return tG(e,"cumulative-layout-shift",r,t)}function tV(e){let t=e.entries[e.entries.length-1];return tG(e,"first-input-delay",t?.target?[t.target]:void 0)}function tJ(e){let t=e.entries[e.entries.length-1];return tG(e,"interaction-to-next-paint",t?.target?[t.target]:void 0)}function tG(e,t,r,n){let i=e.value,s=e.rating,o=t$(i);return{type:"web-vital",name:t,start:o,end:o,data:{value:i,size:i,rating:s,nodeIds:r?r.map(e=>tM.mirror.getId(e)):void 0,attributions:n}}}let tX=["log","warn","error"],tY=function(){let e=!1,t=!1,r={exception:()=>void 0,infoTick:()=>void 0,setConfig:e=>{e.captureExceptions,e.traceInternals}};return tX.forEach(e=>{r[e]=()=>void 0}),r}();class tQ extends Error{constructor(){super("Event buffer exceeded maximum size of 20000000.")}}class tZ{constructor(){this.events=[],this._totalSize=0,this.hasCheckout=!1,this.waitForCheckout=!1}get hasEvents(){return this.events.length>0}get type(){return"sync"}destroy(){this.events=[]}async addEvent(e){let t=JSON.stringify(e).length;if(this._totalSize+=t,this._totalSize>2e7)throw new tQ;this.events.push(e)}finish(){return new Promise(e=>{let t=this.events;this.clear(),e(JSON.stringify(t))})}clear(){this.events=[],this._totalSize=0,this.hasCheckout=!1}getEarliestTimestamp(){let e=this.events.map(e=>e.timestamp).sort()[0];return e?tI(e):null}}class t0{constructor(e){this._worker=e,this._id=0}ensureReady(){return this._ensureReadyPromise||(this._ensureReadyPromise=new Promise((e,t)=>{this._worker.addEventListener("message",({data:r})=>{r.success?e():t()},{once:!0}),this._worker.addEventListener("error",e=>{t(e)},{once:!0})})),this._ensureReadyPromise}destroy(){this._worker.terminate()}postMessage(e,t){let r=this._getAndIncrementId();return new Promise((n,i)=>{let s=({data:t})=>{if(t.method===e&&t.id===r){if(this._worker.removeEventListener("message",s),!t.success)return void i(Error("Error in compression worker"));n(t.response)}};this._worker.addEventListener("message",s),this._worker.postMessage({id:r,method:e,arg:t})})}_getAndIncrementId(){return this._id++}}class t1{constructor(e){this._worker=new t0(e),this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1,this.waitForCheckout=!1}get hasEvents(){return!!this._earliestTimestamp}get type(){return"worker"}ensureReady(){return this._worker.ensureReady()}destroy(){this._worker.destroy()}addEvent(e){let t=tI(e.timestamp);(!this._earliestTimestamp||t<this._earliestTimestamp)&&(this._earliestTimestamp=t);let r=JSON.stringify(e);return(this._totalSize+=r.length,this._totalSize>2e7)?Promise.reject(new tQ):this._sendEventToWorker(r)}finish(){return this._finishRequest()}clear(){this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1,this._worker.postMessage("clear").then(null,e=>{})}getEarliestTimestamp(){return this._earliestTimestamp}_sendEventToWorker(e){return this._worker.postMessage("addEvent",e)}async _finishRequest(){let e=await this._worker.postMessage("finish");return this._earliestTimestamp=null,this._totalSize=0,e}}class t2{constructor(e){this._fallback=new tZ,this._compression=new t1(e),this._used=this._fallback,this._ensureWorkerIsLoadedPromise=this._ensureWorkerIsLoaded()}get waitForCheckout(){return this._used.waitForCheckout}get type(){return this._used.type}get hasEvents(){return this._used.hasEvents}get hasCheckout(){return this._used.hasCheckout}set hasCheckout(e){this._used.hasCheckout=e}set waitForCheckout(e){this._used.waitForCheckout=e}destroy(){this._fallback.destroy(),this._compression.destroy()}clear(){return this._used.clear()}getEarliestTimestamp(){return this._used.getEarliestTimestamp()}addEvent(e){return this._used.addEvent(e)}async finish(){return await this.ensureWorkerIsLoaded(),this._used.finish()}ensureWorkerIsLoaded(){return this._ensureWorkerIsLoadedPromise}async _ensureWorkerIsLoaded(){try{await this._compression.ensureReady()}catch(e){return}await this._switchToCompressionWorker()}async _switchToCompressionWorker(){let{events:e,hasCheckout:t,waitForCheckout:r}=this._fallback,n=[];for(let t of e)n.push(this._compression.addEvent(t));this._compression.hasCheckout=t,this._compression.waitForCheckout=r,this._used=this._compression;try{await Promise.all(n),this._fallback.clear()}catch(e){}}}function t3(){try{return"sessionStorage"in U&&!!U.sessionStorage}catch{return!1}}function t5(e){return void 0!==e&&Math.random()<e}function t8(e){if(t3())try{U.sessionStorage.setItem(B,JSON.stringify(e))}catch{}}function t6(e){let t=Date.now(),r=e.id||(0,v.eJ)(),n=e.started||t,i=e.lastActivity||t,s=e.segmentId||0;return{id:r,started:n,lastActivity:i,segmentId:s,sampled:e.sampled,previousSessionId:e.previousSessionId}}function t4({sessionSampleRate:e,allowBuffering:t,stickySession:r=!1},{previousSessionId:n}={}){let i=t6({sampled:t5(e)?"session":!!t&&"buffer",previousSessionId:n});return r&&t8(i),i}function t7(e,t,r=+new Date){return null===e||void 0===t||t<0||0!==t&&e+t<=r}function t9(e,{maxReplayDuration:t,sessionIdleExpire:r,targetTime:n=Date.now()}){return t7(e.started,t,n)||t7(e.lastActivity,r,n)}function re(e,{sessionIdleExpire:t,maxReplayDuration:r}){return!!t9(e,{sessionIdleExpire:t,maxReplayDuration:r})&&("buffer"!==e.sampled||0!==e.segmentId)}function rt({sessionIdleExpire:e,maxReplayDuration:t,previousSessionId:r},n){let i=n.stickySession&&function(){if(!t3())return null;try{let e=U.sessionStorage.getItem(B);if(!e)return null;let t=JSON.parse(e);return t6(t)}catch{return null}}();return i?re(i,{sessionIdleExpire:e,maxReplayDuration:t})?t4(n,{previousSessionId:i.id}):i:t4(n,{previousSessionId:r})}function rr(e,t,r){return!!ri(e,t)&&(rn(e,t,r),!0)}async function rn(e,t,r){let{eventBuffer:n}=e;if(!n||n.waitForCheckout&&!r)return null;let i="buffer"===e.recordingMode;try{r&&i&&n.clear(),r&&(n.hasCheckout=!0,n.waitForCheckout=!1);let s=e.getOptions(),o=function(e,t){try{if("function"==typeof t&&e.type===eQ.Custom)return t(e)}catch(e){return null}return e}(t,s.beforeAddRecordingEvent);if(!o)return;return await n.addEvent(o)}catch(s){let t=s&&s instanceof tQ;if(t&&i)return n.clear(),n.waitForCheckout=!0,null;e.handleException(s),await e.stop({reason:t?"addEventSizeExceeded":"addEvent"});let r=(0,S.KU)();r&&r.recordDroppedEvent("internal_sdk_error","replay")}}function ri(e,t){if(!e.eventBuffer||e.isPaused()||!e.isEnabled())return!1;let r=tI(t.timestamp);return!(r+e.timeouts.sessionIdlePause<Date.now())&&!(r>e.getContext().initialTimestamp+e.getOptions().maxReplayDuration)}function rs(e){return"transaction"===e.type}function ro(e){return"feedback"===e.type}function ra(e){return!!e.category}function rl(){let e=(0,S.o5)().getPropagationContext().dsc;e&&delete e.replay_id;let t=(0,_.Bk)();if(t){let e=(0,w.k1)(t);delete e.replay_id}}function rc(e,t){return t.map(({type:t,start:r,end:n,name:i,data:s})=>{let o=e.throttledAddEvent({type:eQ.Custom,timestamp:r,data:{tag:"performanceSpan",payload:{op:t,description:i,startTimestamp:r,endTimestamp:n,data:s}}});return"string"==typeof o?Promise.resolve(null):o})}function ru(e,t){var r;e.isEnabled()&&null!==t&&(r=t.name,(0,b.A)(r,(0,S.KU)())||e.addUpdate(()=>(rc(e,[t]),!0)))}function rd(e){if(!e)return;let t=new TextEncoder;try{if("string"==typeof e)return t.encode(e).length;if(e instanceof URLSearchParams)return t.encode(e.toString()).length;if(e instanceof FormData){let r=(0,A.P4)(e);return t.encode(r).length}if(e instanceof Blob)return e.size;if(e instanceof ArrayBuffer)return e.byteLength}catch{}}function rh(e){if(!e)return;let t=parseInt(e,10);return isNaN(t)?void 0:t}function rp(e,t){if(!e)return{headers:{},size:void 0,_meta:{warnings:[t]}};let r={...e._meta},n=r.warnings||[];return r.warnings=[...n,t],e._meta=r,e}function rf(e,t){if(!t)return null;let{startTimestamp:r,endTimestamp:n,url:i,method:s,statusCode:o,request:a,response:l}=t;return{type:e,start:r/1e3,end:n/1e3,name:i,data:{method:s,statusCode:o,request:a,response:l}}}function rm(e){return{headers:{},size:e,_meta:{warnings:["URL_SKIPPED"]}}}function ry(e,t,r){if(!t&&0===Object.keys(e).length)return;if(!t)return{headers:e};if(!r)return{headers:e,size:t};let n={headers:e,size:t},{body:i,warnings:s}=function(e){if(!e||"string"!=typeof e)return{body:e};let t=e.length>15e4,r=function(e){let t=e[0],r=e[e.length-1];return"["===t&&"]"===r||"{"===t&&"}"===r}(e);if(t){let t=e.slice(0,15e4);return r?{body:t,warnings:["MAYBE_JSON_TRUNCATED"]}:{body:`${t}…`,warnings:["TEXT_TRUNCATED"]}}if(r)try{return{body:JSON.parse(e)}}catch{}return{body:e}}(r);return n.body=i,s?.length&&(n._meta={warnings:s}),n}function rg(e,t){return Object.entries(e).reduce((r,[n,i])=>{let s=n.toLowerCase();return t.includes(s)&&e[n]&&(r[s]=i),r},{})}function rv(e,t){let r=function(e,t=U.document.baseURI){if(e.startsWith("http://")||e.startsWith("https://")||e.startsWith(U.location.origin))return e;let r=new URL(e,t);if(r.origin!==new URL(t).origin)return e;let n=r.href;return!e.endsWith("/")&&n.endsWith("/")?n.slice(0,-1):n}(e);return(0,k.Xr)(r,t)}async function rS(e,t,r){try{let n=await r_(e,t,r),i=rf("resource.fetch",n);ru(r.replay,i)}catch(e){}}async function r_(e,t,r){let n=Date.now(),{startTimestamp:i=n,endTimestamp:s=n}=t,{url:o,method:a,status_code:l=0,request_body_size:c,response_body_size:u}=e.data,d=rv(o,r.networkDetailAllowUrls)&&!rv(o,r.networkDetailDenyUrls);return{startTimestamp:i,endTimestamp:s,url:o,method:a,statusCode:l,request:d?function({networkCaptureBodies:e,networkRequestHeaders:t},r,n){var i,s;let o=r?(i=r,s=t,1===i.length&&"string"!=typeof i[0]?rE(i[0],s):2===i.length?rE(i[1],s):{}):{};if(!e)return ry(o,n,void 0);let a=(0,A.Gv)(r),[l,c]=(0,A.sY)(a,tY),u=ry(o,n,l);return c?rp(u,c):u}(r,t.input,c):rm(c),response:await rw(d,r,t.response,u)}}async function rw(e,{networkCaptureBodies:t,networkResponseHeaders:r},n,i){if(!e&&void 0!==i)return rm(i);let s=n?rk(n.headers,r):{};if(!n||!t&&void 0!==i)return ry(s,i,void 0);let[o,a]=await rb(n),l=function(e,{networkCaptureBodies:t,responseBodySize:r,captureDetails:n,headers:i}){try{let s=e?.length&&void 0===r?rd(e):r;if(!n)return rm(s);if(t)return ry(i,s,e);return ry(i,s,void 0)}catch(e){return ry(i,r,void 0)}}(o,{networkCaptureBodies:t,responseBodySize:i,captureDetails:e,headers:s});return a?rp(l,a):l}async function rb(e){let t=function(e){try{return e.clone()}catch(e){}}(e);if(!t)return[void 0,"BODY_PARSE_ERROR"];try{var r;return[await (r=t,new Promise((e,t)=>{let n=(0,O.wg)(()=>t(Error("Timeout while trying to read response body")),500);rM(r).then(t=>e(t),e=>t(e)).finally(()=>clearTimeout(n))}))]}catch(e){if(e instanceof Error&&e.message.indexOf("Timeout")>-1)return[void 0,"BODY_PARSE_TIMEOUT"];return[void 0,"BODY_PARSE_ERROR"]}}function rk(e,t){let r={};return t.forEach(t=>{e.get(t)&&(r[t]=e.get(t))}),r}function rE(e,t){if(!e)return{};let r=e.headers;return r?r instanceof Headers?rk(r,t):Array.isArray(r)?{}:rg(r,t):{}}async function rM(e){return await e.text()}async function rI(e,t,r){try{let n=function(e,t,r){let n=Date.now(),{startTimestamp:i=n,endTimestamp:s=n,input:o,xhr:a}=t,{url:l,method:c,status_code:u=0,request_body_size:d,response_body_size:h}=e.data;if(!l)return null;if(!a||!rv(l,r.networkDetailAllowUrls)||rv(l,r.networkDetailDenyUrls)){let e=rm(d);return{startTimestamp:i,endTimestamp:s,url:l,method:c,statusCode:u,request:e,response:rm(h)}}let p=a[L.Er],f=p?rg(p.request_headers,r.networkRequestHeaders):{},m=rg(function(e){let t=e.getAllResponseHeaders();return t?t.split("\r\n").reduce((e,t)=>{let[r,n]=t.split(": ");return n&&(e[r.toLowerCase()]=n),e},{}):{}}(a),r.networkResponseHeaders),[y,g]=r.networkCaptureBodies?(0,A.sY)(o,tY):[void 0],[v,S]=r.networkCaptureBodies?function(e){let t=[];try{return[e.responseText]}catch(e){t.push(e)}try{var r=e.response,n=e.responseType;try{if("string"==typeof r)return[r];if(r instanceof Document)return[r.body.outerHTML];if("json"===n&&r&&"object"==typeof r)return[JSON.stringify(r)];if(!r)return[void 0]}catch(e){return[void 0,"BODY_PARSE_ERROR"]}return[void 0,"UNPARSEABLE_BODY_TYPE"]}catch(e){t.push(e)}return[void 0]}(a):[void 0],_=ry(f,d,y),w=ry(m,h,v);return{startTimestamp:i,endTimestamp:s,url:l,method:c,statusCode:u,request:g?rp(_,g):_,response:S?rp(w,S):w}}(e,t,r),i=rf("resource.xhr",n);ru(r.replay,i)}catch(e){}}async function rC(e){try{return Promise.all(rc(e,[function(e){let{jsHeapSizeLimit:t,totalJSHeapSize:r,usedJSHeapSize:n}=e,i=Date.now()/1e3;return{type:"memory",name:"memory",start:i,end:i,data:{memory:{jsHeapSizeLimit:t,totalJSHeapSize:r,usedJSHeapSize:n}}}}(U.performance.memory)]))}catch{return[]}}let rR=u.O.navigator;async function rT({client:e,scope:t,replayId:r,event:n}){let i={event_id:r,integrations:"object"!=typeof e._integrations||null===e._integrations||Array.isArray(e._integrations)?void 0:Object.keys(e._integrations)};e.emit("preprocessEvent",n,i);let s=await (0,I.mG)(e.getOptions(),n,i,t,e,(0,S.rm)());if(!s)return null;e.emit("postprocessEvent",s,i),s.platform=s.platform||"javascript";let o=e.getSdkMetadata(),{name:a,version:l}=o?.sdk||{};return s.sdk={...s.sdk,name:a||"sentry.javascript.unknown",version:l||"0.0.0"},s}async function rx({recordingData:e,replayId:t,segmentId:r,eventContext:n,timestamp:i,session:s}){var o;let a,l=function({recordingData:e,headers:t}){let r,n=`${JSON.stringify(t)}
`;if("string"==typeof e)r=`${n}${e}`;else{let t=new TextEncoder().encode(n);(r=new Uint8Array(t.length+e.length)).set(t),r.set(e,t.length)}return r}({recordingData:e,headers:{segment_id:r}}),{urls:c,errorIds:u,traceIds:d,initialTimestamp:h}=n,p=(0,S.KU)(),f=(0,S.o5)(),m=p?.getTransport(),y=p?.getDsn();if(!p||!m||!y||!s.sampled)return(0,C.XW)({});let g={type:"replay_event",replay_start_timestamp:h/1e3,timestamp:i/1e3,error_ids:u,trace_ids:d,urls:c,replay_id:t,segment_id:r,replay_type:s.sampled},v=await rT({scope:f,client:p,replayId:t,event:g});if(!v)return p.recordDroppedEvent("event_processor","replay"),(0,C.XW)({});delete v.sdkProcessingMetadata;let _=(o=p.getOptions().tunnel,(0,M.h4)((0,M.n2)(v,(0,M.Cj)(v),o,y),[[{type:"replay_event"},v],[{type:"replay_recording",length:"string"==typeof l?new TextEncoder().encode(l).length:l.length},l]]));try{a=await m.send(_)}catch(t){let e=Error(z);try{e.cause=t}catch{}throw e}if("number"==typeof a.statusCode&&(a.statusCode<200||a.statusCode>=300))throw new rD(a.statusCode);let w=(0,R.wq)({},a);if((0,R.Jz)(w,"replay"))throw new rO(w);return a}class rD extends Error{constructor(e){super(`Transport returned status code ${e}`)}}class rO extends Error{constructor(e){super("Rate limit hit"),this.rateLimits=e}}async function rN(e,t={count:0,interval:5e3}){let{recordingData:r,onError:n}=e;if(r.length)try{return await rx(e),!0}catch(r){if(r instanceof rD||r instanceof rO)throw r;if((0,g.o)("Replays",{_retryCount:t.count}),n&&n(r),t.count>=3){let e=Error(`${z} - max retries exceeded`);try{e.cause=r}catch{}throw e}return t.interval*=++t.count,new Promise((r,n)=>{(0,O.wg)(async()=>{try{await rN(e,t),r(!0)}catch(e){n(e)}},t.interval)})}}let rA="__THROTTLED";class rL{constructor({options:e,recordingOptions:t}){var r,n,i;this.eventBuffer=null,this.performanceEntries=[],this.replayPerformanceEntries=[],this.recordingMode="session",this.timeouts={sessionIdlePause:3e5,sessionIdleExpire:9e5},this._lastActivity=Date.now(),this._isEnabled=!1,this._isPaused=!1,this._requiresManualStart=!1,this._hasInitializedCoreListeners=!1,this._context={errorIds:new Set,traceIds:new Set,urls:[],initialTimestamp:Date.now(),initialUrl:""},this._recordingOptions=t,this._options=e,this._debouncedFlush=(r=()=>this._flush(),n=this._options.flushMinDelay,i={maxWait:this._options.flushMaxDelay},(0,E.s)(r,n,{...i,setTimeoutImpl:O.wg})),this._throttledAddEvent=function(e,t,r){let n=new Map,i=e=>{let t=e-5;n.forEach((e,r)=>{r<t&&n.delete(r)})},s=()=>[...n.values()].reduce((e,t)=>e+t,0),o=!1;return(...t)=>{let r=Math.floor(Date.now()/1e3);if(i(r),s()>=300){let e=o;return o=!0,e?"__SKIPPED":rA}o=!1;let a=n.get(r)||0;return n.set(r,a+1),e(...t)}}((e,t)=>(function(e,t,r){return ri(e,t)?rn(e,t,r):Promise.resolve(null)})(this,e,t),0,0);let{slowClickTimeout:s,slowClickIgnoreSelectors:o}=this.getOptions(),a=s?{threshold:Math.min(3e3,s),timeout:s,scrollTimeout:300,ignoreSelector:o?o.join(","):""}:void 0;a&&(this.clickDetector=new tN(this,a)),this._handleVisibilityChange=()=>{"visible"===U.document.visibilityState?this._doChangeToForegroundTasks():this._doChangeToBackgroundTasks()},this._handleWindowBlur=()=>{let e=tP({category:"ui.blur"});this._doChangeToBackgroundTasks(e)},this._handleWindowFocus=()=>{let e=tP({category:"ui.focus"});this._doChangeToForegroundTasks(e)},this._handleKeyboardEvent=e=>{!function(e,t){if(!e.isEnabled())return;e.updateUserActivity();let r=function(e){var t;let{metaKey:r,shiftKey:n,ctrlKey:i,altKey:s,key:o,target:a}=e;if(!a||"INPUT"===(t=a).tagName||"TEXTAREA"===t.tagName||t.isContentEditable||!o)return null;let l=r||i||s,c=1===o.length;if(!l&&c)return null;let u=(0,p.Hd)(a,{maxStringLength:200})||"<unknown>",d=tz(a,u);return tP({category:"ui.keyDown",message:u,data:{...d.data,metaKey:r,shiftKey:n,ctrlKey:i,altKey:s,key:o}})}(t);r&&tR(e,r)}(this,e)}}getContext(){return this._context}isEnabled(){return this._isEnabled}isPaused(){return this._isPaused}isRecordingCanvas(){return!!this._canvas}getOptions(){return this._options}handleException(e){this._options.onError&&this._options.onError(e)}initializeSampling(e){let{errorSampleRate:t,sessionSampleRate:r}=this._options,n=t<=0&&r<=0;if(this._requiresManualStart=n,!n)this._initializeSessionForSampling(e),this.session&&!1!==this.session.sampled&&(this.recordingMode="buffer"===this.session.sampled&&0===this.session.segmentId?"buffer":"session",this._initializeRecording())}start(){if(this._isEnabled&&"session"===this.recordingMode||this._isEnabled&&"buffer"===this.recordingMode)return;this._updateUserActivity();let e=rt({maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire},{stickySession:this._options.stickySession,sessionSampleRate:1,allowBuffering:!1});this.session=e,this.recordingMode="session",this._initializeRecording()}startBuffering(){if(this._isEnabled)return;let e=rt({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration},{stickySession:this._options.stickySession,sessionSampleRate:0,allowBuffering:!0});this.session=e,this.recordingMode="buffer",this._initializeRecording()}startRecording(){try{var e;let t,r=this._canvas;this._stopRecording=tM({...this._recordingOptions,..."buffer"===this.recordingMode?{checkoutEveryNms:6e4}:this._options._experiments.continuousCheckout&&{checkoutEveryNms:Math.max(36e4,this._options._experiments.continuousCheckout)},emit:(e=this,t=!1,(r,n)=>{if(!e.checkAndHandleExpiredSession())return;let i=n||!t;t=!0,e.clickDetector&&function(e,t){try{var r;if(r=t,3!==r.type)return;let{source:n}=t.data;if(tO.has(n)&&e.registerMutation(t.timestamp),n===eZ.Scroll&&e.registerScroll(t.timestamp),t.data.source===eZ.MouseInteraction){let{type:r,id:n}=t.data,i=tM.mirror.getNode(n);i instanceof HTMLElement&&r===e0.Click&&e.registerClick(i)}}catch{}}(e.clickDetector,r),e.addUpdate(()=>{var t;if("buffer"===e.recordingMode&&i&&e.setInitialState(),!rr(e,r,i))return!0;if(!i)return!1;let n=e.session;if(t=e,i&&t.session&&0===t.session.segmentId&&rr(t,function(e){let t=e.getOptions();return{type:eQ.Custom,timestamp:Date.now(),data:{tag:"options",payload:{shouldRecordCanvas:e.isRecordingCanvas(),sessionSampleRate:t.sessionSampleRate,errorSampleRate:t.errorSampleRate,useCompressionOption:t.useCompression,blockAllMedia:t.blockAllMedia,maskAllText:t.maskAllText,maskAllInputs:t.maskAllInputs,useCompression:!!e.eventBuffer&&"worker"===e.eventBuffer.type,networkDetailHasUrls:t.networkDetailAllowUrls.length>0,networkCaptureBodies:t.networkCaptureBodies,networkRequestHasHeaders:t.networkRequestHeaders.length>0,networkResponseHasHeaders:t.networkResponseHeaders.length>0}}}}(t),!1),"buffer"===e.recordingMode&&n&&e.eventBuffer){let t=e.eventBuffer.getEarliestTimestamp();t&&(n.started=t,e.getOptions().stickySession&&t8(n))}return!!n?.previousSessionId||("session"===e.recordingMode&&e.flush(),!0)})}),.../iPhone|iPad|iPod/i.test(rR?.userAgent??"")||/Macintosh/i.test(rR?.userAgent??"")&&rR?.maxTouchPoints&&rR?.maxTouchPoints>1?{sampling:{mousemove:!1}}:{},onMutation:this._onMutationHandler.bind(this),...r?{recordCanvas:r.recordCanvas,getCanvasManager:r.getCanvasManager,sampling:r.sampling,dataURLOptions:r.dataURLOptions}:{}})}catch(e){this.handleException(e)}}stopRecording(){try{return this._stopRecording&&(this._stopRecording(),this._stopRecording=void 0),!0}catch(e){return this.handleException(e),!1}}async stop({forceFlush:e=!1,reason:t}={}){if(this._isEnabled){this._isEnabled=!1,this.recordingMode="buffer";try{rl(),this._removeListeners(),this.stopRecording(),this._debouncedFlush.cancel(),e&&await this._flush({force:!0}),this.eventBuffer?.destroy(),this.eventBuffer=null,function(){if(t3())try{U.sessionStorage.removeItem(B)}catch{}}(),this.session=void 0}catch(e){this.handleException(e)}}}pause(){this._isPaused||(this._isPaused=!0,this.stopRecording())}resume(){this._isPaused&&this._checkSession()&&(this._isPaused=!1,this.startRecording())}async sendBufferedReplayOrFlush({continueRecording:e=!0}={}){if("session"===this.recordingMode)return this.flushImmediate();let t=Date.now();await this.flushImmediate();let r=this.stopRecording();e&&r&&"session"!==this.recordingMode&&(this.recordingMode="session",this.session&&(this._updateUserActivity(t),this._updateSessionActivity(t),this._maybeSaveSession()),this.startRecording())}addUpdate(e){let t=e();"buffer"!==this.recordingMode&&this._isEnabled&&!0!==t&&this._debouncedFlush()}triggerUserActivity(){if(this._updateUserActivity(),!this._stopRecording){if(!this._checkSession())return;this.resume();return}this.checkAndHandleExpiredSession(),this._updateSessionActivity()}updateUserActivity(){this._updateUserActivity(),this._updateSessionActivity()}conditionalFlush(){return"buffer"===this.recordingMode?Promise.resolve():this.flushImmediate()}flush(){return this._debouncedFlush()}flushImmediate(){return this._debouncedFlush(),this._debouncedFlush.flush()}cancelFlush(){this._debouncedFlush.cancel()}getSessionId(){return this.session?.id}checkAndHandleExpiredSession(){return this._lastActivity&&t7(this._lastActivity,this.timeouts.sessionIdlePause)&&this.session&&"session"===this.session.sampled?void this.pause():!!this._checkSession()}setInitialState(){let e=`${U.location.pathname}${U.location.hash}${U.location.search}`,t=`${U.location.origin}${e}`;this.performanceEntries=[],this.replayPerformanceEntries=[],this._clearContext(),this._context.initialUrl=t,this._context.initialTimestamp=Date.now(),this._context.urls.push(t)}throttledAddEvent(e,t){let r=this._throttledAddEvent(e,t);if(r===rA){let e=tP({category:"replay.throttled"});this.addUpdate(()=>!rr(this,{type:5,timestamp:e.timestamp||0,data:{tag:"breadcrumb",payload:e,metric:!0}}))}return r}getCurrentRoute(){let e=this.lastActiveSpan||(0,_.Bk)(),t=e&&(0,_.zU)(e),r=(t&&(0,_.et)(t).data||{})[T.i_];if(t&&r&&["route","custom"].includes(r))return(0,_.et)(t).description}_initializeRecording(){this.setInitialState(),this._updateSessionActivity(),this.eventBuffer=function({useCompression:e,workerUrl:t}){if(e&&window.Worker){let e=function(e){try{let t=e||function(){if("undefined"==typeof __SENTRY_EXCLUDE_REPLAY_WORKER__||!__SENTRY_EXCLUDE_REPLAY_WORKER__){let e=new Blob(['var t=Uint8Array,n=Uint16Array,r=Int32Array,e=new t([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),i=new t([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),s=new t([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),a=function(t,e){for(var i=new n(31),s=0;s<31;++s)i[s]=e+=1<<t[s-1];var a=new r(i[30]);for(s=1;s<30;++s)for(var o=i[s];o<i[s+1];++o)a[o]=o-i[s]<<5|s;return{b:i,r:a}},o=a(e,2),h=o.b,f=o.r;h[28]=258,f[258]=28;for(var l=a(i,0).r,u=new n(32768),c=0;c<32768;++c){var v=(43690&c)>>1|(21845&c)<<1;v=(61680&(v=(52428&v)>>2|(13107&v)<<2))>>4|(3855&v)<<4,u[c]=((65280&v)>>8|(255&v)<<8)>>1}var d=function(t,r,e){for(var i=t.length,s=0,a=new n(r);s<i;++s)t[s]&&++a[t[s]-1];var o,h=new n(r);for(s=1;s<r;++s)h[s]=h[s-1]+a[s-1]<<1;if(e){o=new n(1<<r);var f=15-r;for(s=0;s<i;++s)if(t[s])for(var l=s<<4|t[s],c=r-t[s],v=h[t[s]-1]++<<c,d=v|(1<<c)-1;v<=d;++v)o[u[v]>>f]=l}else for(o=new n(i),s=0;s<i;++s)t[s]&&(o[s]=u[h[t[s]-1]++]>>15-t[s]);return o},p=new t(288);for(c=0;c<144;++c)p[c]=8;for(c=144;c<256;++c)p[c]=9;for(c=256;c<280;++c)p[c]=7;for(c=280;c<288;++c)p[c]=8;var g=new t(32);for(c=0;c<32;++c)g[c]=5;var w=d(p,9,0),y=d(g,5,0),m=function(t){return(t+7)/8|0},b=function(n,r,e){return(null==e||e>n.length)&&(e=n.length),new t(n.subarray(r,e))},M=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],E=function(t,n,r){var e=new Error(n||M[t]);if(e.code=t,Error.captureStackTrace&&Error.captureStackTrace(e,E),!r)throw e;return e},z=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8},_=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8,t[e+2]|=r>>16},x=function(r,e){for(var i=[],s=0;s<r.length;++s)r[s]&&i.push({s:s,f:r[s]});var a=i.length,o=i.slice();if(!a)return{t:F,l:0};if(1==a){var h=new t(i[0].s+1);return h[i[0].s]=1,{t:h,l:1}}i.sort(function(t,n){return t.f-n.f}),i.push({s:-1,f:25001});var f=i[0],l=i[1],u=0,c=1,v=2;for(i[0]={s:-1,f:f.f+l.f,l:f,r:l};c!=a-1;)f=i[i[u].f<i[v].f?u++:v++],l=i[u!=c&&i[u].f<i[v].f?u++:v++],i[c++]={s:-1,f:f.f+l.f,l:f,r:l};var d=o[0].s;for(s=1;s<a;++s)o[s].s>d&&(d=o[s].s);var p=new n(d+1),g=A(i[c-1],p,0);if(g>e){s=0;var w=0,y=g-e,m=1<<y;for(o.sort(function(t,n){return p[n.s]-p[t.s]||t.f-n.f});s<a;++s){var b=o[s].s;if(!(p[b]>e))break;w+=m-(1<<g-p[b]),p[b]=e}for(w>>=y;w>0;){var M=o[s].s;p[M]<e?w-=1<<e-p[M]++-1:++s}for(;s>=0&&w;--s){var E=o[s].s;p[E]==e&&(--p[E],++w)}g=e}return{t:new t(p),l:g}},A=function(t,n,r){return-1==t.s?Math.max(A(t.l,n,r+1),A(t.r,n,r+1)):n[t.s]=r},D=function(t){for(var r=t.length;r&&!t[--r];);for(var e=new n(++r),i=0,s=t[0],a=1,o=function(t){e[i++]=t},h=1;h<=r;++h)if(t[h]==s&&h!=r)++a;else{if(!s&&a>2){for(;a>138;a-=138)o(32754);a>2&&(o(a>10?a-11<<5|28690:a-3<<5|12305),a=0)}else if(a>3){for(o(s),--a;a>6;a-=6)o(8304);a>2&&(o(a-3<<5|8208),a=0)}for(;a--;)o(s);a=1,s=t[h]}return{c:e.subarray(0,i),n:r}},T=function(t,n){for(var r=0,e=0;e<n.length;++e)r+=t[e]*n[e];return r},k=function(t,n,r){var e=r.length,i=m(n+2);t[i]=255&e,t[i+1]=e>>8,t[i+2]=255^t[i],t[i+3]=255^t[i+1];for(var s=0;s<e;++s)t[i+s+4]=r[s];return 8*(i+4+e)},U=function(t,r,a,o,h,f,l,u,c,v,m){z(r,m++,a),++h[256];for(var b=x(h,15),M=b.t,E=b.l,A=x(f,15),U=A.t,C=A.l,F=D(M),I=F.c,S=F.n,L=D(U),O=L.c,j=L.n,q=new n(19),B=0;B<I.length;++B)++q[31&I[B]];for(B=0;B<O.length;++B)++q[31&O[B]];for(var G=x(q,7),H=G.t,J=G.l,K=19;K>4&&!H[s[K-1]];--K);var N,P,Q,R,V=v+5<<3,W=T(h,p)+T(f,g)+l,X=T(h,M)+T(f,U)+l+14+3*K+T(q,H)+2*q[16]+3*q[17]+7*q[18];if(c>=0&&V<=W&&V<=X)return k(r,m,t.subarray(c,c+v));if(z(r,m,1+(X<W)),m+=2,X<W){N=d(M,E,0),P=M,Q=d(U,C,0),R=U;var Y=d(H,J,0);z(r,m,S-257),z(r,m+5,j-1),z(r,m+10,K-4),m+=14;for(B=0;B<K;++B)z(r,m+3*B,H[s[B]]);m+=3*K;for(var Z=[I,O],$=0;$<2;++$){var tt=Z[$];for(B=0;B<tt.length;++B){var nt=31&tt[B];z(r,m,Y[nt]),m+=H[nt],nt>15&&(z(r,m,tt[B]>>5&127),m+=tt[B]>>12)}}}else N=w,P=p,Q=y,R=g;for(B=0;B<u;++B){var rt=o[B];if(rt>255){_(r,m,N[(nt=rt>>18&31)+257]),m+=P[nt+257],nt>7&&(z(r,m,rt>>23&31),m+=e[nt]);var et=31&rt;_(r,m,Q[et]),m+=R[et],et>3&&(_(r,m,rt>>5&8191),m+=i[et])}else _(r,m,N[rt]),m+=P[rt]}return _(r,m,N[256]),m+P[256]},C=new r([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),F=new t(0),I=function(){for(var t=new Int32Array(256),n=0;n<256;++n){for(var r=n,e=9;--e;)r=(1&r&&-306674912)^r>>>1;t[n]=r}return t}(),S=function(){var t=1,n=0;return{p:function(r){for(var e=t,i=n,s=0|r.length,a=0;a!=s;){for(var o=Math.min(a+2655,s);a<o;++a)i+=e+=r[a];e=(65535&e)+15*(e>>16),i=(65535&i)+15*(i>>16)}t=e,n=i},d:function(){return(255&(t%=65521))<<24|(65280&t)<<8|(255&(n%=65521))<<8|n>>8}}},L=function(s,a,o,h,u){if(!u&&(u={l:1},a.dictionary)){var c=a.dictionary.subarray(-32768),v=new t(c.length+s.length);v.set(c),v.set(s,c.length),s=v,u.w=c.length}return function(s,a,o,h,u,c){var v=c.z||s.length,d=new t(h+v+5*(1+Math.ceil(v/7e3))+u),p=d.subarray(h,d.length-u),g=c.l,w=7&(c.r||0);if(a){w&&(p[0]=c.r>>3);for(var y=C[a-1],M=y>>13,E=8191&y,z=(1<<o)-1,_=c.p||new n(32768),x=c.h||new n(z+1),A=Math.ceil(o/3),D=2*A,T=function(t){return(s[t]^s[t+1]<<A^s[t+2]<<D)&z},F=new r(25e3),I=new n(288),S=new n(32),L=0,O=0,j=c.i||0,q=0,B=c.w||0,G=0;j+2<v;++j){var H=T(j),J=32767&j,K=x[H];if(_[J]=K,x[H]=J,B<=j){var N=v-j;if((L>7e3||q>24576)&&(N>423||!g)){w=U(s,p,0,F,I,S,O,q,G,j-G,w),q=L=O=0,G=j;for(var P=0;P<286;++P)I[P]=0;for(P=0;P<30;++P)S[P]=0}var Q=2,R=0,V=E,W=J-K&32767;if(N>2&&H==T(j-W))for(var X=Math.min(M,N)-1,Y=Math.min(32767,j),Z=Math.min(258,N);W<=Y&&--V&&J!=K;){if(s[j+Q]==s[j+Q-W]){for(var $=0;$<Z&&s[j+$]==s[j+$-W];++$);if($>Q){if(Q=$,R=W,$>X)break;var tt=Math.min(W,$-2),nt=0;for(P=0;P<tt;++P){var rt=j-W+P&32767,et=rt-_[rt]&32767;et>nt&&(nt=et,K=rt)}}}W+=(J=K)-(K=_[J])&32767}if(R){F[q++]=268435456|f[Q]<<18|l[R];var it=31&f[Q],st=31&l[R];O+=e[it]+i[st],++I[257+it],++S[st],B=j+Q,++L}else F[q++]=s[j],++I[s[j]]}}for(j=Math.max(j,B);j<v;++j)F[q++]=s[j],++I[s[j]];w=U(s,p,g,F,I,S,O,q,G,j-G,w),g||(c.r=7&w|p[w/8|0]<<3,w-=7,c.h=x,c.p=_,c.i=j,c.w=B)}else{for(j=c.w||0;j<v+g;j+=65535){var at=j+65535;at>=v&&(p[w/8|0]=g,at=v),w=k(p,w+1,s.subarray(j,at))}c.i=v}return b(d,0,h+m(w)+u)}(s,null==a.level?6:a.level,null==a.mem?u.l?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(s.length)))):20:12+a.mem,o,h,u)},O=function(t,n,r){for(;r;++n)t[n]=r,r>>>=8},j=function(){function n(n,r){if("function"==typeof n&&(r=n,n={}),this.ondata=r,this.o=n||{},this.s={l:0,i:32768,w:32768,z:32768},this.b=new t(98304),this.o.dictionary){var e=this.o.dictionary.subarray(-32768);this.b.set(e,32768-e.length),this.s.i=32768-e.length}}return n.prototype.p=function(t,n){this.ondata(L(t,this.o,0,0,this.s),n)},n.prototype.push=function(n,r){this.ondata||E(5),this.s.l&&E(4);var e=n.length+this.s.z;if(e>this.b.length){if(e>2*this.b.length-32768){var i=new t(-32768&e);i.set(this.b.subarray(0,this.s.z)),this.b=i}var s=this.b.length-this.s.z;this.b.set(n.subarray(0,s),this.s.z),this.s.z=this.b.length,this.p(this.b,!1),this.b.set(this.b.subarray(-32768)),this.b.set(n.subarray(s),32768),this.s.z=n.length-s+32768,this.s.i=32766,this.s.w=32768}else this.b.set(n,this.s.z),this.s.z+=n.length;this.s.l=1&r,(this.s.z>this.s.w+8191||r)&&(this.p(this.b,r||!1),this.s.w=this.s.i,this.s.i-=2)},n.prototype.flush=function(){this.ondata||E(5),this.s.l&&E(4),this.p(this.b,!1),this.s.w=this.s.i,this.s.i-=2},n}();function q(t,n){n||(n={});var r=function(){var t=-1;return{p:function(n){for(var r=t,e=0;e<n.length;++e)r=I[255&r^n[e]]^r>>>8;t=r},d:function(){return~t}}}(),e=t.length;r.p(t);var i,s=L(t,n,10+((i=n).filename?i.filename.length+1:0),8),a=s.length;return function(t,n){var r=n.filename;if(t[0]=31,t[1]=139,t[2]=8,t[8]=n.level<2?4:9==n.level?2:0,t[9]=3,0!=n.mtime&&O(t,4,Math.floor(new Date(n.mtime||Date.now())/1e3)),r){t[3]=8;for(var e=0;e<=r.length;++e)t[e+10]=r.charCodeAt(e)}}(s,n),O(s,a-8,r.d()),O(s,a-4,e),s}var B=function(){function t(t,n){this.c=S(),this.v=1,j.call(this,t,n)}return t.prototype.push=function(t,n){this.c.p(t),j.prototype.push.call(this,t,n)},t.prototype.p=function(t,n){var r=L(t,this.o,this.v&&(this.o.dictionary?6:2),n&&4,this.s);this.v&&(function(t,n){var r=n.level,e=0==r?0:r<6?1:9==r?3:2;if(t[0]=120,t[1]=e<<6|(n.dictionary&&32),t[1]|=31-(t[0]<<8|t[1])%31,n.dictionary){var i=S();i.p(n.dictionary),O(t,2,i.d())}}(r,this.o),this.v=0),n&&O(r,r.length-4,this.c.d()),this.ondata(r,n)},t.prototype.flush=function(){j.prototype.flush.call(this)},t}(),G="undefined"!=typeof TextEncoder&&new TextEncoder,H="undefined"!=typeof TextDecoder&&new TextDecoder;try{H.decode(F,{stream:!0})}catch(t){}var J=function(){function t(t){this.ondata=t}return t.prototype.push=function(t,n){this.ondata||E(5),this.d&&E(4),this.ondata(K(t),this.d=n||!1)},t}();function K(n,r){if(G)return G.encode(n);for(var e=n.length,i=new t(n.length+(n.length>>1)),s=0,a=function(t){i[s++]=t},o=0;o<e;++o){if(s+5>i.length){var h=new t(s+8+(e-o<<1));h.set(i),i=h}var f=n.charCodeAt(o);f<128||r?a(f):f<2048?(a(192|f>>6),a(128|63&f)):f>55295&&f<57344?(a(240|(f=65536+(1047552&f)|1023&n.charCodeAt(++o))>>18),a(128|f>>12&63),a(128|f>>6&63),a(128|63&f)):(a(224|f>>12),a(128|f>>6&63),a(128|63&f))}return b(i,0,s)}const N=new class{constructor(){this._init()}clear(){this._init()}addEvent(t){if(!t)throw new Error("Adding invalid event");const n=this._hasEvents?",":"";this.stream.push(n+t),this._hasEvents=!0}finish(){this.stream.push("]",!0);const t=function(t){let n=0;for(const r of t)n+=r.length;const r=new Uint8Array(n);for(let n=0,e=0,i=t.length;n<i;n++){const i=t[n];r.set(i,e),e+=i.length}return r}(this._deflatedData);return this._init(),t}_init(){this._hasEvents=!1,this._deflatedData=[],this.deflate=new B,this.deflate.ondata=(t,n)=>{this._deflatedData.push(t)},this.stream=new J((t,n)=>{this.deflate.push(t,n)}),this.stream.push("[")}},P={clear:()=>{N.clear()},addEvent:t=>N.addEvent(t),finish:()=>N.finish(),compress:t=>function(t){return q(K(t))}(t)};addEventListener("message",function(t){const n=t.data.method,r=t.data.id,e=t.data.arg;if(n in P&&"function"==typeof P[n])try{const t=P[n](e);postMessage({id:r,method:n,success:!0,response:t})}catch(t){postMessage({id:r,method:n,success:!1,response:t.message}),console.error(t)}}),postMessage({id:void 0,method:"init",success:!0,response:void 0});']);return URL.createObjectURL(e)}return""}();if(!t)return;let r=new Worker(t);return new t2(r)}catch(e){}}(t);if(e)return e}return new tZ}({useCompression:this._options.useCompression,workerUrl:this._options.workerUrl}),this._removeListeners(),this._addListeners(),this._isEnabled=!0,this._isPaused=!1,this.startRecording()}_initializeSessionForSampling(e){let t=this._options.errorSampleRate>0,r=rt({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration,previousSessionId:e},{stickySession:this._options.stickySession,sessionSampleRate:this._options.sessionSampleRate,allowBuffering:t});this.session=r}_checkSession(){if(!this.session)return!1;let e=this.session;return!re(e,{sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration})||(this._refreshSession(e),!1)}async _refreshSession(e){this._isEnabled&&(await this.stop({reason:"refresh session"}),this.initializeSampling(e.id))}_addListeners(){try{U.document.addEventListener("visibilitychange",this._handleVisibilityChange),U.addEventListener("blur",this._handleWindowBlur),U.addEventListener("focus",this._handleWindowFocus),U.addEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.addListeners(),this._hasInitializedCoreListeners||(!function(e){let t=(0,S.KU)();(0,P.i)(tB(e)),(0,F._)(t=>{if(!e.isEnabled())return;let r=function(e){let{from:t,to:r}=e,n=Date.now()/1e3;return{type:"navigation.push",start:n,end:n,name:r,data:{previous:t}}}(t);null!==r&&(e.getContext().urls.push(r.name),e.triggerUserActivity(),e.addUpdate(()=>(rc(e,[r]),!1)))});let r=(0,S.KU)();r&&r.on("beforeAddBreadcrumb",t=>(function(e,t){var r;if(!e.isEnabled()||!ra(t))return;let n=(r=t,!ra(r)||["fetch","xhr","sentry.event","sentry.transaction"].includes(r.category)||r.category.startsWith("ui.")?null:"console"===r.category?function(e){let t=e.data?.arguments;if(!Array.isArray(t)||0===t.length)return tP(e);let r=!1,n=t.map(e=>{if(!e)return e;if("string"==typeof e)return e.length>5e3?(r=!0,`${e.slice(0,5e3)}…`):e;if("object"==typeof e)try{let t=(0,d.S8)(e,7);if(JSON.stringify(t).length>5e3)return r=!0,`${JSON.stringify(t,null,2).slice(0,5e3)}…`;return t}catch{}return e});return tP({...e,data:{...e.data,arguments:n,...r?{_meta:{warnings:["CONSOLE_ARG_TRUNCATED"]}}:{}}})}(r):tP(r));n&&tR(e,n)})(e,t));let n=(0,S.KU)();try{let{networkDetailAllowUrls:t,networkDetailDenyUrls:r,networkCaptureBodies:i,networkRequestHeaders:s,networkResponseHeaders:o}=e.getOptions(),a={replay:e,networkDetailAllowUrls:t,networkDetailDenyUrls:r,networkCaptureBodies:i,networkRequestHeaders:s,networkResponseHeaders:o};n&&n.on("beforeAddBreadcrumb",(e,t)=>(function(e,t,r){if(t.data)try{var n,i,s,o;if(n=t,"xhr"===n.category&&(i=r,i?.xhr)&&(!function(e,t){let{xhr:r,input:n}=t;if(!r)return;let i=rd(n),s=r.getResponseHeader("content-length")?rh(r.getResponseHeader("content-length")):function(e,t){try{let r="json"===t&&e&&"object"==typeof e?JSON.stringify(e):e;return rd(r)}catch{return}}(r.response,r.responseType);void 0!==i&&(e.data.request_body_size=i),void 0!==s&&(e.data.response_body_size=s)}(t,r),rI(t,r,e)),s=t,"fetch"===s.category&&(o=r,o?.response)){let{input:n,response:i}=r,s=rd(n?(0,A.Gv)(n):void 0),o=i?rh(i.headers.get("content-length")):void 0;void 0!==s&&(t.data.request_body_size=s),void 0!==o&&(t.data.response_body_size=o),rS(t,r,e)}}catch(e){}})(a,e,t))}catch{}let i=Object.assign((t,r)=>!e.isEnabled()||e.isPaused()?t:"replay_event"===t.type?(delete t.breadcrumbs,t):!t.type||rs(t)||ro(t)?e.checkAndHandleExpiredSession()?ro(t)?(e.flush(),t.contexts.feedback.replay_id=e.getSessionId(),e.triggerUserActivity(),e.addUpdate(()=>!t.timestamp||(e.throttledAddEvent({type:eQ.Custom,timestamp:1e3*t.timestamp,data:{tag:"breadcrumb",payload:{timestamp:t.timestamp,type:"default",category:"sentry.feedback",data:{feedbackId:t.event_id}}}}),!1)),t):!t.type&&t.exception?.values?.length&&r.originalException?.__rrweb__&&!e.getOptions()._experiments.captureExceptions?null:(("buffer"===e.recordingMode&&t.message!==z&&t.exception&&!t.type&&t5(e.getOptions().errorSampleRate)||"session"===e.recordingMode)&&(t.tags={...t.tags,replayId:e.getSessionId()}),t):(rl(),t):t,{id:"Replay"});(0,g.SA)(i),t&&(t.on("beforeSendEvent",t=>{e.isEnabled()&&!t.type&&function(e,t){let r=t.exception?.values?.[0]?.value;"string"==typeof r&&(r.match(/(reactjs\.org\/docs\/error-decoder\.html\?invariant=|react\.dev\/errors\/)(418|419|422|423|425)/)||r.match(/(does not match server-rendered HTML|Hydration failed because)/i))&&tR(e,tP({category:"replay.hydrate-error",data:{url:(0,p.$N)()}}))}(e,t)}),t.on("afterSendEvent",(t,r)=>{if(!e.isEnabled()||t.type&&!rs(t))return;let n=r?.statusCode;if(n&&!(n<200)&&!(n>=300)){if(rs(t))return void function(e,t){let r=e.getContext();t.contexts?.trace?.trace_id&&r.traceIds.size<100&&r.traceIds.add(t.contexts.trace.trace_id)}(e,t);!function(e,t){let r=e.getContext();if(t.event_id&&r.errorIds.size<100&&r.errorIds.add(t.event_id),"buffer"!==e.recordingMode||!t.tags||!t.tags.replayId)return;let{beforeErrorSampling:n}=e.getOptions();("function"!=typeof n||n(t))&&(0,O.wg)(async()=>{try{await e.sendBufferedReplayOrFlush()}catch(t){e.handleException(t)}})}(e,t)}}),t.on("createDsc",t=>{let r=e.getSessionId();r&&e.isEnabled()&&"session"===e.recordingMode&&e.checkAndHandleExpiredSession()&&(t.replay_id=r)}),t.on("spanStart",t=>{e.lastActiveSpan=t}),t.on("spanEnd",t=>{e.lastActiveSpan=t}),t.on("beforeSendFeedback",async(t,r)=>{let n=e.getSessionId();r?.includeReplay&&e.isEnabled()&&n&&t.contexts?.feedback&&("api"===t.contexts.feedback.source&&await e.flush(),t.contexts.feedback.replay_id=n)}),t.on("openFeedbackWidget",async()=>{await e.flush()}))}(this),this._hasInitializedCoreListeners=!0)}catch(e){this.handleException(e)}this._performanceCleanupCallback=function(e){function t(t){e.performanceEntries.includes(t)||e.performanceEntries.push(t)}function r({entries:e}){e.forEach(t)}let n=[];return["navigation","paint","resource"].forEach(e=>{n.push((0,N.wv)(e,r))}),n.push((0,N.Pt)(tj(tq,e)),(0,N.a9)(tj(tK,e)),(0,N.T5)(tj(tV,e)),(0,N.hT)(tj(tJ,e))),()=>{n.forEach(e=>e())}}(this)}_removeListeners(){try{U.document.removeEventListener("visibilitychange",this._handleVisibilityChange),U.removeEventListener("blur",this._handleWindowBlur),U.removeEventListener("focus",this._handleWindowFocus),U.removeEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.removeListeners(),this._performanceCleanupCallback&&this._performanceCleanupCallback()}catch(e){this.handleException(e)}}_doChangeToBackgroundTasks(e){this.session&&(t9(this.session,{maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire})||(e&&this._createCustomBreadcrumb(e),this.conditionalFlush()))}_doChangeToForegroundTasks(e){if(this.session)this.checkAndHandleExpiredSession()&&e&&this._createCustomBreadcrumb(e)}_updateUserActivity(e=Date.now()){this._lastActivity=e}_updateSessionActivity(e=Date.now()){this.session&&(this.session.lastActivity=e,this._maybeSaveSession())}_createCustomBreadcrumb(e){this.addUpdate(()=>{this.throttledAddEvent({type:eQ.Custom,timestamp:e.timestamp||0,data:{tag:"breadcrumb",payload:e}})})}_addPerformanceEntries(){let e=this.performanceEntries.map(tH).filter(Boolean).concat(this.replayPerformanceEntries);if(this.performanceEntries=[],this.replayPerformanceEntries=[],this._requiresManualStart){let t=this._context.initialTimestamp/1e3;e=e.filter(e=>e.start>=t)}return Promise.all(rc(this,e))}_clearContext(){this._context.errorIds.clear(),this._context.traceIds.clear(),this._context.urls=[]}_updateInitialTimestampFromEventBuffer(){let{session:e,eventBuffer:t}=this;if(!e||!t||this._requiresManualStart||e.segmentId)return;let r=t.getEarliestTimestamp();r&&r<this._context.initialTimestamp&&(this._context.initialTimestamp=r)}_popEventContext(){let e={initialTimestamp:this._context.initialTimestamp,initialUrl:this._context.initialUrl,errorIds:Array.from(this._context.errorIds),traceIds:Array.from(this._context.traceIds),urls:this._context.urls};return this._clearContext(),e}async _runFlush(){let e=this.getSessionId();if(this.session&&this.eventBuffer&&e&&(await this._addPerformanceEntries(),this.eventBuffer?.hasEvents)){if((await rC(this),this.eventBuffer)&&e===this.getSessionId())try{this._updateInitialTimestampFromEventBuffer();let t=Date.now();if(t-this._context.initialTimestamp>this._options.maxReplayDuration+3e4)throw Error("Session is too long, not sending replay");let r=this._popEventContext(),n=this.session.segmentId++;this._maybeSaveSession();let i=await this.eventBuffer.finish();await rN({replayId:e,recordingData:i,segmentId:n,eventContext:r,session:this.session,timestamp:t,onError:e=>this.handleException(e)})}catch(t){this.handleException(t),this.stop({reason:"sendReplay"});let e=(0,S.KU)();e&&e.recordDroppedEvent(t instanceof rO?"ratelimit_backoff":"send_error","replay")}}}async _flush({force:e=!1}={}){if(!this._isEnabled&&!e||!this.checkAndHandleExpiredSession()||!this.session)return;let t=this.session.started,r=Date.now()-t;this._debouncedFlush.cancel();let n=r<this._options.minReplayDuration,i=r>this._options.maxReplayDuration+5e3;if(n||i){n&&this._debouncedFlush();return}let s=this.eventBuffer;s&&0===this.session.segmentId&&s.hasCheckout;let o=!!this._flushLock;this._flushLock||(this._flushLock=this._runFlush());try{await this._flushLock}catch(e){this.handleException(e)}finally{this._flushLock=void 0,o&&this._debouncedFlush()}}_maybeSaveSession(){this.session&&this._options.stickySession&&t8(this.session)}_onMutationHandler(e){let{ignoreMutations:t}=this._options._experiments;if(t?.length&&e.some(e=>{let r=function(e){if(!e)return null;try{return e.nodeType===e.ELEMENT_NODE?e:e.parentElement}catch{return null}}(e.target),n=t.join(",");return r?.matches(n)}))return!1;let r=e.length,n=this._options.mutationLimit,i=this._options.mutationBreadcrumbLimit,s=n&&r>n;if(r>i||s){let e=tP({category:"replay.mutations",data:{count:r,limit:s}});this._createCustomBreadcrumb(e)}return!s||(this.stop({reason:"mutationLimit",forceFlush:"session"===this.recordingMode}),!1)}}function rP(e,t){return[...e,...t].join(",")}let rF='img,image,svg,video,object,picture,embed,map,audio,link[rel="icon"],link[rel="apple-touch-icon"]',rU=["content-length","content-type","accept"],rB=!1,rz=e=>new rW(e);class rW{constructor({flushMinDelay:e=5e3,flushMaxDelay:t=5500,minReplayDuration:r=4999,maxReplayDuration:n=36e5,stickySession:i=!0,useCompression:s=!0,workerUrl:o,_experiments:a={},maskAllText:l=!0,maskAllInputs:c=!0,blockAllMedia:u=!0,mutationBreadcrumbLimit:d=750,mutationLimit:h=1e4,slowClickTimeout:p=7e3,slowClickIgnoreSelectors:f=[],networkDetailAllowUrls:m=[],networkDetailDenyUrls:y=[],networkCaptureBodies:g=!0,networkRequestHeaders:v=[],networkResponseHeaders:S=[],mask:_=[],maskAttributes:w=["title","placeholder","aria-label"],unmask:b=[],block:k=[],unblock:E=[],ignore:M=[],maskFn:I,beforeAddRecordingEvent:C,beforeErrorSampling:R,onError:T}={}){this.name="Replay";let D=function({mask:e,unmask:t,block:r,unblock:n,ignore:i}){return{maskTextSelector:rP(e,[".sentry-mask","[data-sentry-mask]"]),unmaskTextSelector:rP(t,[]),blockSelector:rP(r,[".sentry-block","[data-sentry-block]","base","iframe[srcdoc]:not([src])"]),unblockSelector:rP(n,[]),ignoreSelector:rP(i,[".sentry-ignore","[data-sentry-ignore]",'input[type="file"]'])}}({mask:_,unmask:b,block:k,unblock:E,ignore:M});if(this._recordingOptions={maskAllInputs:c,maskAllText:l,maskInputOptions:{password:!0},maskTextFn:I,maskInputFn:I,maskAttributeFn:(e,t,r)=>(function({el:e,key:t,maskAttributes:r,maskAllText:n,privacyOptions:i,value:s}){return!n||i.unmaskTextSelector&&e.matches(i.unmaskTextSelector)?s:r.includes(t)||"value"===t&&"INPUT"===e.tagName&&["submit","button"].includes(e.getAttribute("type")||"")?s.replace(/[\S]/g,"*"):s})({maskAttributes:w,maskAllText:l,privacyOptions:D,key:e,value:t,el:r}),...D,slimDOMOptions:"all",inlineStylesheet:!0,inlineImages:!1,collectFonts:!0,errorHandler:e=>{try{e.__rrweb__=!0}catch{}},recordCrossOriginIframes:!!a.recordCrossOriginIframes},this._initialOptions={flushMinDelay:e,flushMaxDelay:t,minReplayDuration:Math.min(r,15e3),maxReplayDuration:Math.min(n,36e5),stickySession:i,useCompression:s,workerUrl:o,blockAllMedia:u,maskAllInputs:c,maskAllText:l,mutationBreadcrumbLimit:d,mutationLimit:h,slowClickTimeout:p,slowClickIgnoreSelectors:f,networkDetailAllowUrls:m,networkDetailDenyUrls:y,networkCaptureBodies:g,networkRequestHeaders:rj(v),networkResponseHeaders:rj(S),beforeAddRecordingEvent:C,beforeErrorSampling:R,onError:T,_experiments:a},this._initialOptions.blockAllMedia&&(this._recordingOptions.blockSelector=this._recordingOptions.blockSelector?`${this._recordingOptions.blockSelector},${rF}`:rF),this._isInitialized&&(0,x.B)())throw Error("Multiple Sentry Session Replay instances are not supported");this._isInitialized=!0}get _isInitialized(){return rB}set _isInitialized(e){rB=e}afterAllSetup(e){(0,x.B)()&&!this._replay&&(this._setup(e),this._initialize(e))}start(){this._replay&&this._replay.start()}startBuffering(){this._replay&&this._replay.startBuffering()}stop(){return this._replay?this._replay.stop({forceFlush:"session"===this._replay.recordingMode}):Promise.resolve()}flush(e){return this._replay?this._replay.isEnabled()?this._replay.sendBufferedReplayOrFlush(e):(this._replay.start(),Promise.resolve()):Promise.resolve()}getReplayId(){if(this._replay?.isEnabled())return this._replay.getSessionId()}getRecordingMode(){if(this._replay?.isEnabled())return this._replay.recordingMode}_initialize(e){this._replay&&(this._maybeLoadFromReplayCanvasIntegration(e),this._replay.initializeSampling())}_setup(e){let t=function(e,t){let r=t.getOptions(),n={sessionSampleRate:0,errorSampleRate:0,...e},i=(0,D.i)(r.replaysSessionSampleRate),s=(0,D.i)(r.replaysOnErrorSampleRate);return null==i&&null==s&&(0,y.pq)(()=>{console.warn("Replay is disabled because neither `replaysSessionSampleRate` nor `replaysOnErrorSampleRate` are set.")}),null!=i&&(n.sessionSampleRate=i),null!=s&&(n.errorSampleRate=s),n}(this._initialOptions,e);this._replay=new rL({options:t,recordingOptions:this._recordingOptions})}_maybeLoadFromReplayCanvasIntegration(e){try{let t=e.getIntegrationByName("ReplayCanvas");if(!t)return;this._replay._canvas=t.getOptions()}catch{}}}function rj(e){return[...rU,...e.map(e=>e.toLowerCase())]}}}]);