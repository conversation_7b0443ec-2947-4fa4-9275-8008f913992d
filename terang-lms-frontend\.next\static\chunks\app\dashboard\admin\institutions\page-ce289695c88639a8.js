try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="daa43ecb-bed0-49d2-9177-9c7887c647ff",e._sentryDebugIdIdentifier="sentry-dbid-daa43ecb-bed0-49d2-9177-9c7887c647ff")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7893],{5917:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(71847).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},6191:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(71847).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},11010:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(71847).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},12833:(e,t,a)=>{"use strict";a.d(t,{I:()=>c,SQ:()=>l,_2:()=>u,hO:()=>m,lp:()=>x,mB:()=>p,rI:()=>i,ty:()=>o});var r=a(95155);a(12115);var s=a(47971),n=a(5917),d=a(64269);function i(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"dropdown-menu",...t,"data-sentry-element":"DropdownMenuPrimitive.Root","data-sentry-component":"DropdownMenu","data-sentry-source-file":"dropdown-menu.tsx"})}function o(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"dropdown-menu-trigger",...t,"data-sentry-element":"DropdownMenuPrimitive.Trigger","data-sentry-component":"DropdownMenuTrigger","data-sentry-source-file":"dropdown-menu.tsx"})}function l(e){let{className:t,sideOffset:a=4,...n}=e;return(0,r.jsx)(s.ZL,{"data-sentry-element":"DropdownMenuPrimitive.Portal","data-sentry-component":"DropdownMenuContent","data-sentry-source-file":"dropdown-menu.tsx",children:(0,r.jsx)(s.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...n,"data-sentry-element":"DropdownMenuPrimitive.Content","data-sentry-source-file":"dropdown-menu.tsx"})})}function c(e){let{...t}=e;return(0,r.jsx)(s.YJ,{"data-slot":"dropdown-menu-group",...t,"data-sentry-element":"DropdownMenuPrimitive.Group","data-sentry-component":"DropdownMenuGroup","data-sentry-source-file":"dropdown-menu.tsx"})}function u(e){let{className:t,inset:a,variant:n="default",...i}=e;return(0,r.jsx)(s.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":n,className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground focus:[&_svg:not([class*='text-'])]:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i,"data-sentry-element":"DropdownMenuPrimitive.Item","data-sentry-component":"DropdownMenuItem","data-sentry-source-file":"dropdown-menu.tsx"})}function m(e){let{className:t,children:a,checked:i,...o}=e;return(0,r.jsxs)(s.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:i,...o,"data-sentry-element":"DropdownMenuPrimitive.CheckboxItem","data-sentry-component":"DropdownMenuCheckboxItem","data-sentry-source-file":"dropdown-menu.tsx",children:[(0,r.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{"data-sentry-element":"DropdownMenuPrimitive.ItemIndicator","data-sentry-source-file":"dropdown-menu.tsx",children:(0,r.jsx)(n.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"dropdown-menu.tsx"})})}),a]})}function x(e){let{className:t,inset:a,...n}=e;return(0,r.jsx)(s.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,d.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...n,"data-sentry-element":"DropdownMenuPrimitive.Label","data-sentry-component":"DropdownMenuLabel","data-sentry-source-file":"dropdown-menu.tsx"})}function p(e){let{className:t,...a}=e;return(0,r.jsx)(s.wv,{"data-slot":"dropdown-menu-separator",className:(0,d.cn)("bg-border -mx-1 my-1 h-px",t),...a,"data-sentry-element":"DropdownMenuPrimitive.Separator","data-sentry-component":"DropdownMenuSeparator","data-sentry-source-file":"dropdown-menu.tsx"})}},15894:(e,t,a)=>{"use strict";a.d(t,{d:()=>s});var r=a(18720);function s(){return{toast:e=>{let{title:t,description:a,variant:s="default"}=e;"destructive"===s?r.oR.error(t,{description:a}):r.oR.success(t,{description:a})}}}},19408:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(71847).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},20764:(e,t,a)=>{"use strict";a.d(t,{$:()=>o,r:()=>i});var r=a(95155);a(12115);var s=a(32467),n=a(83101),d=a(64269);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:n,asChild:o=!1,...l}=e,c=o?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,d.cn)(i({variant:a,size:n,className:t})),...l,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},31936:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var r=a(95155);a(12115);var s=a(64269);function n(e){let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},35299:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(71847).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},37772:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(71847).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},45766:(e,t,a)=>{Promise.resolve().then(a.bind(a,66228))},64269:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n,z:()=>d});var r=a(2821),s=a(75889);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}function d(e){var t,a;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:s=0,sizeType:n="normal"}=r;if(0===e)return"0 Byte";let d=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,d)).toFixed(s)," ").concat("accurate"===n?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][d])?t:"Bytest":null!=(a=["Bytes","KB","MB","GB","TB"][d])?a:"Bytes")}},66094:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>l,ZB:()=>i,Zp:()=>n,aR:()=>d,wL:()=>c});var r=a(95155);a(12115);var s=a(64269);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},66228:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>j});var r=a(95155),s=a(12115),n=a(66094),d=a(20764),i=a(31936),o=a(88021),l=a(88864),c=a(12833),u=a(6191),m=a(86651),x=a(35299),p=a(19408),f=a(91169),h=a(37772),y=a(11010),b=a(71360),v=a(52619),g=a.n(v),w=a(15894);function j(){let[e,t]=(0,s.useState)(""),[a,v]=(0,s.useState)([]),[j,k]=(0,s.useState)(!0),[T,N]=(0,s.useState)(null),{toast:M}=(0,w.d)(),C=(0,s.useRef)(!0),_=(0,s.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";try{k(!0);let t=await fetch("/api/institutions?search=".concat(encodeURIComponent(e))),a=await t.json();a.success?v(a.data.institutions):M({title:"Error",description:a.error||"Failed to fetch institutions",variant:"destructive"})}catch(e){console.error("Error fetching institutions:",e),M({title:"Error",description:"Failed to fetch institutions",variant:"destructive"})}finally{k(!1)}},[]),A=async t=>{if(confirm("Are you sure you want to delete this institution? This action cannot be undone."))try{N(t);let a=await fetch("/api/institutions/".concat(t),{method:"DELETE"}),r=await a.json();r.success?(M({title:"Success",description:"Institution deleted successfully"}),_(e)):M({title:"Error",description:r.error||"Failed to delete institution",variant:"destructive"})}catch(e){console.error("Error deleting institution:",e),M({title:"Error",description:"Failed to delete institution",variant:"destructive"})}finally{N(null)}};(0,s.useEffect)(()=>{_()},[]),(0,s.useEffect)(()=>{if(C.current){C.current=!1;return}let t=setTimeout(()=>{_(e)},500);return()=>clearTimeout(t)},[e,_]);let D=e=>(0,r.jsx)(o.E,{variant:"paid"===e?"default":"destructive","data-sentry-element":"Badge","data-sentry-component":"getStatusBadge","data-sentry-source-file":"page.tsx",children:e}),B=e=>(0,r.jsx)(o.E,{variant:"enterprise"===e?"default":"pro"===e?"secondary":"outline","data-sentry-element":"Badge","data-sentry-component":"getPlanBadge","data-sentry-source-file":"page.tsx",children:e});return(0,r.jsxs)("div",{className:"space-y-6","data-sentry-component":"InstitutionsPage","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Institutions"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage all educational institutions on the platform"})]}),(0,r.jsx)(g(),{href:"/dashboard/admin/institutions/new","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)(d.$,{"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Plus","data-sentry-source-file":"page.tsx"}),"Add Institution"]})})]}),(0,r.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(n.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"All Institutions"}),(0,r.jsx)(n.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"View and manage all registered institutions"})]}),(0,r.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)("div",{className:"mb-4 flex items-center space-x-2",children:(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(m.A,{className:"text-muted-foreground absolute top-2.5 left-2 h-4 w-4","data-sentry-element":"Search","data-sentry-source-file":"page.tsx"}),(0,r.jsx)(i.p,{placeholder:"Search institutions...",value:e,onChange:e=>t(e.target.value),className:"pl-8","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]})}),(0,r.jsx)("div",{className:"rounded-md border",children:(0,r.jsxs)(l.Table,{"data-sentry-element":"Table","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(l.TableHeader,{"data-sentry-element":"TableHeader","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)(l.TableRow,{"data-sentry-element":"TableRow","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(l.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Institution"}),(0,r.jsx)(l.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Type"}),(0,r.jsx)(l.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Plan"}),(0,r.jsx)(l.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Students/Teachers"}),(0,r.jsx)(l.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Payment Status"}),(0,r.jsx)(l.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Due Date"}),(0,r.jsx)(l.TableHead,{className:"w-[70px]","data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Actions"})]})}),(0,r.jsx)(l.TableBody,{"data-sentry-element":"TableBody","data-sentry-source-file":"page.tsx",children:j?(0,r.jsx)(l.TableRow,{children:(0,r.jsxs)(l.TableCell,{colSpan:7,className:"py-8 text-center",children:[(0,r.jsx)(x.A,{className:"mx-auto h-6 w-6 animate-spin"}),(0,r.jsx)("p",{className:"text-muted-foreground mt-2 text-sm",children:"Loading institutions..."})]})}):0===a.length?(0,r.jsx)(l.TableRow,{children:(0,r.jsxs)(l.TableCell,{colSpan:7,className:"py-8 text-center",children:[(0,r.jsx)(p.A,{className:"text-muted-foreground mx-auto mb-4 h-12 w-12"}),(0,r.jsx)("h3",{className:"mb-2 text-sm font-semibold",children:"No institutions found"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4 text-sm",children:e?"Try adjusting your search terms.":"Get started by adding a new institution."}),!e&&(0,r.jsx)(g(),{href:"/dashboard/admin/institutions/new",children:(0,r.jsxs)(d.$,{children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Add Institution"]})})]})}):a.map(e=>(0,r.jsxs)(l.TableRow,{children:[(0,r.jsx)(l.TableCell,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(p.A,{className:"text-muted-foreground h-4 w-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:e.name}),(0,r.jsxs)("p",{className:"text-muted-foreground text-sm",children:["ID: ",e.id]})]})]})}),(0,r.jsx)(l.TableCell,{children:(0,r.jsx)(o.E,{variant:"outline",children:e.type})}),(0,r.jsx)(l.TableCell,{children:(0,r.jsxs)("div",{className:"space-y-1",children:[B(e.subscription_plan),(0,r.jsx)("p",{className:"text-muted-foreground text-xs",children:e.billing_cycle})]})}),(0,r.jsx)(l.TableCell,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(f.A,{className:"text-muted-foreground h-3 w-3"}),(0,r.jsxs)("span",{className:"text-sm",children:[e.student_count,"/",e.teacher_count]})]})}),(0,r.jsx)(l.TableCell,{children:D(e.payment_status)}),(0,r.jsx)(l.TableCell,{children:(0,r.jsx)("span",{className:"text-sm",children:e.payment_due_date?new Date(e.payment_due_date).toLocaleDateString():"N/A"})}),(0,r.jsx)(l.TableCell,{children:(0,r.jsxs)(c.rI,{children:[(0,r.jsx)(c.ty,{asChild:!0,children:(0,r.jsx)(d.$,{variant:"ghost",className:"h-8 w-8 p-0",disabled:T===e.id,children:T===e.id?(0,r.jsx)(x.A,{className:"h-4 w-4 animate-spin"}):(0,r.jsx)(h.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(c.SQ,{align:"end",children:[(0,r.jsx)(c._2,{asChild:!0,children:(0,r.jsxs)(g(),{href:"/dashboard/admin/institutions/".concat(e.id),children:[(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Edit"]})}),(0,r.jsxs)(c._2,{className:"text-red-600",onClick:()=>A(e.id),disabled:T===e.id,children:[(0,r.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},e.id))})]})})]})]})]})}},71360:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(71847).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},86651:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(71847).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},88021:(e,t,a)=>{"use strict";a.d(t,{E:()=>o});var r=a(95155);a(12115);var s=a(32467),n=a(83101),d=a(64269);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:a,asChild:n=!1,...o}=e,l=n?s.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,d.cn)(i({variant:a}),t),...o,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},88864:(e,t,a)=>{"use strict";a.d(t,{Table:()=>n,TableBody:()=>i,TableCell:()=>c,TableHead:()=>l,TableHeader:()=>d,TableRow:()=>o});var r=a(95155);a(12115);var s=a(64269);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto","data-sentry-component":"Table","data-sentry-source-file":"table.tsx",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",t),...a})})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",t),...a,"data-sentry-component":"TableHeader","data-sentry-source-file":"table.tsx"})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",t),...a,"data-sentry-component":"TableBody","data-sentry-source-file":"table.tsx"})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a,"data-sentry-component":"TableRow","data-sentry-source-file":"table.tsx"})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a,"data-sentry-component":"TableHead","data-sentry-source-file":"table.tsx"})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a,"data-sentry-component":"TableCell","data-sentry-source-file":"table.tsx"})}},91169:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(71847).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,7055,4736,660,8720,6093,7971,4850,8441,3840,7358],()=>t(45766)),_N_E=e.O()}]);