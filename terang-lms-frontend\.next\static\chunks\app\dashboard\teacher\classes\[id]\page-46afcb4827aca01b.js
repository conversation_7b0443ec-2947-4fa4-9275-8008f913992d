try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="3370e3e3-0fcb-4fb9-8d44-ab64e54fcdcd",e._sentryDebugIdIdentifier="sentry-dbid-3370e3e3-0fcb-4fb9-8d44-ab64e54fcdcd")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9955],{10489:(e,t,a)=>{"use strict";a.d(t,{b:()=>i});var s=a(12115),r=a(97602),n=a(95155),d=s.forwardRef((e,t)=>(0,n.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var i=d},17229:(e,t,a)=>{Promise.resolve().then(a.bind(a,49782))},20063:(e,t,a)=>{"use strict";var s=a(47260);a.o(s,"useParams")&&a.d(t,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(t,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(t,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(t,{useSearchParams:function(){return s.useSearchParams}})},20764:(e,t,a)=>{"use strict";a.d(t,{$:()=>l,r:()=>i});var s=a(95155);a(12115);var r=a(32467),n=a(83101),d=a(64269);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:n,asChild:l=!1,...o}=e,c=l?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,d.cn)(i({variant:a,size:n,className:t})),...o,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},31936:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var s=a(95155);a(12115);var r=a(64269);function n(e){let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},35299:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},35626:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},42526:(e,t,a)=>{"use strict";a.d(t,{J:()=>d});var s=a(95155);a(12115);var r=a(10489),n=a(64269);function d(e){let{className:t,...a}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a,"data-sentry-element":"LabelPrimitive.Root","data-sentry-component":"Label","data-sentry-source-file":"label.tsx"})}},46046:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},47254:(e,t,a)=>{"use strict";a.d(t,{T:()=>n});var s=a(95155);a(12115);var r=a(64269);function n(e){let{className:t,...a}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a,"data-sentry-component":"Textarea","data-sentry-source-file":"textarea.tsx"})}},47886:(e,t,a)=>{"use strict";a.d(t,{WG:()=>r,cl:()=>d,qs:()=>s});let s={setUser:e=>{localStorage.setItem("auth_user",JSON.stringify(e))},getUser:()=>{{let e=localStorage.getItem("auth_user");return e?JSON.parse(e):null}},removeUser:()=>{localStorage.removeItem("auth_user")},isAuthenticated:()=>null!==s.getUser(),hasRole:e=>{let t=s.getUser();return(null==t?void 0:t.role)===e},isSuperAdmin:()=>s.hasRole("super_admin"),isTeacher:()=>s.hasRole("teacher"),isStudent:()=>s.hasRole("student")},r=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},n=()=>{let e=s.getUser();return e||(window.location.href="/auth/sign-in",null)},d=e=>{let t=n();return t?t.role!==e?(window.location.href=r(t),null):t:null}},47937:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},49782:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var s=a(95155),r=a(12115),n=a(20063),d=a(66094),i=a(20764),l=a(31936),o=a(42526),c=a(47254),u=a(35299),h=a(35626),x=a(46046),m=a(91169),p=a(47937),f=a(52619),g=a.n(f),y=a(47886),v=a(18720);function b(){let e=(0,n.useRouter)(),t=(0,n.useParams)().id,[a,f]=(0,r.useState)(!1),[b,j]=(0,r.useState)(!0),[w,N]=(0,r.useState)(null),[C,k]=(0,r.useState)({name:"",description:""});(0,r.useEffect)(()=>{t&&B()},[t]);let B=async()=>{try{let a=y.qs.getUser();if(!a){v.oR.error("Please log in to edit classes"),e.push("/auth/sign-in");return}let s=await fetch("/api/classes/".concat(t,"?teacherId=").concat(a.id)),r=await s.json();r.success&&r.class?(N(r.class),k({name:r.class.name,description:r.class.description||""})):(v.oR.error(r.error||"Failed to fetch class data"),e.push("/dashboard/teacher/classes"))}catch(t){console.error("Error fetching class:",t),v.oR.error("Failed to fetch class data"),e.push("/dashboard/teacher/classes")}finally{j(!1)}},A=async a=>{a.preventDefault(),f(!0);try{let a=y.qs.getUser();if(!a)return void v.oR.error("Please log in to update classes");if(!C.name.trim())return void v.oR.error("Class name is required");let s=await fetch("/api/classes/".concat(t),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:C.name.trim(),description:C.description.trim(),teacherId:a.id})}),r=await s.json();r.success?(v.oR.success("Class updated successfully!"),e.push("/dashboard/teacher/classes")):v.oR.error(r.error||"Failed to update class")}catch(e){console.error("Error updating class:",e),v.oR.error("Failed to update class")}finally{f(!1)}},R=(e,t)=>{k(a=>({...a,[e]:t}))};return b?(0,s.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,s.jsx)(u.A,{className:"h-8 w-8 animate-spin"}),(0,s.jsx)("span",{className:"ml-2",children:"Loading class data..."})]}):w?(0,s.jsxs)("div",{className:"space-y-6","data-sentry-component":"EditClassPage","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(g(),{href:"/dashboard/teacher/classes","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(i.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ArrowLeft","data-sentry-source-file":"page.tsx"}),"Back"]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Edit Class"}),(0,s.jsxs)("p",{className:"text-muted-foreground",children:["Update the details for ",w.name]})]})]}),(0,s.jsxs)(d.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(d.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(d.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Class Details"}),(0,s.jsx)(d.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Update the basic information for this class"})]}),(0,s.jsx)(d.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)("form",{onSubmit:A,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{htmlFor:"name","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Class Name"}),(0,s.jsx)(l.p,{id:"name",value:C.name,onChange:e=>R("name",e.target.value),placeholder:"e.g., Mathematics Grade 10A",required:!0,"data-sentry-element":"Input","data-sentry-source-file":"page.tsx"}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"Choose a descriptive name that includes subject and grade level"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{htmlFor:"description","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Description"}),(0,s.jsx)(c.T,{id:"description",value:C.description,onChange:e=>R("description",e.target.value),placeholder:"Brief description of the class and its objectives",rows:4,"data-sentry-element":"Textarea","data-sentry-source-file":"page.tsx"}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"Provide a brief description of what this class covers"})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,s.jsx)(g(),{href:"/dashboard/teacher/classes","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(i.$,{variant:"outline",type:"button","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Cancel"})}),(0,s.jsxs)(i.$,{type:"submit",disabled:a,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[a?(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(x.A,{className:"mr-2 h-4 w-4"}),a?"Updating...":"Update Class"]})]})]})})]}),(0,s.jsxs)(d.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(d.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(d.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Class Statistics"}),(0,s.jsx)(d.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Current statistics for this class"})]}),(0,s.jsx)(d.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:w.studentCount}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"Students"})]}),(0,s.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:w.courseCount}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"Courses"})]}),(0,s.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:new Date(w.createdAt).toLocaleDateString()}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"Created"})]})]})})]}),(0,s.jsxs)(d.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(d.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(d.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Quick Actions"}),(0,s.jsx)(d.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Manage students and courses for this class"})]}),(0,s.jsx)(d.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,s.jsx)(g(),{href:"/dashboard/teacher/classes/".concat(t,"/students"),"data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(i.$,{variant:"outline","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Users","data-sentry-source-file":"page.tsx"}),"Manage Students"]})}),(0,s.jsx)(g(),{href:"/dashboard/teacher/classes/".concat(t,"/courses"),"data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(i.$,{variant:"outline","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4","data-sentry-element":"BookOpen","data-sentry-source-file":"page.tsx"}),"Assign Courses"]})})]})})]})]}):(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold",children:"Class not found"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-2",children:"The class you're looking for doesn't exist."}),(0,s.jsx)(g(),{href:"/dashboard/teacher/classes",children:(0,s.jsxs)(i.$,{className:"mt-4",children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Back to Classes"]})})]})})}},64269:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n,z:()=>d});var s=a(2821),r=a(75889);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}function d(e){var t,a;let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:r=0,sizeType:n="normal"}=s;if(0===e)return"0 Byte";let d=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,d)).toFixed(r)," ").concat("accurate"===n?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][d])?t:"Bytest":null!=(a=["Bytes","KB","MB","GB","TB"][d])?a:"Bytes")}},66094:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>o,ZB:()=>i,Zp:()=>n,aR:()=>d,wL:()=>c});var s=a(95155);a(12115);var r=a(64269);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},91169:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},97602:(e,t,a)=>{"use strict";a.d(t,{hO:()=>l,sG:()=>i});var s=a(12115),r=a(47650),n=a(32467),d=a(95155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let a=(0,n.TL)(`Primitive.${t}`),r=s.forwardRef((e,s)=>{let{asChild:r,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(r?a:t,{...n,ref:s})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function l(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,7055,8720,4850,8441,3840,7358],()=>t(17229)),_N_E=e.O()}]);