try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="91466771-c904-49cd-9eff-4802ea0a5d2f",e._sentryDebugIdIdentifier="sentry-dbid-91466771-c904-49cd-9eff-4802ea0a5d2f")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3798],{12014:(e,t,n)=>{var l,i,r,o,a,s,u,p,d,c,f,m,h,g,y,C,T,E,v,A,_,O,I,S,b,R,N,P,M,D,w,x,U,k,L,q,G,H,F,V,j,J,B,Y;let W,K;n.d(t,{M4:()=>nn,ZU:()=>r});class ${}function z(e,t){return e.replace(/\{([^}]+)\}/g,(e,n)=>{if(Object.prototype.hasOwnProperty.call(t,n)){let e=t[n];return null!=e?String(e):""}throw Error(`Key '${n}' not found in valueMap.`)})}function X(e,t,n){for(let l=0;l<t.length-1;l++){let i=t[l];if(i.endsWith("[]")){let r=i.slice(0,-2);if(!(r in e))if(Array.isArray(n))e[r]=Array.from({length:n.length},()=>({}));else throw Error(`Value must be a list given an array path ${i}`);if(Array.isArray(e[r])){let i=e[r];if(Array.isArray(n))for(let e=0;e<i.length;e++)X(i[e],t.slice(l+1),n[e]);else for(let e of i)X(e,t.slice(l+1),n)}return}if(i.endsWith("[0]")){let r=i.slice(0,-3);r in e||(e[r]=[{}]),X(e[r][0],t.slice(l+1),n);return}e[i]&&"object"==typeof e[i]||(e[i]={}),e=e[i]}let l=t[t.length-1],i=e[l];if(void 0!==i){if(!n||"object"==typeof n&&0===Object.keys(n).length||n===i)return;if("object"==typeof i&&"object"==typeof n&&null!==i&&null!==n)Object.assign(i,n);else throw Error(`Cannot set value for an existing key. Key: ${l}`)}else e[l]=n}function Q(e,t){try{if(1===t.length&&"_self"===t[0])return e;for(let n=0;n<t.length;n++){if("object"!=typeof e||null===e)return;let l=t[n];if(l.endsWith("[]")){let i=l.slice(0,-2);if(!(i in e))return;{let l=e[i];if(!Array.isArray(l))return;return l.map(e=>Q(e,t.slice(n+1)))}}e=e[l]}return e}catch(e){if(e instanceof TypeError)return;throw e}}function Z(e){if("string"!=typeof e)throw Error("fromImageBytes must be a string");return e}!function(e){e.OUTCOME_UNSPECIFIED="OUTCOME_UNSPECIFIED",e.OUTCOME_OK="OUTCOME_OK",e.OUTCOME_FAILED="OUTCOME_FAILED",e.OUTCOME_DEADLINE_EXCEEDED="OUTCOME_DEADLINE_EXCEEDED"}(l||(l={})),function(e){e.LANGUAGE_UNSPECIFIED="LANGUAGE_UNSPECIFIED",e.PYTHON="PYTHON"}(i||(i={})),function(e){e.TYPE_UNSPECIFIED="TYPE_UNSPECIFIED",e.STRING="STRING",e.NUMBER="NUMBER",e.INTEGER="INTEGER",e.BOOLEAN="BOOLEAN",e.ARRAY="ARRAY",e.OBJECT="OBJECT",e.NULL="NULL"}(r||(r={})),function(e){e.HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",e.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",e.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT",e.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",e.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",e.HARM_CATEGORY_CIVIC_INTEGRITY="HARM_CATEGORY_CIVIC_INTEGRITY",e.HARM_CATEGORY_IMAGE_HATE="HARM_CATEGORY_IMAGE_HATE",e.HARM_CATEGORY_IMAGE_DANGEROUS_CONTENT="HARM_CATEGORY_IMAGE_DANGEROUS_CONTENT",e.HARM_CATEGORY_IMAGE_HARASSMENT="HARM_CATEGORY_IMAGE_HARASSMENT",e.HARM_CATEGORY_IMAGE_SEXUALLY_EXPLICIT="HARM_CATEGORY_IMAGE_SEXUALLY_EXPLICIT"}(o||(o={})),function(e){e.HARM_BLOCK_METHOD_UNSPECIFIED="HARM_BLOCK_METHOD_UNSPECIFIED",e.SEVERITY="SEVERITY",e.PROBABILITY="PROBABILITY"}(a||(a={})),function(e){e.HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",e.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",e.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",e.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",e.BLOCK_NONE="BLOCK_NONE",e.OFF="OFF"}(s||(s={})),function(e){e.MODE_UNSPECIFIED="MODE_UNSPECIFIED",e.MODE_DYNAMIC="MODE_DYNAMIC"}(u||(u={})),function(e){e.AUTH_TYPE_UNSPECIFIED="AUTH_TYPE_UNSPECIFIED",e.NO_AUTH="NO_AUTH",e.API_KEY_AUTH="API_KEY_AUTH",e.HTTP_BASIC_AUTH="HTTP_BASIC_AUTH",e.GOOGLE_SERVICE_ACCOUNT_AUTH="GOOGLE_SERVICE_ACCOUNT_AUTH",e.OAUTH="OAUTH",e.OIDC_AUTH="OIDC_AUTH"}(p||(p={})),function(e){e.API_SPEC_UNSPECIFIED="API_SPEC_UNSPECIFIED",e.SIMPLE_SEARCH="SIMPLE_SEARCH",e.ELASTIC_SEARCH="ELASTIC_SEARCH"}(d||(d={})),function(e){e.ENVIRONMENT_UNSPECIFIED="ENVIRONMENT_UNSPECIFIED",e.ENVIRONMENT_BROWSER="ENVIRONMENT_BROWSER"}(c||(c={})),function(e){e.URL_RETRIEVAL_STATUS_UNSPECIFIED="URL_RETRIEVAL_STATUS_UNSPECIFIED",e.URL_RETRIEVAL_STATUS_SUCCESS="URL_RETRIEVAL_STATUS_SUCCESS",e.URL_RETRIEVAL_STATUS_ERROR="URL_RETRIEVAL_STATUS_ERROR",e.URL_RETRIEVAL_STATUS_PAYWALL="URL_RETRIEVAL_STATUS_PAYWALL",e.URL_RETRIEVAL_STATUS_UNSAFE="URL_RETRIEVAL_STATUS_UNSAFE"}(f||(f={})),function(e){e.FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",e.STOP="STOP",e.MAX_TOKENS="MAX_TOKENS",e.SAFETY="SAFETY",e.RECITATION="RECITATION",e.LANGUAGE="LANGUAGE",e.OTHER="OTHER",e.BLOCKLIST="BLOCKLIST",e.PROHIBITED_CONTENT="PROHIBITED_CONTENT",e.SPII="SPII",e.MALFORMED_FUNCTION_CALL="MALFORMED_FUNCTION_CALL",e.IMAGE_SAFETY="IMAGE_SAFETY",e.UNEXPECTED_TOOL_CALL="UNEXPECTED_TOOL_CALL"}(m||(m={})),function(e){e.HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",e.NEGLIGIBLE="NEGLIGIBLE",e.LOW="LOW",e.MEDIUM="MEDIUM",e.HIGH="HIGH"}(h||(h={})),function(e){e.HARM_SEVERITY_UNSPECIFIED="HARM_SEVERITY_UNSPECIFIED",e.HARM_SEVERITY_NEGLIGIBLE="HARM_SEVERITY_NEGLIGIBLE",e.HARM_SEVERITY_LOW="HARM_SEVERITY_LOW",e.HARM_SEVERITY_MEDIUM="HARM_SEVERITY_MEDIUM",e.HARM_SEVERITY_HIGH="HARM_SEVERITY_HIGH"}(g||(g={})),function(e){e.BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",e.SAFETY="SAFETY",e.OTHER="OTHER",e.BLOCKLIST="BLOCKLIST",e.PROHIBITED_CONTENT="PROHIBITED_CONTENT",e.IMAGE_SAFETY="IMAGE_SAFETY"}(y||(y={})),function(e){e.TRAFFIC_TYPE_UNSPECIFIED="TRAFFIC_TYPE_UNSPECIFIED",e.ON_DEMAND="ON_DEMAND",e.PROVISIONED_THROUGHPUT="PROVISIONED_THROUGHPUT"}(C||(C={})),function(e){e.MODALITY_UNSPECIFIED="MODALITY_UNSPECIFIED",e.TEXT="TEXT",e.IMAGE="IMAGE",e.AUDIO="AUDIO"}(T||(T={})),function(e){e.MEDIA_RESOLUTION_UNSPECIFIED="MEDIA_RESOLUTION_UNSPECIFIED",e.MEDIA_RESOLUTION_LOW="MEDIA_RESOLUTION_LOW",e.MEDIA_RESOLUTION_MEDIUM="MEDIA_RESOLUTION_MEDIUM",e.MEDIA_RESOLUTION_HIGH="MEDIA_RESOLUTION_HIGH"}(E||(E={})),function(e){e.JOB_STATE_UNSPECIFIED="JOB_STATE_UNSPECIFIED",e.JOB_STATE_QUEUED="JOB_STATE_QUEUED",e.JOB_STATE_PENDING="JOB_STATE_PENDING",e.JOB_STATE_RUNNING="JOB_STATE_RUNNING",e.JOB_STATE_SUCCEEDED="JOB_STATE_SUCCEEDED",e.JOB_STATE_FAILED="JOB_STATE_FAILED",e.JOB_STATE_CANCELLING="JOB_STATE_CANCELLING",e.JOB_STATE_CANCELLED="JOB_STATE_CANCELLED",e.JOB_STATE_PAUSED="JOB_STATE_PAUSED",e.JOB_STATE_EXPIRED="JOB_STATE_EXPIRED",e.JOB_STATE_UPDATING="JOB_STATE_UPDATING",e.JOB_STATE_PARTIALLY_SUCCEEDED="JOB_STATE_PARTIALLY_SUCCEEDED"}(v||(v={})),function(e){e.ADAPTER_SIZE_UNSPECIFIED="ADAPTER_SIZE_UNSPECIFIED",e.ADAPTER_SIZE_ONE="ADAPTER_SIZE_ONE",e.ADAPTER_SIZE_TWO="ADAPTER_SIZE_TWO",e.ADAPTER_SIZE_FOUR="ADAPTER_SIZE_FOUR",e.ADAPTER_SIZE_EIGHT="ADAPTER_SIZE_EIGHT",e.ADAPTER_SIZE_SIXTEEN="ADAPTER_SIZE_SIXTEEN",e.ADAPTER_SIZE_THIRTY_TWO="ADAPTER_SIZE_THIRTY_TWO"}(A||(A={})),function(e){e.FEATURE_SELECTION_PREFERENCE_UNSPECIFIED="FEATURE_SELECTION_PREFERENCE_UNSPECIFIED",e.PRIORITIZE_QUALITY="PRIORITIZE_QUALITY",e.BALANCED="BALANCED",e.PRIORITIZE_COST="PRIORITIZE_COST"}(_||(_={})),function(e){e.UNSPECIFIED="UNSPECIFIED",e.BLOCKING="BLOCKING",e.NON_BLOCKING="NON_BLOCKING"}(O||(O={})),function(e){e.MODE_UNSPECIFIED="MODE_UNSPECIFIED",e.MODE_DYNAMIC="MODE_DYNAMIC"}(I||(I={})),function(e){e.MODE_UNSPECIFIED="MODE_UNSPECIFIED",e.AUTO="AUTO",e.ANY="ANY",e.NONE="NONE"}(S||(S={})),function(e){e.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",e.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",e.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",e.BLOCK_NONE="BLOCK_NONE"}(b||(b={})),function(e){e.DONT_ALLOW="DONT_ALLOW",e.ALLOW_ADULT="ALLOW_ADULT",e.ALLOW_ALL="ALLOW_ALL"}(R||(R={})),function(e){e.auto="auto",e.en="en",e.ja="ja",e.ko="ko",e.hi="hi",e.zh="zh",e.pt="pt",e.es="es"}(N||(N={})),function(e){e.MASK_MODE_DEFAULT="MASK_MODE_DEFAULT",e.MASK_MODE_USER_PROVIDED="MASK_MODE_USER_PROVIDED",e.MASK_MODE_BACKGROUND="MASK_MODE_BACKGROUND",e.MASK_MODE_FOREGROUND="MASK_MODE_FOREGROUND",e.MASK_MODE_SEMANTIC="MASK_MODE_SEMANTIC"}(P||(P={})),function(e){e.CONTROL_TYPE_DEFAULT="CONTROL_TYPE_DEFAULT",e.CONTROL_TYPE_CANNY="CONTROL_TYPE_CANNY",e.CONTROL_TYPE_SCRIBBLE="CONTROL_TYPE_SCRIBBLE",e.CONTROL_TYPE_FACE_MESH="CONTROL_TYPE_FACE_MESH"}(M||(M={})),function(e){e.SUBJECT_TYPE_DEFAULT="SUBJECT_TYPE_DEFAULT",e.SUBJECT_TYPE_PERSON="SUBJECT_TYPE_PERSON",e.SUBJECT_TYPE_ANIMAL="SUBJECT_TYPE_ANIMAL",e.SUBJECT_TYPE_PRODUCT="SUBJECT_TYPE_PRODUCT"}(D||(D={})),function(e){e.EDIT_MODE_DEFAULT="EDIT_MODE_DEFAULT",e.EDIT_MODE_INPAINT_REMOVAL="EDIT_MODE_INPAINT_REMOVAL",e.EDIT_MODE_INPAINT_INSERTION="EDIT_MODE_INPAINT_INSERTION",e.EDIT_MODE_OUTPAINT="EDIT_MODE_OUTPAINT",e.EDIT_MODE_CONTROLLED_EDITING="EDIT_MODE_CONTROLLED_EDITING",e.EDIT_MODE_STYLE="EDIT_MODE_STYLE",e.EDIT_MODE_BGSWAP="EDIT_MODE_BGSWAP",e.EDIT_MODE_PRODUCT_IMAGE="EDIT_MODE_PRODUCT_IMAGE"}(w||(w={})),function(e){e.OPTIMIZED="OPTIMIZED",e.LOSSLESS="LOSSLESS"}(x||(x={})),function(e){e.STATE_UNSPECIFIED="STATE_UNSPECIFIED",e.PROCESSING="PROCESSING",e.ACTIVE="ACTIVE",e.FAILED="FAILED"}(U||(U={})),function(e){e.SOURCE_UNSPECIFIED="SOURCE_UNSPECIFIED",e.UPLOADED="UPLOADED",e.GENERATED="GENERATED"}(k||(k={})),function(e){e.MODALITY_UNSPECIFIED="MODALITY_UNSPECIFIED",e.TEXT="TEXT",e.IMAGE="IMAGE",e.VIDEO="VIDEO",e.AUDIO="AUDIO",e.DOCUMENT="DOCUMENT"}(L||(L={})),function(e){e.START_SENSITIVITY_UNSPECIFIED="START_SENSITIVITY_UNSPECIFIED",e.START_SENSITIVITY_HIGH="START_SENSITIVITY_HIGH",e.START_SENSITIVITY_LOW="START_SENSITIVITY_LOW"}(q||(q={})),function(e){e.END_SENSITIVITY_UNSPECIFIED="END_SENSITIVITY_UNSPECIFIED",e.END_SENSITIVITY_HIGH="END_SENSITIVITY_HIGH",e.END_SENSITIVITY_LOW="END_SENSITIVITY_LOW"}(G||(G={})),function(e){e.ACTIVITY_HANDLING_UNSPECIFIED="ACTIVITY_HANDLING_UNSPECIFIED",e.START_OF_ACTIVITY_INTERRUPTS="START_OF_ACTIVITY_INTERRUPTS",e.NO_INTERRUPTION="NO_INTERRUPTION"}(H||(H={})),function(e){e.TURN_COVERAGE_UNSPECIFIED="TURN_COVERAGE_UNSPECIFIED",e.TURN_INCLUDES_ONLY_ACTIVITY="TURN_INCLUDES_ONLY_ACTIVITY",e.TURN_INCLUDES_ALL_INPUT="TURN_INCLUDES_ALL_INPUT"}(F||(F={})),function(e){e.SCHEDULING_UNSPECIFIED="SCHEDULING_UNSPECIFIED",e.SILENT="SILENT",e.WHEN_IDLE="WHEN_IDLE",e.INTERRUPT="INTERRUPT"}(V||(V={})),function(e){e.SCALE_UNSPECIFIED="SCALE_UNSPECIFIED",e.C_MAJOR_A_MINOR="C_MAJOR_A_MINOR",e.D_FLAT_MAJOR_B_FLAT_MINOR="D_FLAT_MAJOR_B_FLAT_MINOR",e.D_MAJOR_B_MINOR="D_MAJOR_B_MINOR",e.E_FLAT_MAJOR_C_MINOR="E_FLAT_MAJOR_C_MINOR",e.E_MAJOR_D_FLAT_MINOR="E_MAJOR_D_FLAT_MINOR",e.F_MAJOR_D_MINOR="F_MAJOR_D_MINOR",e.G_FLAT_MAJOR_E_FLAT_MINOR="G_FLAT_MAJOR_E_FLAT_MINOR",e.G_MAJOR_E_MINOR="G_MAJOR_E_MINOR",e.A_FLAT_MAJOR_F_MINOR="A_FLAT_MAJOR_F_MINOR",e.A_MAJOR_G_FLAT_MINOR="A_MAJOR_G_FLAT_MINOR",e.B_FLAT_MAJOR_G_MINOR="B_FLAT_MAJOR_G_MINOR",e.B_MAJOR_A_FLAT_MINOR="B_MAJOR_A_FLAT_MINOR"}(j||(j={})),function(e){e.MUSIC_GENERATION_MODE_UNSPECIFIED="MUSIC_GENERATION_MODE_UNSPECIFIED",e.QUALITY="QUALITY",e.DIVERSITY="DIVERSITY",e.VOCALIZATION="VOCALIZATION"}(J||(J={})),function(e){e.PLAYBACK_CONTROL_UNSPECIFIED="PLAYBACK_CONTROL_UNSPECIFIED",e.PLAY="PLAY",e.PAUSE="PAUSE",e.STOP="STOP",e.RESET_CONTEXT="RESET_CONTEXT"}(B||(B={}));function ee(e){return"object"==typeof e&&null!==e&&("fileData"in e||"text"in e||"functionCall"in e||"functionResponse"in e||"inlineData"in e||"videoMetadata"in e||"codeExecutionResult"in e||"executableCode"in e)}class et{constructor(e){let t={};for(let n of e.headers.entries())t[n[0]]=n[1];this.headers=t,this.responseInternal=e}json(){return this.responseInternal.json()}}class en{get text(){var e,t,n,l,i,r,o,a;if((null==(l=null==(n=null==(t=null==(e=this.candidates)?void 0:e[0])?void 0:t.content)?void 0:n.parts)?void 0:l.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning text from the first one.");let s="",u=!1,p=[];for(let e of null!=(a=null==(o=null==(r=null==(i=this.candidates)?void 0:i[0])?void 0:r.content)?void 0:o.parts)?a:[]){for(let[t,n]of Object.entries(e))"text"!==t&&"thought"!==t&&(null!==n||void 0!==n)&&p.push(t);if("string"==typeof e.text){if("boolean"==typeof e.thought&&e.thought)continue;u=!0,s+=e.text}}return p.length>0&&console.warn(`there are non-text parts ${p} in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.`),u?s:void 0}get data(){var e,t,n,l,i,r,o,a;if((null==(l=null==(n=null==(t=null==(e=this.candidates)?void 0:e[0])?void 0:t.content)?void 0:n.parts)?void 0:l.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning data from the first one.");let s="",u=[];for(let e of null!=(a=null==(o=null==(r=null==(i=this.candidates)?void 0:i[0])?void 0:r.content)?void 0:o.parts)?a:[]){for(let[t,n]of Object.entries(e))"inlineData"!==t&&(null!==n||void 0!==n)&&u.push(t);e.inlineData&&"string"==typeof e.inlineData.data&&(s+=atob(e.inlineData.data))}return u.length>0&&console.warn(`there are non-data parts ${u} in the response, returning concatenation of all data parts. Please refer to the non data parts for a full response from model.`),s.length>0?btoa(s):void 0}get functionCalls(){var e,t,n,l,i,r,o,a;if((null==(l=null==(n=null==(t=null==(e=this.candidates)?void 0:e[0])?void 0:t.content)?void 0:n.parts)?void 0:l.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning function calls from the first one.");let s=null==(a=null==(o=null==(r=null==(i=this.candidates)?void 0:i[0])?void 0:r.content)?void 0:o.parts)?void 0:a.filter(e=>e.functionCall).map(e=>e.functionCall).filter(e=>void 0!==e);if((null==s?void 0:s.length)!==0)return s}get executableCode(){var e,t,n,l,i,r,o,a,s;if((null==(l=null==(n=null==(t=null==(e=this.candidates)?void 0:e[0])?void 0:t.content)?void 0:n.parts)?void 0:l.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning executable code from the first one.");let u=null==(a=null==(o=null==(r=null==(i=this.candidates)?void 0:i[0])?void 0:r.content)?void 0:o.parts)?void 0:a.filter(e=>e.executableCode).map(e=>e.executableCode).filter(e=>void 0!==e);if((null==u?void 0:u.length)!==0)return null==(s=null==u?void 0:u[0])?void 0:s.code}get codeExecutionResult(){var e,t,n,l,i,r,o,a,s;if((null==(l=null==(n=null==(t=null==(e=this.candidates)?void 0:e[0])?void 0:t.content)?void 0:n.parts)?void 0:l.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning code execution result from the first one.");let u=null==(a=null==(o=null==(r=null==(i=this.candidates)?void 0:i[0])?void 0:r.content)?void 0:o.parts)?void 0:a.filter(e=>e.codeExecutionResult).map(e=>e.codeExecutionResult).filter(e=>void 0!==e);if((null==u?void 0:u.length)!==0)return null==(s=null==u?void 0:u[0])?void 0:s.output}}class el{}class ei{}class er{}class eo{}class ea{}class es{}class eu{}class ep{}class ed{}class ec{}class ef{}class em{}class eh{}class eg{}class ey{}class eC{}class eT{}class eE{get text(){var e,t,n;let l="",i=!1,r=[];for(let o of null!=(n=null==(t=null==(e=this.serverContent)?void 0:e.modelTurn)?void 0:t.parts)?n:[]){for(let[e,t]of Object.entries(o))"text"!==e&&"thought"!==e&&null!==t&&r.push(e);if("string"==typeof o.text){if("boolean"==typeof o.thought&&o.thought)continue;i=!0,l+=o.text}}return r.length>0&&console.warn(`there are non-text parts ${r} in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.`),i?l:void 0}get data(){var e,t,n;let l="",i=[];for(let r of null!=(n=null==(t=null==(e=this.serverContent)?void 0:e.modelTurn)?void 0:t.parts)?n:[]){for(let[e,t]of Object.entries(r))"inlineData"!==e&&null!==t&&i.push(e);r.inlineData&&"string"==typeof r.inlineData.data&&(l+=atob(r.inlineData.data))}return i.length>0&&console.warn(`there are non-data parts ${i} in the response, returning concatenation of all data parts. Please refer to the non data parts for a full response from model.`),l.length>0?btoa(l):void 0}}class ev{_fromAPIResponse({apiResponse:e,isVertexAI:t}){let n=new ev;if(n.name=e.name,n.metadata=e.metadata,n.done=e.done,n.error=e.error,t){let t=e.response;if(t){let e=new ec,l=t.videos;e.generatedVideos=null==l?void 0:l.map(e=>({video:{uri:e.gcsUri,videoBytes:e.bytesBase64Encoded?Z(e.bytesBase64Encoded):void 0,mimeType:e.mimeType}})),e.raiMediaFilteredCount=t.raiMediaFilteredCount,e.raiMediaFilteredReasons=t.raiMediaFilteredReasons,n.response=e}}else{let t=e.response;if(t){let e=new ec,l=t.generateVideoResponse,i=null==l?void 0:l.generatedSamples;e.generatedVideos=null==i?void 0:i.map(e=>{let t=e.video;return{video:{uri:null==t?void 0:t.uri,videoBytes:(null==t?void 0:t.encodedVideo)?Z(null==t?void 0:t.encodedVideo):void 0,mimeType:e.encoding}}}),e.raiMediaFilteredCount=null==l?void 0:l.raiMediaFilteredCount,e.raiMediaFilteredReasons=null==l?void 0:l.raiMediaFilteredReasons,n.response=e}}return n}}class eA{get audioChunk(){if(this.serverContent&&this.serverContent.audioChunks&&this.serverContent.audioChunks.length>0)return this.serverContent.audioChunks[0]}}function e_(e,t){if(!t||"string"!=typeof t)throw Error("model is required and must be a string");if(e.isVertexAI())if(t.startsWith("publishers/")||t.startsWith("projects/")||t.startsWith("models/"))return t;else{if(!(t.indexOf("/")>=0))return`publishers/google/models/${t}`;let e=t.split("/",2);return`publishers/${e[0]}/models/${e[1]}`}return t.startsWith("models/")||t.startsWith("tunedModels/")?t:`models/${t}`}function eO(e,t){let n=e_(e,t);return n?n.startsWith("publishers/")&&e.isVertexAI()?`projects/${e.getProject()}/locations/${e.getLocation()}/${n}`:n.startsWith("models/")&&e.isVertexAI()?`projects/${e.getProject()}/locations/${e.getLocation()}/publishers/google/${n}`:n:""}function eI(e){return Array.isArray(e)?e.map(e=>eS(e)):[eS(e)]}function eS(e){if("object"==typeof e&&null!==e)return e;throw Error(`Could not parse input as Blob. Unsupported blob type: ${typeof e}`)}function eb(e){let t=eS(e);if(t.mimeType&&t.mimeType.startsWith("image/"))return t;throw Error(`Unsupported mime type: ${t.mimeType}`)}function eR(e){let t=eS(e);if(t.mimeType&&t.mimeType.startsWith("audio/"))return t;throw Error(`Unsupported mime type: ${t.mimeType}`)}function eN(e){if(null==e)throw Error("PartUnion is required");if("object"==typeof e)return e;if("string"==typeof e)return{text:e};throw Error(`Unsupported part type: ${typeof e}`)}function eP(e){if(null==e||Array.isArray(e)&&0===e.length)throw Error("PartListUnion is required");return Array.isArray(e)?e.map(e=>eN(e)):[eN(e)]}function eM(e){return null!=e&&"object"==typeof e&&"parts"in e&&Array.isArray(e.parts)}function eD(e){return null!=e&&"object"==typeof e&&"functionCall"in e}function ew(e){return null!=e&&"object"==typeof e&&"functionResponse"in e}function ex(e){if(null==e)throw Error("ContentUnion is required");return eM(e)?e:{role:"user",parts:eP(e)}}function eU(e,t){if(!t)return[];if(e.isVertexAI()&&Array.isArray(t))return t.flatMap(e=>{let t=ex(e);return t.parts&&t.parts.length>0&&void 0!==t.parts[0].text?[t.parts[0].text]:[]});if(e.isVertexAI()){let e=ex(t);return e.parts&&e.parts.length>0&&void 0!==e.parts[0].text?[e.parts[0].text]:[]}return Array.isArray(t)?t.map(e=>ex(e)):[ex(t)]}function ek(e){if(null==e||Array.isArray(e)&&0===e.length)throw Error("contents are required");if(!Array.isArray(e)){if(eD(e)||ew(e))throw Error("To specify functionCall or functionResponse parts, please wrap them in a Content object, specifying the role for them");return[ex(e)]}let t=[],n=[],l=eM(e[0]);for(let i of e){let e=eM(i);if(e!=l)throw Error("Mixing Content and Parts is not supported, please group the parts into a the appropriate Content objects and specify the roles for them");if(e)t.push(i);else if(eD(i)||ew(i))throw Error("To specify functionCall or functionResponse parts, please wrap them, and any other parts, in Content objects as appropriate, specifying the role for them");else n.push(i)}return l||t.push({role:"user",parts:eP(n)}),t}function eL(e){let t={},n=["items"],l=["anyOf"],i=["properties"];if(e.type&&e.anyOf)throw Error("type and anyOf cannot be both populated.");let o=e.anyOf;for(let[a,s]of(null!=o&&2==o.length&&("null"===o[0].type?(t.nullable=!0,e=o[1]):"null"===o[1].type&&(t.nullable=!0,e=o[0])),e.type instanceof Array&&function(e,t){e.includes("null")&&(t.nullable=!0);let n=e.filter(e=>"null"!==e);if(1===n.length)t.type=Object.values(r).includes(n[0].toUpperCase())?n[0].toUpperCase():r.TYPE_UNSPECIFIED;else for(let e of(t.anyOf=[],n))t.anyOf.push({type:Object.values(r).includes(e.toUpperCase())?e.toUpperCase():r.TYPE_UNSPECIFIED})}(e.type,t),Object.entries(e)))if(null!=s)if("type"==a){if("null"===s)throw Error("type: null can not be the only possible type for the field.");if(s instanceof Array)continue;t.type=Object.values(r).includes(s.toUpperCase())?s.toUpperCase():r.TYPE_UNSPECIFIED}else if(n.includes(a))t[a]=eL(s);else if(l.includes(a)){let e=[];for(let n of s){if("null"==n.type){t.nullable=!0;continue}e.push(eL(n))}t[a]=e}else if(i.includes(a)){let e={};for(let[t,n]of Object.entries(s))e[t]=eL(n);t[a]=e}else{if("additionalProperties"===a)continue;t[a]=s}return t}function eq(e){if("object"==typeof e)return e;if("string"==typeof e)return{voiceConfig:{prebuiltVoiceConfig:{voiceName:e}}};throw Error(`Unsupported speechConfig type: ${typeof e}`)}function eG(e){if("multiSpeakerVoiceConfig"in e)throw Error("multiSpeakerVoiceConfig is not supported in the live API.");return e}function eH(e){if(e.functionDeclarations)for(let t of e.functionDeclarations)t.parameters&&(Object.keys(t.parameters).includes("$schema")?t.parametersJsonSchema||(t.parametersJsonSchema=t.parameters,delete t.parameters):t.parameters=eL(t.parameters)),t.response&&(Object.keys(t.response).includes("$schema")?t.responseJsonSchema||(t.responseJsonSchema=t.response,delete t.response):t.response=eL(t.response));return e}function eF(e){if(null==e)throw Error("tools is required");if(!Array.isArray(e))throw Error("tools is required and must be an array of Tools");let t=[];for(let n of e)t.push(n);return t}function eV(e,t){if("string"!=typeof t)throw Error("name must be a string");return function(e,t,n,l=1){let i=!t.startsWith(`${n}/`)&&t.split("/").length===l;if(e.isVertexAI())if(t.startsWith("projects/"))return t;else if(t.startsWith("locations/"))return`projects/${e.getProject()}/${t}`;else if(t.startsWith(`${n}/`))return`projects/${e.getProject()}/locations/${e.getLocation()}/${t}`;else if(i)return`projects/${e.getProject()}/locations/${e.getLocation()}/${n}/${t}`;else return t;return i?`${n}/${t}`:t}(e,t,"cachedContents")}function ej(e){switch(e){case"STATE_UNSPECIFIED":return"JOB_STATE_UNSPECIFIED";case"CREATING":return"JOB_STATE_RUNNING";case"ACTIVE":return"JOB_STATE_SUCCEEDED";case"FAILED":return"JOB_STATE_FAILED";default:return e}}function eJ(e){var t;let n;if(null!=e&&"object"==typeof e&&"name"in e&&(n=e.name),null==e||"object"!=typeof e||!("uri"in e)||void 0!==(n=e.uri)){if(null==e||"object"!=typeof e||!("video"in e)||void 0!==(n=null==(t=e.video)?void 0:t.uri)){if("string"==typeof e&&(n=e),void 0===n)throw Error("Could not extract file name from the provided input.");if(n.startsWith("https://")){let e=n.split("files/")[1].match(/[a-z0-9]+/);if(null===e)throw Error(`Could not extract file name from URI ${n}`);n=e[0]}else n.startsWith("files/")&&(n=n.split("files/")[1]);return n}}}function eB(e,t){let n;return e.isVertexAI()?t?"publishers/google/models":"models":t?"models":"tunedModels"}function eY(e){for(let l of["models","tunedModels","publisherModels"]){var t,n;if(t=e,n=l,null!==t&&"object"==typeof t&&n in t)return e[l]}return[]}function eW(e,t){if("string"==typeof t||Array.isArray(t)){if(Array.isArray(t))return{inlinedRequests:t};else if("string"==typeof t){if(t.startsWith("gs://"))return{format:"jsonl",gcsUri:[t]};else if(t.startsWith("bq://"))return{format:"bigquery",bigqueryUri:t};else if(t.startsWith("files/"))return{fileName:t}}}else{if(e&&e.isVertexAI()){if(t.gcsUri&&t.bigqueryUri)throw Error("Only one of `gcsUri` or `bigqueryUri` can be set.");else if(!t.gcsUri&&!t.bigqueryUri)throw Error("One of `gcsUri` or `bigqueryUri` must be set.")}else if(t.inlinedRequests&&t.fileName)throw Error("Only one of `inlinedRequests` or `fileName` can be set.");else if(!t.inlinedRequests&&!t.fileName)throw Error("One of `inlinedRequests` or `fileName` must be set.");return t}throw Error(`Unsupported source: ${t}`)}function eK(e,t){if(!e.isVertexAI()){if(/batches\/[^/]+$/.test(t))return t.split("/").pop();throw Error(`Invalid batch job name: ${t}.`)}if(/^projects\/[^/]+\/locations\/[^/]+\/batchPredictionJobs\/[^/]+$/.test(t))return t.split("/").pop();if(/^\d+$/.test(t))return t;throw Error(`Invalid batch job name: ${t}.`)}function e$(e){if("BATCH_STATE_UNSPECIFIED"===e)return"JOB_STATE_UNSPECIFIED";if("BATCH_STATE_PENDING"===e)return"JOB_STATE_PENDING";if("BATCH_STATE_SUCCEEDED"===e)return"JOB_STATE_SUCCEEDED";if("BATCH_STATE_FAILED"===e)return"JOB_STATE_FAILED";if("BATCH_STATE_CANCELLED"===e)return"JOB_STATE_CANCELLED";else return e}function ez(e){let t={},n=Q(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["videoMetadata"]);null!=n&&X(t,["videoMetadata"],function(e){let t={},n=Q(e,["fps"]);null!=n&&X(t,["fps"],n);let l=Q(e,["endOffset"]);null!=l&&X(t,["endOffset"],l);let i=Q(e,["startOffset"]);return null!=i&&X(t,["startOffset"],i),t}(n));let l=Q(e,["thought"]);null!=l&&X(t,["thought"],l);let i=Q(e,["inlineData"]);null!=i&&X(t,["inlineData"],function(e){let t={};if(void 0!==Q(e,["displayName"]))throw Error("displayName parameter is not supported in Gemini API.");let n=Q(e,["data"]);null!=n&&X(t,["data"],n);let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(i));let r=Q(e,["fileData"]);null!=r&&X(t,["fileData"],function(e){let t={};if(void 0!==Q(e,["displayName"]))throw Error("displayName parameter is not supported in Gemini API.");let n=Q(e,["fileUri"]);null!=n&&X(t,["fileUri"],n);let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(r));let o=Q(e,["thoughtSignature"]);null!=o&&X(t,["thoughtSignature"],o);let a=Q(e,["codeExecutionResult"]);null!=a&&X(t,["codeExecutionResult"],a);let s=Q(e,["executableCode"]);null!=s&&X(t,["executableCode"],s);let u=Q(e,["functionCall"]);null!=u&&X(t,["functionCall"],u);let p=Q(e,["functionResponse"]);null!=p&&X(t,["functionResponse"],p);let d=Q(e,["text"]);return null!=d&&X(t,["text"],d),t})(e))),X(t,["parts"],e)}let l=Q(e,["role"]);return null!=l&&X(t,["role"],l),t}function eX(e){let t={},n=Q(e,["prebuiltVoiceConfig"]);return null!=n&&X(t,["prebuiltVoiceConfig"],function(e){let t={},n=Q(e,["voiceName"]);return null!=n&&X(t,["voiceName"],n),t}(n)),t}function eQ(e){let t={},n=Q(e,["details"]);null!=n&&X(t,["details"],n);let l=Q(e,["code"]);null!=l&&X(t,["code"],l);let i=Q(e,["message"]);return null!=i&&X(t,["message"],i),t}function eZ(e){let t={},n=Q(e,["name"]);null!=n&&X(t,["name"],n);let l=Q(e,["metadata","displayName"]);null!=l&&X(t,["displayName"],l);let i=Q(e,["metadata","state"]);null!=i&&X(t,["state"],e$(i));let r=Q(e,["metadata","createTime"]);null!=r&&X(t,["createTime"],r);let o=Q(e,["metadata","endTime"]);null!=o&&X(t,["endTime"],o);let a=Q(e,["metadata","updateTime"]);null!=a&&X(t,["updateTime"],a);let s=Q(e,["metadata","model"]);null!=s&&X(t,["model"],s);let u=Q(e,["metadata","output"]);return null!=u&&X(t,["dest"],function(e){let t={},n=Q(e,["responsesFile"]);null!=n&&X(t,["fileName"],n);let l=Q(e,["inlinedResponses","inlinedResponses"]);if(null!=l){let e=l;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["response"]);null!=n&&X(t,["response"],function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["candidates"]);if(null!=l){let e=l;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["content"]);null!=n&&X(t,["content"],function(e){let t={},n=Q(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["videoMetadata"]);null!=n&&X(t,["videoMetadata"],function(e){let t={},n=Q(e,["fps"]);null!=n&&X(t,["fps"],n);let l=Q(e,["endOffset"]);null!=l&&X(t,["endOffset"],l);let i=Q(e,["startOffset"]);return null!=i&&X(t,["startOffset"],i),t}(n));let l=Q(e,["thought"]);null!=l&&X(t,["thought"],l);let i=Q(e,["inlineData"]);null!=i&&X(t,["inlineData"],function(e){let t={},n=Q(e,["data"]);null!=n&&X(t,["data"],n);let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(i));let r=Q(e,["fileData"]);null!=r&&X(t,["fileData"],function(e){let t={},n=Q(e,["fileUri"]);null!=n&&X(t,["fileUri"],n);let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(r));let o=Q(e,["thoughtSignature"]);null!=o&&X(t,["thoughtSignature"],o);let a=Q(e,["codeExecutionResult"]);null!=a&&X(t,["codeExecutionResult"],a);let s=Q(e,["executableCode"]);null!=s&&X(t,["executableCode"],s);let u=Q(e,["functionCall"]);null!=u&&X(t,["functionCall"],u);let p=Q(e,["functionResponse"]);null!=p&&X(t,["functionResponse"],p);let d=Q(e,["text"]);return null!=d&&X(t,["text"],d),t})(e))),X(t,["parts"],e)}let l=Q(e,["role"]);return null!=l&&X(t,["role"],l),t}(n));let l=Q(e,["citationMetadata"]);null!=l&&X(t,["citationMetadata"],function(e){let t={},n=Q(e,["citationSources"]);return null!=n&&X(t,["citations"],n),t}(l));let i=Q(e,["tokenCount"]);null!=i&&X(t,["tokenCount"],i);let r=Q(e,["finishReason"]);null!=r&&X(t,["finishReason"],r);let o=Q(e,["urlContextMetadata"]);null!=o&&X(t,["urlContextMetadata"],function(e){let t={},n=Q(e,["urlMetadata"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["retrievedUrl"]);null!=n&&X(t,["retrievedUrl"],n);let l=Q(e,["urlRetrievalStatus"]);return null!=l&&X(t,["urlRetrievalStatus"],l),t})(e))),X(t,["urlMetadata"],e)}return t}(o));let a=Q(e,["avgLogprobs"]);null!=a&&X(t,["avgLogprobs"],a);let s=Q(e,["groundingMetadata"]);null!=s&&X(t,["groundingMetadata"],s);let u=Q(e,["index"]);null!=u&&X(t,["index"],u);let p=Q(e,["logprobsResult"]);null!=p&&X(t,["logprobsResult"],p);let d=Q(e,["safetyRatings"]);return null!=d&&X(t,["safetyRatings"],d),t})(e))),X(t,["candidates"],e)}let i=Q(e,["modelVersion"]);null!=i&&X(t,["modelVersion"],i);let r=Q(e,["promptFeedback"]);null!=r&&X(t,["promptFeedback"],r);let o=Q(e,["responseId"]);null!=o&&X(t,["responseId"],o);let a=Q(e,["usageMetadata"]);return null!=a&&X(t,["usageMetadata"],a),t}(n));let l=Q(e,["error"]);return null!=l&&X(t,["error"],eQ(l)),t})(e))),X(t,["inlinedResponses"],e)}return t}(u)),t}function e0(e){let t={},n=Q(e,["details"]);null!=n&&X(t,["details"],n);let l=Q(e,["code"]);null!=l&&X(t,["code"],l);let i=Q(e,["message"]);return null!=i&&X(t,["message"],i),t}function e1(e){let t={},n=Q(e,["name"]);null!=n&&X(t,["name"],n);let l=Q(e,["displayName"]);null!=l&&X(t,["displayName"],l);let i=Q(e,["state"]);null!=i&&X(t,["state"],e$(i));let r=Q(e,["error"]);null!=r&&X(t,["error"],e0(r));let o=Q(e,["createTime"]);null!=o&&X(t,["createTime"],o);let a=Q(e,["startTime"]);null!=a&&X(t,["startTime"],a);let s=Q(e,["endTime"]);null!=s&&X(t,["endTime"],s);let u=Q(e,["updateTime"]);null!=u&&X(t,["updateTime"],u);let p=Q(e,["model"]);null!=p&&X(t,["model"],p);let d=Q(e,["inputConfig"]);null!=d&&X(t,["src"],function(e){let t={},n=Q(e,["instancesFormat"]);null!=n&&X(t,["format"],n);let l=Q(e,["gcsSource","uris"]);null!=l&&X(t,["gcsUri"],l);let i=Q(e,["bigquerySource","inputUri"]);return null!=i&&X(t,["bigqueryUri"],i),t}(d));let c=Q(e,["outputConfig"]);return null!=c&&X(t,["dest"],function(e){let t={},n=Q(e,["predictionsFormat"]);null!=n&&X(t,["format"],n);let l=Q(e,["gcsDestination","outputUriPrefix"]);null!=l&&X(t,["gcsUri"],l);let i=Q(e,["bigqueryDestination","outputUri"]);return null!=i&&X(t,["bigqueryUri"],i),t}(c)),t}!function(e){e.PAGED_ITEM_BATCH_JOBS="batchJobs",e.PAGED_ITEM_MODELS="models",e.PAGED_ITEM_TUNING_JOBS="tuningJobs",e.PAGED_ITEM_FILES="files",e.PAGED_ITEM_CACHED_CONTENTS="cachedContents"}(Y||(Y={}));class e4{constructor(e,t,n,l){this.pageInternal=[],this.paramsInternal={},this.requestInternal=t,this.init(e,n,l)}init(e,t,n){var l,i;this.nameInternal=e,this.pageInternal=t[this.nameInternal]||[],this.sdkHttpResponseInternal=null==t?void 0:t.sdkHttpResponse,this.idxInternal=0;let r={config:{}};(r=n&&0!==Object.keys(n).length?"object"==typeof n?Object.assign({},n):n:{config:{}}).config&&(r.config.pageToken=t.nextPageToken),this.paramsInternal=r,this.pageInternalSize=null!=(i=null==(l=r.config)?void 0:l.pageSize)?i:this.pageInternal.length}initNextPage(e){this.init(this.nameInternal,e,this.paramsInternal)}get page(){return this.pageInternal}get name(){return this.nameInternal}get pageSize(){return this.pageInternalSize}get sdkHttpResponse(){return this.sdkHttpResponseInternal}get params(){return this.paramsInternal}get pageLength(){return this.pageInternal.length}getItem(e){return this.pageInternal[e]}[Symbol.asyncIterator](){return{next:async()=>{if(this.idxInternal>=this.pageLength)if(!this.hasNextPage())return{value:void 0,done:!0};else await this.nextPage();let e=this.getItem(this.idxInternal);return this.idxInternal+=1,{value:e,done:!1}},return:async()=>({value:void 0,done:!0})}}async nextPage(){if(!this.hasNextPage())throw Error("No more pages to fetch.");let e=await this.requestInternal(this.params);return this.initNextPage(e),this.page}hasNextPage(){var e;return(null==(e=this.params.config)?void 0:e.pageToken)!==void 0}}class e6 extends ${constructor(e){super(),this.apiClient=e,this.create=async e=>{if(this.apiClient.isVertexAI()){let t=Date.now().toString();if(Array.isArray(e.src))throw Error("InlinedRequest[] is not supported in Vertex AI. Please use Google Cloud Storage URI or BigQuery URI instead.");if(e.config=e.config||{},void 0===e.config.displayName&&(e.config.displayName="genaiBatchJob_${timestampStr}"),void 0===e.config.dest&&"string"==typeof e.src)if(e.src.startsWith("gs://")&&e.src.endsWith(".jsonl"))e.config.dest=`${e.src.slice(0,-6)}/dest`;else if(e.src.startsWith("bq://"))e.config.dest=`${e.src}_dest_${t}`;else throw Error("Unsupported source:"+e.src)}return await this.createInternal(e)},this.list=async(e={})=>new e4(Y.PAGED_ITEM_BATCH_JOBS,e=>this.listInternal(e),await this.listInternal(e),e)}async createInternal(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["model"],e_(e,l));let i=Q(t,["src"]);null!=i&&X(n,["inputConfig"],function(e){let t={},n=Q(e,["format"]);null!=n&&X(t,["instancesFormat"],n);let l=Q(e,["gcsUri"]);null!=l&&X(t,["gcsSource","uris"],l);let i=Q(e,["bigqueryUri"]);if(null!=i&&X(t,["bigquerySource","inputUri"],i),void 0!==Q(e,["fileName"]))throw Error("fileName parameter is not supported in Vertex AI.");if(void 0!==Q(e,["inlinedRequests"]))throw Error("inlinedRequests parameter is not supported in Vertex AI.");return t}(eW(e,i)));let r=Q(t,["config"]);return null!=r&&X(n,["config"],function(e,t){let n=Q(e,["displayName"]);void 0!==t&&null!=n&&X(t,["displayName"],n);let l=Q(e,["dest"]);return void 0!==t&&null!=l&&X(t,["outputConfig"],function(e){let t={},n=Q(e,["format"]);null!=n&&X(t,["predictionsFormat"],n);let l=Q(e,["gcsUri"]);null!=l&&X(t,["gcsDestination","outputUriPrefix"],l);let i=Q(e,["bigqueryUri"]);if(null!=i&&X(t,["bigqueryDestination","outputUri"],i),void 0!==Q(e,["fileName"]))throw Error("fileName parameter is not supported in Vertex AI.");if(void 0!==Q(e,["inlinedResponses"]))throw Error("inlinedResponses parameter is not supported in Vertex AI.");return t}(function(e){if("string"!=typeof e)return e;if(e.startsWith("gs://"))return{format:"jsonl",gcsUri:e};if(e.startsWith("bq://"))return{format:"bigquery",bigqueryUri:e};throw Error(`Unsupported destination: ${e}`)}(l))),{}}(r,n)),n}(this.apiClient,e);return o=z("batchPredictionJobs",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json()).then(e=>e1(e))}{let t=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","model"],e_(e,l));let i=Q(t,["src"]);null!=i&&X(n,["batch","inputConfig"],function(e,t){let n={};if(void 0!==Q(t,["format"]))throw Error("format parameter is not supported in Gemini API.");if(void 0!==Q(t,["gcsUri"]))throw Error("gcsUri parameter is not supported in Gemini API.");if(void 0!==Q(t,["bigqueryUri"]))throw Error("bigqueryUri parameter is not supported in Gemini API.");let l=Q(t,["fileName"]);null!=l&&X(n,["fileName"],l);let i=Q(t,["inlinedRequests"]);if(null!=i){let t=i;Array.isArray(t)&&(t=t.map(t=>(function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["request","model"],e_(e,l));let i=Q(t,["contents"]);if(null!=i){let e=ek(i);Array.isArray(e)&&(e=e.map(e=>ez(e))),X(n,["request","contents"],e)}let r=Q(t,["config"]);return null!=r&&X(n,["request","generationConfig"],function(e,t,n){let l={},i=Q(t,["systemInstruction"]);void 0!==n&&null!=i&&X(n,["systemInstruction"],ez(ex(i)));let r=Q(t,["temperature"]);null!=r&&X(l,["temperature"],r);let o=Q(t,["topP"]);null!=o&&X(l,["topP"],o);let a=Q(t,["topK"]);null!=a&&X(l,["topK"],a);let s=Q(t,["candidateCount"]);null!=s&&X(l,["candidateCount"],s);let u=Q(t,["maxOutputTokens"]);null!=u&&X(l,["maxOutputTokens"],u);let p=Q(t,["stopSequences"]);null!=p&&X(l,["stopSequences"],p);let d=Q(t,["responseLogprobs"]);null!=d&&X(l,["responseLogprobs"],d);let c=Q(t,["logprobs"]);null!=c&&X(l,["logprobs"],c);let f=Q(t,["presencePenalty"]);null!=f&&X(l,["presencePenalty"],f);let m=Q(t,["frequencyPenalty"]);null!=m&&X(l,["frequencyPenalty"],m);let h=Q(t,["seed"]);null!=h&&X(l,["seed"],h);let g=Q(t,["responseMimeType"]);null!=g&&X(l,["responseMimeType"],g);let y=Q(t,["responseSchema"]);null!=y&&X(l,["responseSchema"],function(e){let t={},n=Q(e,["anyOf"]);null!=n&&X(t,["anyOf"],n);let l=Q(e,["default"]);null!=l&&X(t,["default"],l);let i=Q(e,["description"]);null!=i&&X(t,["description"],i);let r=Q(e,["enum"]);null!=r&&X(t,["enum"],r);let o=Q(e,["example"]);null!=o&&X(t,["example"],o);let a=Q(e,["format"]);null!=a&&X(t,["format"],a);let s=Q(e,["items"]);null!=s&&X(t,["items"],s);let u=Q(e,["maxItems"]);null!=u&&X(t,["maxItems"],u);let p=Q(e,["maxLength"]);null!=p&&X(t,["maxLength"],p);let d=Q(e,["maxProperties"]);null!=d&&X(t,["maxProperties"],d);let c=Q(e,["maximum"]);null!=c&&X(t,["maximum"],c);let f=Q(e,["minItems"]);null!=f&&X(t,["minItems"],f);let m=Q(e,["minLength"]);null!=m&&X(t,["minLength"],m);let h=Q(e,["minProperties"]);null!=h&&X(t,["minProperties"],h);let g=Q(e,["minimum"]);null!=g&&X(t,["minimum"],g);let y=Q(e,["nullable"]);null!=y&&X(t,["nullable"],y);let C=Q(e,["pattern"]);null!=C&&X(t,["pattern"],C);let T=Q(e,["properties"]);null!=T&&X(t,["properties"],T);let E=Q(e,["propertyOrdering"]);null!=E&&X(t,["propertyOrdering"],E);let v=Q(e,["required"]);null!=v&&X(t,["required"],v);let A=Q(e,["title"]);null!=A&&X(t,["title"],A);let _=Q(e,["type"]);return null!=_&&X(t,["type"],_),t}(eL(y)));let C=Q(t,["responseJsonSchema"]);if(null!=C&&X(l,["responseJsonSchema"],C),void 0!==Q(t,["routingConfig"]))throw Error("routingConfig parameter is not supported in Gemini API.");if(void 0!==Q(t,["modelSelectionConfig"]))throw Error("modelSelectionConfig parameter is not supported in Gemini API.");let T=Q(t,["safetySettings"]);if(void 0!==n&&null!=T){let e=T;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={};if(void 0!==Q(e,["method"]))throw Error("method parameter is not supported in Gemini API.");let n=Q(e,["category"]);null!=n&&X(t,["category"],n);let l=Q(e,["threshold"]);return null!=l&&X(t,["threshold"],l),t})(e))),X(n,["safetySettings"],e)}let E=Q(t,["tools"]);if(void 0!==n&&null!=E){let e=eF(E);Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["functionDeclarations"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["behavior"]);null!=n&&X(t,["behavior"],n);let l=Q(e,["description"]);null!=l&&X(t,["description"],l);let i=Q(e,["name"]);null!=i&&X(t,["name"],i);let r=Q(e,["parameters"]);null!=r&&X(t,["parameters"],r);let o=Q(e,["parametersJsonSchema"]);null!=o&&X(t,["parametersJsonSchema"],o);let a=Q(e,["response"]);null!=a&&X(t,["response"],a);let s=Q(e,["responseJsonSchema"]);return null!=s&&X(t,["responseJsonSchema"],s),t})(e))),X(t,["functionDeclarations"],e)}if(void 0!==Q(e,["retrieval"]))throw Error("retrieval parameter is not supported in Gemini API.");let l=Q(e,["googleSearch"]);null!=l&&X(t,["googleSearch"],function(e){let t={},n=Q(e,["timeRangeFilter"]);return null!=n&&X(t,["timeRangeFilter"],function(e){let t={},n=Q(e,["startTime"]);null!=n&&X(t,["startTime"],n);let l=Q(e,["endTime"]);return null!=l&&X(t,["endTime"],l),t}(n)),t}(l));let i=Q(e,["googleSearchRetrieval"]);if(null!=i&&X(t,["googleSearchRetrieval"],function(e){let t={},n=Q(e,["dynamicRetrievalConfig"]);return null!=n&&X(t,["dynamicRetrievalConfig"],function(e){let t={},n=Q(e,["mode"]);null!=n&&X(t,["mode"],n);let l=Q(e,["dynamicThreshold"]);return null!=l&&X(t,["dynamicThreshold"],l),t}(n)),t}(i)),void 0!==Q(e,["enterpriseWebSearch"]))throw Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(void 0!==Q(e,["googleMaps"]))throw Error("googleMaps parameter is not supported in Gemini API.");null!=Q(e,["urlContext"])&&X(t,["urlContext"],{});let r=Q(e,["codeExecution"]);null!=r&&X(t,["codeExecution"],r);let o=Q(e,["computerUse"]);return null!=o&&X(t,["computerUse"],o),t})(eH(e)))),X(n,["tools"],e)}let v=Q(t,["toolConfig"]);if(void 0!==n&&null!=v&&X(n,["toolConfig"],function(e){let t={},n=Q(e,["functionCallingConfig"]);null!=n&&X(t,["functionCallingConfig"],function(e){let t={},n=Q(e,["mode"]);null!=n&&X(t,["mode"],n);let l=Q(e,["allowedFunctionNames"]);return null!=l&&X(t,["allowedFunctionNames"],l),t}(n));let l=Q(e,["retrievalConfig"]);return null!=l&&X(t,["retrievalConfig"],function(e){let t={},n=Q(e,["latLng"]);null!=n&&X(t,["latLng"],function(e){let t={},n=Q(e,["latitude"]);null!=n&&X(t,["latitude"],n);let l=Q(e,["longitude"]);return null!=l&&X(t,["longitude"],l),t}(n));let l=Q(e,["languageCode"]);return null!=l&&X(t,["languageCode"],l),t}(l)),t}(v)),void 0!==Q(t,["labels"]))throw Error("labels parameter is not supported in Gemini API.");let A=Q(t,["cachedContent"]);void 0!==n&&null!=A&&X(n,["cachedContent"],eV(e,A));let _=Q(t,["responseModalities"]);null!=_&&X(l,["responseModalities"],_);let O=Q(t,["mediaResolution"]);null!=O&&X(l,["mediaResolution"],O);let I=Q(t,["speechConfig"]);if(null!=I&&X(l,["speechConfig"],function(e){let t={},n=Q(e,["voiceConfig"]);null!=n&&X(t,["voiceConfig"],eX(n));let l=Q(e,["multiSpeakerVoiceConfig"]);null!=l&&X(t,["multiSpeakerVoiceConfig"],function(e){let t={},n=Q(e,["speakerVoiceConfigs"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["speaker"]);null!=n&&X(t,["speaker"],n);let l=Q(e,["voiceConfig"]);return null!=l&&X(t,["voiceConfig"],eX(l)),t})(e))),X(t,["speakerVoiceConfigs"],e)}return t}(l));let i=Q(e,["languageCode"]);return null!=i&&X(t,["languageCode"],i),t}(eq(I))),void 0!==Q(t,["audioTimestamp"]))throw Error("audioTimestamp parameter is not supported in Gemini API.");let S=Q(t,["thinkingConfig"]);return null!=S&&X(l,["thinkingConfig"],function(e){let t={},n=Q(e,["includeThoughts"]);null!=n&&X(t,["includeThoughts"],n);let l=Q(e,["thinkingBudget"]);return null!=l&&X(t,["thinkingBudget"],l),t}(S)),l}(e,r,n)),n})(e,t))),X(n,["requests","requests"],t)}return n}(e,eW(e,i)));let r=Q(t,["config"]);return null!=r&&X(n,["config"],function(e,t){let n=Q(e,["displayName"]);if(void 0!==t&&null!=n&&X(t,["batch","displayName"],n),void 0!==Q(e,["dest"]))throw Error("dest parameter is not supported in Gemini API.");return{}}(r,n)),n}(this.apiClient,e);return o=z("{model}:batchGenerateContent",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json()).then(e=>eZ(e))}}async get(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e,t){let n={},l=Q(t,["name"]);null!=l&&X(n,["_url","name"],eK(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],i),n}(this.apiClient,e);return o=z("batchPredictionJobs/{name}",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"GET",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json()).then(e=>e1(e))}{let t=function(e,t){let n={},l=Q(t,["name"]);null!=l&&X(n,["_url","name"],eK(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],i),n}(this.apiClient,e);return o=z("batches/{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json()).then(e=>eZ(e))}}async cancel(e){var t,n,l,i;let r="",o={};if(this.apiClient.isVertexAI()){let l=function(e,t){let n={},l=Q(t,["name"]);null!=l&&X(n,["_url","name"],eK(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],i),n}(this.apiClient,e);r=z("batchPredictionJobs/{name}:cancel",l._url),o=l._query,delete l.config,delete l._url,delete l._query,await this.apiClient.request({path:r,queryParams:o,body:JSON.stringify(l),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal})}else{let t=function(e,t){let n={},l=Q(t,["name"]);null!=l&&X(n,["_url","name"],eK(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],i),n}(this.apiClient,e);r=z("batches/{name}:cancel",t._url),o=t._query,delete t.config,delete t._url,delete t._query,await this.apiClient.request({path:r,queryParams:o,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal})}}async listInternal(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e){let t={},n=Q(e,["config"]);return null!=n&&X(t,["config"],function(e,t){let n=Q(e,["pageSize"]);void 0!==t&&null!=n&&X(t,["_query","pageSize"],n);let l=Q(e,["pageToken"]);void 0!==t&&null!=l&&X(t,["_query","pageToken"],l);let i=Q(e,["filter"]);return void 0!==t&&null!=i&&X(t,["_query","filter"],i),{}}(n,t)),t}(e);return o=z("batchPredictionJobs",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"GET",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["nextPageToken"]);null!=l&&X(t,["nextPageToken"],l);let i=Q(e,["batchPredictionJobs"]);if(null!=i){let e=i;Array.isArray(e)&&(e=e.map(e=>e1(e))),X(t,["batchJobs"],e)}return t}(e),n=new eT;return Object.assign(n,t),n})}{let t=function(e){let t={},n=Q(e,["config"]);return null!=n&&X(t,["config"],function(e,t){let n=Q(e,["pageSize"]);void 0!==t&&null!=n&&X(t,["_query","pageSize"],n);let l=Q(e,["pageToken"]);if(void 0!==t&&null!=l&&X(t,["_query","pageToken"],l),void 0!==Q(e,["filter"]))throw Error("filter parameter is not supported in Gemini API.");return{}}(n,t)),t}(e);return o=z("batches",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["nextPageToken"]);null!=l&&X(t,["nextPageToken"],l);let i=Q(e,["operations"]);if(null!=i){let e=i;Array.isArray(e)&&(e=e.map(e=>eZ(e))),X(t,["batchJobs"],e)}return t}(e),n=new eT;return Object.assign(n,t),n})}}async delete(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e,t){let n={},l=Q(t,["name"]);null!=l&&X(n,["_url","name"],eK(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],i),n}(this.apiClient,e);return o=z("batchPredictionJobs/{name}",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"DELETE",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>(function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["name"]);null!=l&&X(t,["name"],l);let i=Q(e,["done"]);null!=i&&X(t,["done"],i);let r=Q(e,["error"]);return null!=r&&X(t,["error"],e0(r)),t})(e))}{let t=function(e,t){let n={},l=Q(t,["name"]);null!=l&&X(n,["_url","name"],eK(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],i),n}(this.apiClient,e);return o=z("batches/{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"DELETE",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>(function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["name"]);null!=l&&X(t,["name"],l);let i=Q(e,["done"]);null!=i&&X(t,["done"],i);let r=Q(e,["error"]);return null!=r&&X(t,["error"],eQ(r)),t})(e))}}}function e2(e){let t={},n=Q(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["videoMetadata"]);null!=n&&X(t,["videoMetadata"],function(e){let t={},n=Q(e,["fps"]);null!=n&&X(t,["fps"],n);let l=Q(e,["endOffset"]);null!=l&&X(t,["endOffset"],l);let i=Q(e,["startOffset"]);return null!=i&&X(t,["startOffset"],i),t}(n));let l=Q(e,["thought"]);null!=l&&X(t,["thought"],l);let i=Q(e,["inlineData"]);null!=i&&X(t,["inlineData"],function(e){let t={};if(void 0!==Q(e,["displayName"]))throw Error("displayName parameter is not supported in Gemini API.");let n=Q(e,["data"]);null!=n&&X(t,["data"],n);let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(i));let r=Q(e,["fileData"]);null!=r&&X(t,["fileData"],function(e){let t={};if(void 0!==Q(e,["displayName"]))throw Error("displayName parameter is not supported in Gemini API.");let n=Q(e,["fileUri"]);null!=n&&X(t,["fileUri"],n);let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(r));let o=Q(e,["thoughtSignature"]);null!=o&&X(t,["thoughtSignature"],o);let a=Q(e,["codeExecutionResult"]);null!=a&&X(t,["codeExecutionResult"],a);let s=Q(e,["executableCode"]);null!=s&&X(t,["executableCode"],s);let u=Q(e,["functionCall"]);null!=u&&X(t,["functionCall"],u);let p=Q(e,["functionResponse"]);null!=p&&X(t,["functionResponse"],p);let d=Q(e,["text"]);return null!=d&&X(t,["text"],d),t})(e))),X(t,["parts"],e)}let l=Q(e,["role"]);return null!=l&&X(t,["role"],l),t}function e9(e){let t={},n=Q(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["videoMetadata"]);null!=n&&X(t,["videoMetadata"],function(e){let t={},n=Q(e,["fps"]);null!=n&&X(t,["fps"],n);let l=Q(e,["endOffset"]);null!=l&&X(t,["endOffset"],l);let i=Q(e,["startOffset"]);return null!=i&&X(t,["startOffset"],i),t}(n));let l=Q(e,["thought"]);null!=l&&X(t,["thought"],l);let i=Q(e,["inlineData"]);null!=i&&X(t,["inlineData"],function(e){let t={},n=Q(e,["displayName"]);null!=n&&X(t,["displayName"],n);let l=Q(e,["data"]);null!=l&&X(t,["data"],l);let i=Q(e,["mimeType"]);return null!=i&&X(t,["mimeType"],i),t}(i));let r=Q(e,["fileData"]);null!=r&&X(t,["fileData"],function(e){let t={},n=Q(e,["displayName"]);null!=n&&X(t,["displayName"],n);let l=Q(e,["fileUri"]);null!=l&&X(t,["fileUri"],l);let i=Q(e,["mimeType"]);return null!=i&&X(t,["mimeType"],i),t}(r));let o=Q(e,["thoughtSignature"]);null!=o&&X(t,["thoughtSignature"],o);let a=Q(e,["codeExecutionResult"]);null!=a&&X(t,["codeExecutionResult"],a);let s=Q(e,["executableCode"]);null!=s&&X(t,["executableCode"],s);let u=Q(e,["functionCall"]);null!=u&&X(t,["functionCall"],u);let p=Q(e,["functionResponse"]);null!=p&&X(t,["functionResponse"],p);let d=Q(e,["text"]);return null!=d&&X(t,["text"],d),t})(e))),X(t,["parts"],e)}let l=Q(e,["role"]);return null!=l&&X(t,["role"],l),t}function e3(e){let t={},n=Q(e,["name"]);null!=n&&X(t,["name"],n);let l=Q(e,["displayName"]);null!=l&&X(t,["displayName"],l);let i=Q(e,["model"]);null!=i&&X(t,["model"],i);let r=Q(e,["createTime"]);null!=r&&X(t,["createTime"],r);let o=Q(e,["updateTime"]);null!=o&&X(t,["updateTime"],o);let a=Q(e,["expireTime"]);null!=a&&X(t,["expireTime"],a);let s=Q(e,["usageMetadata"]);return null!=s&&X(t,["usageMetadata"],s),t}function e8(e){let t={},n=Q(e,["name"]);null!=n&&X(t,["name"],n);let l=Q(e,["displayName"]);null!=l&&X(t,["displayName"],l);let i=Q(e,["model"]);null!=i&&X(t,["model"],i);let r=Q(e,["createTime"]);null!=r&&X(t,["createTime"],r);let o=Q(e,["updateTime"]);null!=o&&X(t,["updateTime"],o);let a=Q(e,["expireTime"]);null!=a&&X(t,["expireTime"],a);let s=Q(e,["usageMetadata"]);return null!=s&&X(t,["usageMetadata"],s),t}class e5 extends ${constructor(e){super(),this.apiClient=e,this.list=async(e={})=>new e4(Y.PAGED_ITEM_CACHED_CONTENTS,e=>this.listInternal(e),await this.listInternal(e),e)}async create(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["model"],eO(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],function(e,t){let n=Q(e,["ttl"]);void 0!==t&&null!=n&&X(t,["ttl"],n);let l=Q(e,["expireTime"]);void 0!==t&&null!=l&&X(t,["expireTime"],l);let i=Q(e,["displayName"]);void 0!==t&&null!=i&&X(t,["displayName"],i);let r=Q(e,["contents"]);if(void 0!==t&&null!=r){let e=ek(r);Array.isArray(e)&&(e=e.map(e=>e9(e))),X(t,["contents"],e)}let o=Q(e,["systemInstruction"]);void 0!==t&&null!=o&&X(t,["systemInstruction"],e9(ex(o)));let a=Q(e,["tools"]);if(void 0!==t&&null!=a){let e=a;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["functionDeclarations"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={};if(void 0!==Q(e,["behavior"]))throw Error("behavior parameter is not supported in Vertex AI.");let n=Q(e,["description"]);null!=n&&X(t,["description"],n);let l=Q(e,["name"]);null!=l&&X(t,["name"],l);let i=Q(e,["parameters"]);null!=i&&X(t,["parameters"],i);let r=Q(e,["parametersJsonSchema"]);null!=r&&X(t,["parametersJsonSchema"],r);let o=Q(e,["response"]);null!=o&&X(t,["response"],o);let a=Q(e,["responseJsonSchema"]);return null!=a&&X(t,["responseJsonSchema"],a),t})(e))),X(t,["functionDeclarations"],e)}let l=Q(e,["retrieval"]);null!=l&&X(t,["retrieval"],l);let i=Q(e,["googleSearch"]);null!=i&&X(t,["googleSearch"],function(e){let t={},n=Q(e,["timeRangeFilter"]);return null!=n&&X(t,["timeRangeFilter"],function(e){let t={},n=Q(e,["startTime"]);null!=n&&X(t,["startTime"],n);let l=Q(e,["endTime"]);return null!=l&&X(t,["endTime"],l),t}(n)),t}(i));let r=Q(e,["googleSearchRetrieval"]);null!=r&&X(t,["googleSearchRetrieval"],function(e){let t={},n=Q(e,["dynamicRetrievalConfig"]);return null!=n&&X(t,["dynamicRetrievalConfig"],function(e){let t={},n=Q(e,["mode"]);null!=n&&X(t,["mode"],n);let l=Q(e,["dynamicThreshold"]);return null!=l&&X(t,["dynamicThreshold"],l),t}(n)),t}(r)),null!=Q(e,["enterpriseWebSearch"])&&X(t,["enterpriseWebSearch"],{});let o=Q(e,["googleMaps"]);null!=o&&X(t,["googleMaps"],function(e){let t={},n=Q(e,["authConfig"]);return null!=n&&X(t,["authConfig"],function(e){let t={},n=Q(e,["apiKeyConfig"]);null!=n&&X(t,["apiKeyConfig"],function(e){let t={},n=Q(e,["apiKeyString"]);return null!=n&&X(t,["apiKeyString"],n),t}(n));let l=Q(e,["authType"]);null!=l&&X(t,["authType"],l);let i=Q(e,["googleServiceAccountConfig"]);null!=i&&X(t,["googleServiceAccountConfig"],i);let r=Q(e,["httpBasicAuthConfig"]);null!=r&&X(t,["httpBasicAuthConfig"],r);let o=Q(e,["oauthConfig"]);null!=o&&X(t,["oauthConfig"],o);let a=Q(e,["oidcConfig"]);return null!=a&&X(t,["oidcConfig"],a),t}(n)),t}(o)),null!=Q(e,["urlContext"])&&X(t,["urlContext"],{});let a=Q(e,["codeExecution"]);null!=a&&X(t,["codeExecution"],a);let s=Q(e,["computerUse"]);return null!=s&&X(t,["computerUse"],s),t})(e))),X(t,["tools"],e)}let s=Q(e,["toolConfig"]);void 0!==t&&null!=s&&X(t,["toolConfig"],function(e){let t={},n=Q(e,["functionCallingConfig"]);null!=n&&X(t,["functionCallingConfig"],function(e){let t={},n=Q(e,["mode"]);null!=n&&X(t,["mode"],n);let l=Q(e,["allowedFunctionNames"]);return null!=l&&X(t,["allowedFunctionNames"],l),t}(n));let l=Q(e,["retrievalConfig"]);return null!=l&&X(t,["retrievalConfig"],function(e){let t={},n=Q(e,["latLng"]);null!=n&&X(t,["latLng"],function(e){let t={},n=Q(e,["latitude"]);null!=n&&X(t,["latitude"],n);let l=Q(e,["longitude"]);return null!=l&&X(t,["longitude"],l),t}(n));let l=Q(e,["languageCode"]);return null!=l&&X(t,["languageCode"],l),t}(l)),t}(s));let u=Q(e,["kmsKeyName"]);return void 0!==t&&null!=u&&X(t,["encryption_spec","kmsKeyName"],u),{}}(i,n)),n}(this.apiClient,e);return o=z("cachedContents",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json()).then(e=>e8(e))}{let t=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["model"],eO(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],function(e,t){let n=Q(e,["ttl"]);void 0!==t&&null!=n&&X(t,["ttl"],n);let l=Q(e,["expireTime"]);void 0!==t&&null!=l&&X(t,["expireTime"],l);let i=Q(e,["displayName"]);void 0!==t&&null!=i&&X(t,["displayName"],i);let r=Q(e,["contents"]);if(void 0!==t&&null!=r){let e=ek(r);Array.isArray(e)&&(e=e.map(e=>e2(e))),X(t,["contents"],e)}let o=Q(e,["systemInstruction"]);void 0!==t&&null!=o&&X(t,["systemInstruction"],e2(ex(o)));let a=Q(e,["tools"]);if(void 0!==t&&null!=a){let e=a;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["functionDeclarations"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["behavior"]);null!=n&&X(t,["behavior"],n);let l=Q(e,["description"]);null!=l&&X(t,["description"],l);let i=Q(e,["name"]);null!=i&&X(t,["name"],i);let r=Q(e,["parameters"]);null!=r&&X(t,["parameters"],r);let o=Q(e,["parametersJsonSchema"]);null!=o&&X(t,["parametersJsonSchema"],o);let a=Q(e,["response"]);null!=a&&X(t,["response"],a);let s=Q(e,["responseJsonSchema"]);return null!=s&&X(t,["responseJsonSchema"],s),t})(e))),X(t,["functionDeclarations"],e)}if(void 0!==Q(e,["retrieval"]))throw Error("retrieval parameter is not supported in Gemini API.");let l=Q(e,["googleSearch"]);null!=l&&X(t,["googleSearch"],function(e){let t={},n=Q(e,["timeRangeFilter"]);return null!=n&&X(t,["timeRangeFilter"],function(e){let t={},n=Q(e,["startTime"]);null!=n&&X(t,["startTime"],n);let l=Q(e,["endTime"]);return null!=l&&X(t,["endTime"],l),t}(n)),t}(l));let i=Q(e,["googleSearchRetrieval"]);if(null!=i&&X(t,["googleSearchRetrieval"],function(e){let t={},n=Q(e,["dynamicRetrievalConfig"]);return null!=n&&X(t,["dynamicRetrievalConfig"],function(e){let t={},n=Q(e,["mode"]);null!=n&&X(t,["mode"],n);let l=Q(e,["dynamicThreshold"]);return null!=l&&X(t,["dynamicThreshold"],l),t}(n)),t}(i)),void 0!==Q(e,["enterpriseWebSearch"]))throw Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(void 0!==Q(e,["googleMaps"]))throw Error("googleMaps parameter is not supported in Gemini API.");null!=Q(e,["urlContext"])&&X(t,["urlContext"],{});let r=Q(e,["codeExecution"]);null!=r&&X(t,["codeExecution"],r);let o=Q(e,["computerUse"]);return null!=o&&X(t,["computerUse"],o),t})(e))),X(t,["tools"],e)}let s=Q(e,["toolConfig"]);if(void 0!==t&&null!=s&&X(t,["toolConfig"],function(e){let t={},n=Q(e,["functionCallingConfig"]);null!=n&&X(t,["functionCallingConfig"],function(e){let t={},n=Q(e,["mode"]);null!=n&&X(t,["mode"],n);let l=Q(e,["allowedFunctionNames"]);return null!=l&&X(t,["allowedFunctionNames"],l),t}(n));let l=Q(e,["retrievalConfig"]);return null!=l&&X(t,["retrievalConfig"],function(e){let t={},n=Q(e,["latLng"]);null!=n&&X(t,["latLng"],function(e){let t={},n=Q(e,["latitude"]);null!=n&&X(t,["latitude"],n);let l=Q(e,["longitude"]);return null!=l&&X(t,["longitude"],l),t}(n));let l=Q(e,["languageCode"]);return null!=l&&X(t,["languageCode"],l),t}(l)),t}(s)),void 0!==Q(e,["kmsKeyName"]))throw Error("kmsKeyName parameter is not supported in Gemini API.");return{}}(i,n)),n}(this.apiClient,e);return o=z("cachedContents",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json()).then(e=>e3(e))}}async get(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e,t){let n={},l=Q(t,["name"]);null!=l&&X(n,["_url","name"],eV(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],i),n}(this.apiClient,e);return o=z("{name}",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"GET",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json()).then(e=>e8(e))}{let t=function(e,t){let n={},l=Q(t,["name"]);null!=l&&X(n,["_url","name"],eV(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],i),n}(this.apiClient,e);return o=z("{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json()).then(e=>e3(e))}}async delete(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e,t){let n={},l=Q(t,["name"]);null!=l&&X(n,["_url","name"],eV(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],i),n}(this.apiClient,e);return o=z("{name}",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"DELETE",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json()).then(()=>{let e=new em;return Object.assign(e,{}),e})}{let t=function(e,t){let n={},l=Q(t,["name"]);null!=l&&X(n,["_url","name"],eV(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],i),n}(this.apiClient,e);return o=z("{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"DELETE",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json()).then(()=>{let e=new em;return Object.assign(e,{}),e})}}async update(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e,t){let n={},l=Q(t,["name"]);null!=l&&X(n,["_url","name"],eV(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],function(e,t){let n=Q(e,["ttl"]);void 0!==t&&null!=n&&X(t,["ttl"],n);let l=Q(e,["expireTime"]);return void 0!==t&&null!=l&&X(t,["expireTime"],l),{}}(i,n)),n}(this.apiClient,e);return o=z("{name}",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"PATCH",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json()).then(e=>e8(e))}{let t=function(e,t){let n={},l=Q(t,["name"]);null!=l&&X(n,["_url","name"],eV(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],function(e,t){let n=Q(e,["ttl"]);void 0!==t&&null!=n&&X(t,["ttl"],n);let l=Q(e,["expireTime"]);return void 0!==t&&null!=l&&X(t,["expireTime"],l),{}}(i,n)),n}(this.apiClient,e);return o=z("{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"PATCH",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json()).then(e=>e3(e))}}async listInternal(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e){let t={},n=Q(e,["config"]);return null!=n&&X(t,["config"],function(e,t){let n=Q(e,["pageSize"]);void 0!==t&&null!=n&&X(t,["_query","pageSize"],n);let l=Q(e,["pageToken"]);return void 0!==t&&null!=l&&X(t,["_query","pageToken"],l),{}}(n,t)),t}(e);return o=z("cachedContents",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"GET",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["nextPageToken"]);null!=l&&X(t,["nextPageToken"],l);let i=Q(e,["cachedContents"]);if(null!=i){let e=i;Array.isArray(e)&&(e=e.map(e=>e8(e))),X(t,["cachedContents"],e)}return t}(e),n=new eh;return Object.assign(n,t),n})}{let t=function(e){let t={},n=Q(e,["config"]);return null!=n&&X(t,["config"],function(e,t){let n=Q(e,["pageSize"]);void 0!==t&&null!=n&&X(t,["_query","pageSize"],n);let l=Q(e,["pageToken"]);return void 0!==t&&null!=l&&X(t,["_query","pageToken"],l),{}}(n,t)),t}(e);return o=z("cachedContents",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["nextPageToken"]);null!=l&&X(t,["nextPageToken"],l);let i=Q(e,["cachedContents"]);if(null!=i){let e=i;Array.isArray(e)&&(e=e.map(e=>e3(e))),X(t,["cachedContents"],e)}return t}(e),n=new eh;return Object.assign(n,t),n})}}}function e7(e){return this instanceof e7?(this.v=e,this):new e7(e)}function te(e,t,n){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var l,i=n.apply(e,t||[]),r=[];return l=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",function(e){return function(t){return Promise.resolve(t).then(e,u)}}),l[Symbol.asyncIterator]=function(){return this},l;function o(e,t){i[e]&&(l[e]=function(t){return new Promise(function(n,l){r.push([e,t,n,l])>1||a(e,t)})},t&&(l[e]=t(l[e])))}function a(e,t){try{var n;(n=i[e](t)).value instanceof e7?Promise.resolve(n.value.v).then(s,u):p(r[0][2],n)}catch(e){p(r[0][3],e)}}function s(e){a("next",e)}function u(e){a("throw",e)}function p(e,t){e(t),r.shift(),r.length&&a(r[0][0],r[0][1])}}function tt(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],l=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&l>=e.length&&(e=void 0),{value:e&&e[l++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}(e),t={},l("next"),l("throw"),l("return"),t[Symbol.asyncIterator]=function(){return this},t);function l(n){t[n]=e[n]&&function(t){return new Promise(function(l,i){var r,o,a;r=l,o=i,a=(t=e[n](t)).done,Promise.resolve(t.value).then(function(e){r({value:e,done:a})},o)})}}}function tn(e){if(void 0===e.parts||0===e.parts.length)return!1;for(let t of e.parts)if(void 0===t||0===Object.keys(t).length||!t.thought&&void 0!==t.text&&""===t.text)return!1;return!0}function tl(e){if(void 0===e||0===e.length)return[];let t=[],n=e.length,l=0;for(;l<n;)if("user"===e[l].role)t.push(e[l]),l++;else{let i=[],r=!0;for(;l<n&&"model"===e[l].role;)i.push(e[l]),r&&!tn(e[l])&&(r=!1),l++;r?t.push(...i):t.pop()}return t}"function"==typeof SuppressedError&&SuppressedError;class ti{constructor(e,t){this.modelsModule=e,this.apiClient=t}create(e){return new tr(this.apiClient,this.modelsModule,e.model,e.config,structuredClone(e.history))}}class tr{constructor(e,t,n,l={},i=[]){this.apiClient=e,this.modelsModule=t,this.model=n,this.config=l,this.history=i,this.sendPromise=Promise.resolve(),function(e){if(0!==e.length){for(let t of e)if("user"!==t.role&&"model"!==t.role)throw Error(`Role must be user or model, but got ${t.role}.`)}}(i)}async sendMessage(e){var t;await this.sendPromise;let n=ex(e.message),l=this.modelsModule.generateContent({model:this.model,contents:this.getHistory(!0).concat(n),config:null!=(t=e.config)?t:this.config});return this.sendPromise=(async()=>{var e,t,i;let r=await l,o=null==(t=null==(e=r.candidates)?void 0:e[0])?void 0:t.content,a=r.automaticFunctionCallingHistory,s=this.getHistory(!0).length,u=[];null!=a&&(u=null!=(i=a.slice(s))?i:[]),this.recordHistory(n,o?[o]:[],u)})(),await this.sendPromise.catch(()=>{this.sendPromise=Promise.resolve()}),l}async sendMessageStream(e){var t;await this.sendPromise;let n=ex(e.message),l=this.modelsModule.generateContentStream({model:this.model,contents:this.getHistory(!0).concat(n),config:null!=(t=e.config)?t:this.config});this.sendPromise=l.then(()=>void 0).catch(()=>void 0);let i=await l;return this.processStreamResponse(i,n)}getHistory(e=!1){return structuredClone(e?tl(this.history):this.history)}processStreamResponse(e,t){var n,l;return te(this,arguments,function*(){var i,r,o,a;let s=[];try{for(var u,p=!0,d=tt(e);!(i=(u=yield e7(d.next())).done);p=!0){if(a=u.value,p=!1,function(e){var t;if(void 0==e.candidates||0===e.candidates.length)return!1;let n=null==(t=e.candidates[0])?void 0:t.content;return void 0!==n&&tn(n)}(a)){let e=null==(l=null==(n=a.candidates)?void 0:n[0])?void 0:l.content;void 0!==e&&s.push(e)}yield yield e7(a)}}catch(e){r={error:e}}finally{try{!p&&!i&&(o=d.return)&&(yield e7(o.call(d)))}finally{if(r)throw r.error}}this.recordHistory(t,s)})}recordHistory(e,t,n){let l=[];t.length>0&&t.every(e=>void 0!==e.role)?l=t:l.push({role:"model",parts:[]}),n&&n.length>0?this.history.push(...tl(n)):this.history.push(e),this.history.push(...l)}}class to extends Error{constructor(e){super(e.message),this.name="ApiError",this.status=e.status,Object.setPrototypeOf(this,to.prototype)}}function ta(e){let t={},n=Q(e,["name"]);null!=n&&X(t,["name"],n);let l=Q(e,["displayName"]);null!=l&&X(t,["displayName"],l);let i=Q(e,["mimeType"]);null!=i&&X(t,["mimeType"],i);let r=Q(e,["sizeBytes"]);null!=r&&X(t,["sizeBytes"],r);let o=Q(e,["createTime"]);null!=o&&X(t,["createTime"],o);let a=Q(e,["expirationTime"]);null!=a&&X(t,["expirationTime"],a);let s=Q(e,["updateTime"]);null!=s&&X(t,["updateTime"],s);let u=Q(e,["sha256Hash"]);null!=u&&X(t,["sha256Hash"],u);let p=Q(e,["uri"]);null!=p&&X(t,["uri"],p);let d=Q(e,["downloadUri"]);null!=d&&X(t,["downloadUri"],d);let c=Q(e,["state"]);null!=c&&X(t,["state"],c);let f=Q(e,["source"]);null!=f&&X(t,["source"],f);let m=Q(e,["videoMetadata"]);null!=m&&X(t,["videoMetadata"],m);let h=Q(e,["error"]);return null!=h&&X(t,["error"],function(e){let t={},n=Q(e,["details"]);null!=n&&X(t,["details"],n);let l=Q(e,["message"]);null!=l&&X(t,["message"],l);let i=Q(e,["code"]);return null!=i&&X(t,["code"],i),t}(h)),t}class ts extends ${constructor(e){super(),this.apiClient=e,this.list=async(e={})=>new e4(Y.PAGED_ITEM_FILES,e=>this.listInternal(e),await this.listInternal(e),e)}async upload(e){if(this.apiClient.isVertexAI())throw Error("Vertex AI does not support uploading files. You can share files through a GCS bucket.");return this.apiClient.uploadFile(e.file,e.config).then(e=>ta(e))}async download(e){await this.apiClient.downloadFile(e)}async listInternal(e){var t,n;let l="",i={};if(this.apiClient.isVertexAI())throw Error("This method is only supported by the Gemini Developer API.");{let r=function(e){let t={},n=Q(e,["config"]);return null!=n&&X(t,["config"],function(e,t){let n=Q(e,["pageSize"]);void 0!==t&&null!=n&&X(t,["_query","pageSize"],n);let l=Q(e,["pageToken"]);return void 0!==t&&null!=l&&X(t,["_query","pageToken"],l),{}}(n,t)),t}(e);return l=z("files",r._url),i=r._query,delete r.config,delete r._url,delete r._query,this.apiClient.request({path:l,queryParams:i,body:JSON.stringify(r),httpMethod:"GET",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["nextPageToken"]);null!=l&&X(t,["nextPageToken"],l);let i=Q(e,["files"]);if(null!=i){let e=i;Array.isArray(e)&&(e=e.map(e=>ta(e))),X(t,["files"],e)}return t}(e),n=new eg;return Object.assign(n,t),n})}}async createInternal(e){var t,n;let l="",i={};if(this.apiClient.isVertexAI())throw Error("This method is only supported by the Gemini Developer API.");{let r=function(e){let t={},n=Q(e,["file"]);null!=n&&X(t,["file"],function(e){let t={},n=Q(e,["name"]);null!=n&&X(t,["name"],n);let l=Q(e,["displayName"]);null!=l&&X(t,["displayName"],l);let i=Q(e,["mimeType"]);null!=i&&X(t,["mimeType"],i);let r=Q(e,["sizeBytes"]);null!=r&&X(t,["sizeBytes"],r);let o=Q(e,["createTime"]);null!=o&&X(t,["createTime"],o);let a=Q(e,["expirationTime"]);null!=a&&X(t,["expirationTime"],a);let s=Q(e,["updateTime"]);null!=s&&X(t,["updateTime"],s);let u=Q(e,["sha256Hash"]);null!=u&&X(t,["sha256Hash"],u);let p=Q(e,["uri"]);null!=p&&X(t,["uri"],p);let d=Q(e,["downloadUri"]);null!=d&&X(t,["downloadUri"],d);let c=Q(e,["state"]);null!=c&&X(t,["state"],c);let f=Q(e,["source"]);null!=f&&X(t,["source"],f);let m=Q(e,["videoMetadata"]);null!=m&&X(t,["videoMetadata"],m);let h=Q(e,["error"]);return null!=h&&X(t,["error"],function(e){let t={},n=Q(e,["details"]);null!=n&&X(t,["details"],n);let l=Q(e,["message"]);null!=l&&X(t,["message"],l);let i=Q(e,["code"]);return null!=i&&X(t,["code"],i),t}(h)),t}(n));let l=Q(e,["config"]);return null!=l&&X(t,["config"],l),t}(e);return l=z("upload/v1beta/files",r._url),i=r._query,delete r.config,delete r._url,delete r._query,this.apiClient.request({path:l,queryParams:i,body:JSON.stringify(r),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json()).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);return null!=n&&X(t,["sdkHttpResponse"],n),t}(e),n=new ey;return Object.assign(n,t),n})}}async get(e){var t,n;let l="",i={};if(this.apiClient.isVertexAI())throw Error("This method is only supported by the Gemini Developer API.");{let r=function(e){let t={},n=Q(e,["name"]);null!=n&&X(t,["_url","file"],eJ(n));let l=Q(e,["config"]);return null!=l&&X(t,["config"],l),t}(e);return l=z("files/{file}",r._url),i=r._query,delete r.config,delete r._url,delete r._query,this.apiClient.request({path:l,queryParams:i,body:JSON.stringify(r),httpMethod:"GET",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json()).then(e=>ta(e))}}async delete(e){var t,n;let l="",i={};if(this.apiClient.isVertexAI())throw Error("This method is only supported by the Gemini Developer API.");{let r=function(e){let t={},n=Q(e,["name"]);null!=n&&X(t,["_url","file"],eJ(n));let l=Q(e,["config"]);return null!=l&&X(t,["config"],l),t}(e);return l=z("files/{file}",r._url),i=r._query,delete r.config,delete r._url,delete r._query,this.apiClient.request({path:l,queryParams:i,body:JSON.stringify(r),httpMethod:"DELETE",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json()).then(()=>{let e=new eC;return Object.assign(e,{}),e})}}}function tu(e){let t={},n=Q(e,["prebuiltVoiceConfig"]);return null!=n&&X(t,["prebuiltVoiceConfig"],function(e){let t={},n=Q(e,["voiceName"]);return null!=n&&X(t,["voiceName"],n),t}(n)),t}function tp(e){let t={},n=Q(e,["text"]);null!=n&&X(t,["text"],n);let l=Q(e,["weight"]);return null!=l&&X(t,["weight"],l),t}function td(e){let t={},n=Q(e,["temperature"]);null!=n&&X(t,["temperature"],n);let l=Q(e,["topK"]);null!=l&&X(t,["topK"],l);let i=Q(e,["seed"]);null!=i&&X(t,["seed"],i);let r=Q(e,["guidance"]);null!=r&&X(t,["guidance"],r);let o=Q(e,["bpm"]);null!=o&&X(t,["bpm"],o);let a=Q(e,["density"]);null!=a&&X(t,["density"],a);let s=Q(e,["brightness"]);null!=s&&X(t,["brightness"],s);let u=Q(e,["scale"]);null!=u&&X(t,["scale"],u);let p=Q(e,["muteBass"]);null!=p&&X(t,["muteBass"],p);let d=Q(e,["muteDrums"]);null!=d&&X(t,["muteDrums"],d);let c=Q(e,["onlyBassAndDrums"]);null!=c&&X(t,["onlyBassAndDrums"],c);let f=Q(e,["musicGenerationMode"]);return null!=f&&X(t,["musicGenerationMode"],f),t}function tc(e){let t={},n=Q(e,["model"]);return null!=n&&X(t,["model"],n),t}function tf(e){let t={},n=Q(e,["weightedPrompts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>tp(e))),X(t,["weightedPrompts"],e)}return t}function tm(e){let t={},n=Q(e,["setup"]);null!=n&&X(t,["setup"],tc(n));let l=Q(e,["clientContent"]);null!=l&&X(t,["clientContent"],tf(l));let i=Q(e,["musicGenerationConfig"]);null!=i&&X(t,["musicGenerationConfig"],td(i));let r=Q(e,["playbackControl"]);return null!=r&&X(t,["playbackControl"],r),t}function th(e){let t={},n=Q(e,["text"]);null!=n&&X(t,["text"],n);let l=Q(e,["finished"]);return null!=l&&X(t,["finished"],l),t}function tg(e){let t={},n=Q(e,["modality"]);null!=n&&X(t,["modality"],n);let l=Q(e,["tokenCount"]);return null!=l&&X(t,["tokenCount"],l),t}function ty(e){let t={},n=Q(e,["text"]);null!=n&&X(t,["text"],n);let l=Q(e,["finished"]);return null!=l&&X(t,["finished"],l),t}function tC(e){let t={},n=Q(e,["modality"]);null!=n&&X(t,["modality"],n);let l=Q(e,["tokenCount"]);return null!=l&&X(t,["tokenCount"],l),t}function tT(e){let t={},n=Q(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["videoMetadata"]);null!=n&&X(t,["videoMetadata"],function(e){let t={},n=Q(e,["fps"]);null!=n&&X(t,["fps"],n);let l=Q(e,["endOffset"]);null!=l&&X(t,["endOffset"],l);let i=Q(e,["startOffset"]);return null!=i&&X(t,["startOffset"],i),t}(n));let l=Q(e,["thought"]);null!=l&&X(t,["thought"],l);let i=Q(e,["inlineData"]);null!=i&&X(t,["inlineData"],function(e){let t={};if(void 0!==Q(e,["displayName"]))throw Error("displayName parameter is not supported in Gemini API.");let n=Q(e,["data"]);null!=n&&X(t,["data"],n);let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(i));let r=Q(e,["fileData"]);null!=r&&X(t,["fileData"],function(e){let t={};if(void 0!==Q(e,["displayName"]))throw Error("displayName parameter is not supported in Gemini API.");let n=Q(e,["fileUri"]);null!=n&&X(t,["fileUri"],n);let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(r));let o=Q(e,["thoughtSignature"]);null!=o&&X(t,["thoughtSignature"],o);let a=Q(e,["codeExecutionResult"]);null!=a&&X(t,["codeExecutionResult"],a);let s=Q(e,["executableCode"]);null!=s&&X(t,["executableCode"],s);let u=Q(e,["functionCall"]);null!=u&&X(t,["functionCall"],u);let p=Q(e,["functionResponse"]);null!=p&&X(t,["functionResponse"],p);let d=Q(e,["text"]);return null!=d&&X(t,["text"],d),t})(e))),X(t,["parts"],e)}let l=Q(e,["role"]);return null!=l&&X(t,["role"],l),t}function tE(e){let t={},n=Q(e,["prebuiltVoiceConfig"]);return null!=n&&X(t,["prebuiltVoiceConfig"],function(e){let t={},n=Q(e,["voiceName"]);return null!=n&&X(t,["voiceName"],n),t}(n)),t}function tv(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","model"],e_(e,l));let i=Q(t,["contents"]);if(null!=i){let e=ek(i);Array.isArray(e)&&(e=e.map(e=>tT(e))),X(n,["contents"],e)}let r=Q(t,["config"]);return null!=r&&X(n,["generationConfig"],function(e,t,n){let l={},i=Q(t,["systemInstruction"]);void 0!==n&&null!=i&&X(n,["systemInstruction"],tT(ex(i)));let r=Q(t,["temperature"]);null!=r&&X(l,["temperature"],r);let o=Q(t,["topP"]);null!=o&&X(l,["topP"],o);let a=Q(t,["topK"]);null!=a&&X(l,["topK"],a);let s=Q(t,["candidateCount"]);null!=s&&X(l,["candidateCount"],s);let u=Q(t,["maxOutputTokens"]);null!=u&&X(l,["maxOutputTokens"],u);let p=Q(t,["stopSequences"]);null!=p&&X(l,["stopSequences"],p);let d=Q(t,["responseLogprobs"]);null!=d&&X(l,["responseLogprobs"],d);let c=Q(t,["logprobs"]);null!=c&&X(l,["logprobs"],c);let f=Q(t,["presencePenalty"]);null!=f&&X(l,["presencePenalty"],f);let m=Q(t,["frequencyPenalty"]);null!=m&&X(l,["frequencyPenalty"],m);let h=Q(t,["seed"]);null!=h&&X(l,["seed"],h);let g=Q(t,["responseMimeType"]);null!=g&&X(l,["responseMimeType"],g);let y=Q(t,["responseSchema"]);null!=y&&X(l,["responseSchema"],function(e){let t={},n=Q(e,["anyOf"]);null!=n&&X(t,["anyOf"],n);let l=Q(e,["default"]);null!=l&&X(t,["default"],l);let i=Q(e,["description"]);null!=i&&X(t,["description"],i);let r=Q(e,["enum"]);null!=r&&X(t,["enum"],r);let o=Q(e,["example"]);null!=o&&X(t,["example"],o);let a=Q(e,["format"]);null!=a&&X(t,["format"],a);let s=Q(e,["items"]);null!=s&&X(t,["items"],s);let u=Q(e,["maxItems"]);null!=u&&X(t,["maxItems"],u);let p=Q(e,["maxLength"]);null!=p&&X(t,["maxLength"],p);let d=Q(e,["maxProperties"]);null!=d&&X(t,["maxProperties"],d);let c=Q(e,["maximum"]);null!=c&&X(t,["maximum"],c);let f=Q(e,["minItems"]);null!=f&&X(t,["minItems"],f);let m=Q(e,["minLength"]);null!=m&&X(t,["minLength"],m);let h=Q(e,["minProperties"]);null!=h&&X(t,["minProperties"],h);let g=Q(e,["minimum"]);null!=g&&X(t,["minimum"],g);let y=Q(e,["nullable"]);null!=y&&X(t,["nullable"],y);let C=Q(e,["pattern"]);null!=C&&X(t,["pattern"],C);let T=Q(e,["properties"]);null!=T&&X(t,["properties"],T);let E=Q(e,["propertyOrdering"]);null!=E&&X(t,["propertyOrdering"],E);let v=Q(e,["required"]);null!=v&&X(t,["required"],v);let A=Q(e,["title"]);null!=A&&X(t,["title"],A);let _=Q(e,["type"]);return null!=_&&X(t,["type"],_),t}(eL(y)));let C=Q(t,["responseJsonSchema"]);if(null!=C&&X(l,["responseJsonSchema"],C),void 0!==Q(t,["routingConfig"]))throw Error("routingConfig parameter is not supported in Gemini API.");if(void 0!==Q(t,["modelSelectionConfig"]))throw Error("modelSelectionConfig parameter is not supported in Gemini API.");let T=Q(t,["safetySettings"]);if(void 0!==n&&null!=T){let e=T;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={};if(void 0!==Q(e,["method"]))throw Error("method parameter is not supported in Gemini API.");let n=Q(e,["category"]);null!=n&&X(t,["category"],n);let l=Q(e,["threshold"]);return null!=l&&X(t,["threshold"],l),t})(e))),X(n,["safetySettings"],e)}let E=Q(t,["tools"]);if(void 0!==n&&null!=E){let e=eF(E);Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["functionDeclarations"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["behavior"]);null!=n&&X(t,["behavior"],n);let l=Q(e,["description"]);null!=l&&X(t,["description"],l);let i=Q(e,["name"]);null!=i&&X(t,["name"],i);let r=Q(e,["parameters"]);null!=r&&X(t,["parameters"],r);let o=Q(e,["parametersJsonSchema"]);null!=o&&X(t,["parametersJsonSchema"],o);let a=Q(e,["response"]);null!=a&&X(t,["response"],a);let s=Q(e,["responseJsonSchema"]);return null!=s&&X(t,["responseJsonSchema"],s),t})(e))),X(t,["functionDeclarations"],e)}if(void 0!==Q(e,["retrieval"]))throw Error("retrieval parameter is not supported in Gemini API.");let l=Q(e,["googleSearch"]);null!=l&&X(t,["googleSearch"],function(e){let t={},n=Q(e,["timeRangeFilter"]);return null!=n&&X(t,["timeRangeFilter"],function(e){let t={},n=Q(e,["startTime"]);null!=n&&X(t,["startTime"],n);let l=Q(e,["endTime"]);return null!=l&&X(t,["endTime"],l),t}(n)),t}(l));let i=Q(e,["googleSearchRetrieval"]);if(null!=i&&X(t,["googleSearchRetrieval"],function(e){let t={},n=Q(e,["dynamicRetrievalConfig"]);return null!=n&&X(t,["dynamicRetrievalConfig"],function(e){let t={},n=Q(e,["mode"]);null!=n&&X(t,["mode"],n);let l=Q(e,["dynamicThreshold"]);return null!=l&&X(t,["dynamicThreshold"],l),t}(n)),t}(i)),void 0!==Q(e,["enterpriseWebSearch"]))throw Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(void 0!==Q(e,["googleMaps"]))throw Error("googleMaps parameter is not supported in Gemini API.");null!=Q(e,["urlContext"])&&X(t,["urlContext"],{});let r=Q(e,["codeExecution"]);null!=r&&X(t,["codeExecution"],r);let o=Q(e,["computerUse"]);return null!=o&&X(t,["computerUse"],o),t})(eH(e)))),X(n,["tools"],e)}let v=Q(t,["toolConfig"]);if(void 0!==n&&null!=v&&X(n,["toolConfig"],function(e){let t={},n=Q(e,["functionCallingConfig"]);null!=n&&X(t,["functionCallingConfig"],function(e){let t={},n=Q(e,["mode"]);null!=n&&X(t,["mode"],n);let l=Q(e,["allowedFunctionNames"]);return null!=l&&X(t,["allowedFunctionNames"],l),t}(n));let l=Q(e,["retrievalConfig"]);return null!=l&&X(t,["retrievalConfig"],function(e){let t={},n=Q(e,["latLng"]);null!=n&&X(t,["latLng"],function(e){let t={},n=Q(e,["latitude"]);null!=n&&X(t,["latitude"],n);let l=Q(e,["longitude"]);return null!=l&&X(t,["longitude"],l),t}(n));let l=Q(e,["languageCode"]);return null!=l&&X(t,["languageCode"],l),t}(l)),t}(v)),void 0!==Q(t,["labels"]))throw Error("labels parameter is not supported in Gemini API.");let A=Q(t,["cachedContent"]);void 0!==n&&null!=A&&X(n,["cachedContent"],eV(e,A));let _=Q(t,["responseModalities"]);null!=_&&X(l,["responseModalities"],_);let O=Q(t,["mediaResolution"]);null!=O&&X(l,["mediaResolution"],O);let I=Q(t,["speechConfig"]);if(null!=I&&X(l,["speechConfig"],function(e){let t={},n=Q(e,["voiceConfig"]);null!=n&&X(t,["voiceConfig"],tE(n));let l=Q(e,["multiSpeakerVoiceConfig"]);null!=l&&X(t,["multiSpeakerVoiceConfig"],function(e){let t={},n=Q(e,["speakerVoiceConfigs"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["speaker"]);null!=n&&X(t,["speaker"],n);let l=Q(e,["voiceConfig"]);return null!=l&&X(t,["voiceConfig"],tE(l)),t})(e))),X(t,["speakerVoiceConfigs"],e)}return t}(l));let i=Q(e,["languageCode"]);return null!=i&&X(t,["languageCode"],i),t}(eq(I))),void 0!==Q(t,["audioTimestamp"]))throw Error("audioTimestamp parameter is not supported in Gemini API.");let S=Q(t,["thinkingConfig"]);return null!=S&&X(l,["thinkingConfig"],function(e){let t={},n=Q(e,["includeThoughts"]);null!=n&&X(t,["includeThoughts"],n);let l=Q(e,["thinkingBudget"]);return null!=l&&X(t,["thinkingBudget"],l),t}(S)),l}(e,r,n)),n}function tA(e){let t={},n=Q(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["videoMetadata"]);null!=n&&X(t,["videoMetadata"],function(e){let t={},n=Q(e,["fps"]);null!=n&&X(t,["fps"],n);let l=Q(e,["endOffset"]);null!=l&&X(t,["endOffset"],l);let i=Q(e,["startOffset"]);return null!=i&&X(t,["startOffset"],i),t}(n));let l=Q(e,["thought"]);null!=l&&X(t,["thought"],l);let i=Q(e,["inlineData"]);null!=i&&X(t,["inlineData"],function(e){let t={},n=Q(e,["displayName"]);null!=n&&X(t,["displayName"],n);let l=Q(e,["data"]);null!=l&&X(t,["data"],l);let i=Q(e,["mimeType"]);return null!=i&&X(t,["mimeType"],i),t}(i));let r=Q(e,["fileData"]);null!=r&&X(t,["fileData"],function(e){let t={},n=Q(e,["displayName"]);null!=n&&X(t,["displayName"],n);let l=Q(e,["fileUri"]);null!=l&&X(t,["fileUri"],l);let i=Q(e,["mimeType"]);return null!=i&&X(t,["mimeType"],i),t}(r));let o=Q(e,["thoughtSignature"]);null!=o&&X(t,["thoughtSignature"],o);let a=Q(e,["codeExecutionResult"]);null!=a&&X(t,["codeExecutionResult"],a);let s=Q(e,["executableCode"]);null!=s&&X(t,["executableCode"],s);let u=Q(e,["functionCall"]);null!=u&&X(t,["functionCall"],u);let p=Q(e,["functionResponse"]);null!=p&&X(t,["functionResponse"],p);let d=Q(e,["text"]);return null!=d&&X(t,["text"],d),t})(e))),X(t,["parts"],e)}let l=Q(e,["role"]);return null!=l&&X(t,["role"],l),t}function t_(e){let t={},n=Q(e,["functionDeclarations"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={};if(void 0!==Q(e,["behavior"]))throw Error("behavior parameter is not supported in Vertex AI.");let n=Q(e,["description"]);null!=n&&X(t,["description"],n);let l=Q(e,["name"]);null!=l&&X(t,["name"],l);let i=Q(e,["parameters"]);null!=i&&X(t,["parameters"],i);let r=Q(e,["parametersJsonSchema"]);null!=r&&X(t,["parametersJsonSchema"],r);let o=Q(e,["response"]);null!=o&&X(t,["response"],o);let a=Q(e,["responseJsonSchema"]);return null!=a&&X(t,["responseJsonSchema"],a),t})(e))),X(t,["functionDeclarations"],e)}let l=Q(e,["retrieval"]);null!=l&&X(t,["retrieval"],l);let i=Q(e,["googleSearch"]);null!=i&&X(t,["googleSearch"],function(e){let t={},n=Q(e,["timeRangeFilter"]);return null!=n&&X(t,["timeRangeFilter"],function(e){let t={},n=Q(e,["startTime"]);null!=n&&X(t,["startTime"],n);let l=Q(e,["endTime"]);return null!=l&&X(t,["endTime"],l),t}(n)),t}(i));let r=Q(e,["googleSearchRetrieval"]);null!=r&&X(t,["googleSearchRetrieval"],function(e){let t={},n=Q(e,["dynamicRetrievalConfig"]);return null!=n&&X(t,["dynamicRetrievalConfig"],function(e){let t={},n=Q(e,["mode"]);null!=n&&X(t,["mode"],n);let l=Q(e,["dynamicThreshold"]);return null!=l&&X(t,["dynamicThreshold"],l),t}(n)),t}(r)),null!=Q(e,["enterpriseWebSearch"])&&X(t,["enterpriseWebSearch"],{});let o=Q(e,["googleMaps"]);null!=o&&X(t,["googleMaps"],function(e){let t={},n=Q(e,["authConfig"]);return null!=n&&X(t,["authConfig"],function(e){let t={},n=Q(e,["apiKeyConfig"]);null!=n&&X(t,["apiKeyConfig"],function(e){let t={},n=Q(e,["apiKeyString"]);return null!=n&&X(t,["apiKeyString"],n),t}(n));let l=Q(e,["authType"]);null!=l&&X(t,["authType"],l);let i=Q(e,["googleServiceAccountConfig"]);null!=i&&X(t,["googleServiceAccountConfig"],i);let r=Q(e,["httpBasicAuthConfig"]);null!=r&&X(t,["httpBasicAuthConfig"],r);let o=Q(e,["oauthConfig"]);null!=o&&X(t,["oauthConfig"],o);let a=Q(e,["oidcConfig"]);return null!=a&&X(t,["oidcConfig"],a),t}(n)),t}(o)),null!=Q(e,["urlContext"])&&X(t,["urlContext"],{});let a=Q(e,["codeExecution"]);null!=a&&X(t,["codeExecution"],a);let s=Q(e,["computerUse"]);return null!=s&&X(t,["computerUse"],s),t}function tO(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","model"],e_(e,l));let i=Q(t,["contents"]);if(null!=i){let e=ek(i);Array.isArray(e)&&(e=e.map(e=>tA(e))),X(n,["contents"],e)}let r=Q(t,["config"]);return null!=r&&X(n,["generationConfig"],function(e,t,n){let l={},i=Q(t,["systemInstruction"]);void 0!==n&&null!=i&&X(n,["systemInstruction"],tA(ex(i)));let r=Q(t,["temperature"]);null!=r&&X(l,["temperature"],r);let o=Q(t,["topP"]);null!=o&&X(l,["topP"],o);let a=Q(t,["topK"]);null!=a&&X(l,["topK"],a);let s=Q(t,["candidateCount"]);null!=s&&X(l,["candidateCount"],s);let u=Q(t,["maxOutputTokens"]);null!=u&&X(l,["maxOutputTokens"],u);let p=Q(t,["stopSequences"]);null!=p&&X(l,["stopSequences"],p);let d=Q(t,["responseLogprobs"]);null!=d&&X(l,["responseLogprobs"],d);let c=Q(t,["logprobs"]);null!=c&&X(l,["logprobs"],c);let f=Q(t,["presencePenalty"]);null!=f&&X(l,["presencePenalty"],f);let m=Q(t,["frequencyPenalty"]);null!=m&&X(l,["frequencyPenalty"],m);let h=Q(t,["seed"]);null!=h&&X(l,["seed"],h);let g=Q(t,["responseMimeType"]);null!=g&&X(l,["responseMimeType"],g);let y=Q(t,["responseSchema"]);null!=y&&X(l,["responseSchema"],function(e){let t={},n=Q(e,["anyOf"]);null!=n&&X(t,["anyOf"],n);let l=Q(e,["default"]);null!=l&&X(t,["default"],l);let i=Q(e,["description"]);null!=i&&X(t,["description"],i);let r=Q(e,["enum"]);null!=r&&X(t,["enum"],r);let o=Q(e,["example"]);null!=o&&X(t,["example"],o);let a=Q(e,["format"]);null!=a&&X(t,["format"],a);let s=Q(e,["items"]);null!=s&&X(t,["items"],s);let u=Q(e,["maxItems"]);null!=u&&X(t,["maxItems"],u);let p=Q(e,["maxLength"]);null!=p&&X(t,["maxLength"],p);let d=Q(e,["maxProperties"]);null!=d&&X(t,["maxProperties"],d);let c=Q(e,["maximum"]);null!=c&&X(t,["maximum"],c);let f=Q(e,["minItems"]);null!=f&&X(t,["minItems"],f);let m=Q(e,["minLength"]);null!=m&&X(t,["minLength"],m);let h=Q(e,["minProperties"]);null!=h&&X(t,["minProperties"],h);let g=Q(e,["minimum"]);null!=g&&X(t,["minimum"],g);let y=Q(e,["nullable"]);null!=y&&X(t,["nullable"],y);let C=Q(e,["pattern"]);null!=C&&X(t,["pattern"],C);let T=Q(e,["properties"]);null!=T&&X(t,["properties"],T);let E=Q(e,["propertyOrdering"]);null!=E&&X(t,["propertyOrdering"],E);let v=Q(e,["required"]);null!=v&&X(t,["required"],v);let A=Q(e,["title"]);null!=A&&X(t,["title"],A);let _=Q(e,["type"]);return null!=_&&X(t,["type"],_),t}(eL(y)));let C=Q(t,["responseJsonSchema"]);null!=C&&X(l,["responseJsonSchema"],C);let T=Q(t,["routingConfig"]);null!=T&&X(l,["routingConfig"],T);let E=Q(t,["modelSelectionConfig"]);null!=E&&X(l,["modelConfig"],function(e){let t={},n=Q(e,["featureSelectionPreference"]);return null!=n&&X(t,["featureSelectionPreference"],n),t}(E));let v=Q(t,["safetySettings"]);if(void 0!==n&&null!=v){let e=v;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["method"]);null!=n&&X(t,["method"],n);let l=Q(e,["category"]);null!=l&&X(t,["category"],l);let i=Q(e,["threshold"]);return null!=i&&X(t,["threshold"],i),t})(e))),X(n,["safetySettings"],e)}let A=Q(t,["tools"]);if(void 0!==n&&null!=A){let e=eF(A);Array.isArray(e)&&(e=e.map(e=>t_(eH(e)))),X(n,["tools"],e)}let _=Q(t,["toolConfig"]);void 0!==n&&null!=_&&X(n,["toolConfig"],function(e){let t={},n=Q(e,["functionCallingConfig"]);null!=n&&X(t,["functionCallingConfig"],function(e){let t={},n=Q(e,["mode"]);null!=n&&X(t,["mode"],n);let l=Q(e,["allowedFunctionNames"]);return null!=l&&X(t,["allowedFunctionNames"],l),t}(n));let l=Q(e,["retrievalConfig"]);return null!=l&&X(t,["retrievalConfig"],function(e){let t={},n=Q(e,["latLng"]);null!=n&&X(t,["latLng"],function(e){let t={},n=Q(e,["latitude"]);null!=n&&X(t,["latitude"],n);let l=Q(e,["longitude"]);return null!=l&&X(t,["longitude"],l),t}(n));let l=Q(e,["languageCode"]);return null!=l&&X(t,["languageCode"],l),t}(l)),t}(_));let O=Q(t,["labels"]);void 0!==n&&null!=O&&X(n,["labels"],O);let I=Q(t,["cachedContent"]);void 0!==n&&null!=I&&X(n,["cachedContent"],eV(e,I));let S=Q(t,["responseModalities"]);null!=S&&X(l,["responseModalities"],S);let b=Q(t,["mediaResolution"]);null!=b&&X(l,["mediaResolution"],b);let R=Q(t,["speechConfig"]);null!=R&&X(l,["speechConfig"],function(e){let t={},n=Q(e,["voiceConfig"]);if(null!=n&&X(t,["voiceConfig"],function(e){let t={},n=Q(e,["prebuiltVoiceConfig"]);return null!=n&&X(t,["prebuiltVoiceConfig"],function(e){let t={},n=Q(e,["voiceName"]);return null!=n&&X(t,["voiceName"],n),t}(n)),t}(n)),void 0!==Q(e,["multiSpeakerVoiceConfig"]))throw Error("multiSpeakerVoiceConfig parameter is not supported in Vertex AI.");let l=Q(e,["languageCode"]);return null!=l&&X(t,["languageCode"],l),t}(eq(R)));let N=Q(t,["audioTimestamp"]);null!=N&&X(l,["audioTimestamp"],N);let P=Q(t,["thinkingConfig"]);return null!=P&&X(l,["thinkingConfig"],function(e){let t={},n=Q(e,["includeThoughts"]);null!=n&&X(t,["includeThoughts"],n);let l=Q(e,["thinkingBudget"]);return null!=l&&X(t,["thinkingBudget"],l),t}(P)),l}(e,r,n)),n}function tI(e){let t={},n=Q(e,["gcsUri"]);null!=n&&X(t,["gcsUri"],n);let l=Q(e,["imageBytes"]);null!=l&&X(t,["bytesBase64Encoded"],Z(l));let i=Q(e,["mimeType"]);return null!=i&&X(t,["mimeType"],i),t}function tS(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["candidates"]);if(null!=l){let e=l;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["content"]);null!=n&&X(t,["content"],function(e){let t={},n=Q(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["videoMetadata"]);null!=n&&X(t,["videoMetadata"],function(e){let t={},n=Q(e,["fps"]);null!=n&&X(t,["fps"],n);let l=Q(e,["endOffset"]);null!=l&&X(t,["endOffset"],l);let i=Q(e,["startOffset"]);return null!=i&&X(t,["startOffset"],i),t}(n));let l=Q(e,["thought"]);null!=l&&X(t,["thought"],l);let i=Q(e,["inlineData"]);null!=i&&X(t,["inlineData"],function(e){let t={},n=Q(e,["data"]);null!=n&&X(t,["data"],n);let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(i));let r=Q(e,["fileData"]);null!=r&&X(t,["fileData"],function(e){let t={},n=Q(e,["fileUri"]);null!=n&&X(t,["fileUri"],n);let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(r));let o=Q(e,["thoughtSignature"]);null!=o&&X(t,["thoughtSignature"],o);let a=Q(e,["codeExecutionResult"]);null!=a&&X(t,["codeExecutionResult"],a);let s=Q(e,["executableCode"]);null!=s&&X(t,["executableCode"],s);let u=Q(e,["functionCall"]);null!=u&&X(t,["functionCall"],u);let p=Q(e,["functionResponse"]);null!=p&&X(t,["functionResponse"],p);let d=Q(e,["text"]);return null!=d&&X(t,["text"],d),t})(e))),X(t,["parts"],e)}let l=Q(e,["role"]);return null!=l&&X(t,["role"],l),t}(n));let l=Q(e,["citationMetadata"]);null!=l&&X(t,["citationMetadata"],function(e){let t={},n=Q(e,["citationSources"]);return null!=n&&X(t,["citations"],n),t}(l));let i=Q(e,["tokenCount"]);null!=i&&X(t,["tokenCount"],i);let r=Q(e,["finishReason"]);null!=r&&X(t,["finishReason"],r);let o=Q(e,["urlContextMetadata"]);null!=o&&X(t,["urlContextMetadata"],function(e){let t={},n=Q(e,["urlMetadata"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["retrievedUrl"]);null!=n&&X(t,["retrievedUrl"],n);let l=Q(e,["urlRetrievalStatus"]);return null!=l&&X(t,["urlRetrievalStatus"],l),t})(e))),X(t,["urlMetadata"],e)}return t}(o));let a=Q(e,["avgLogprobs"]);null!=a&&X(t,["avgLogprobs"],a);let s=Q(e,["groundingMetadata"]);null!=s&&X(t,["groundingMetadata"],s);let u=Q(e,["index"]);null!=u&&X(t,["index"],u);let p=Q(e,["logprobsResult"]);null!=p&&X(t,["logprobsResult"],p);let d=Q(e,["safetyRatings"]);return null!=d&&X(t,["safetyRatings"],d),t})(e))),X(t,["candidates"],e)}let i=Q(e,["modelVersion"]);null!=i&&X(t,["modelVersion"],i);let r=Q(e,["promptFeedback"]);null!=r&&X(t,["promptFeedback"],r);let o=Q(e,["responseId"]);null!=o&&X(t,["responseId"],o);let a=Q(e,["usageMetadata"]);return null!=a&&X(t,["usageMetadata"],a),t}function tb(e){let t={},n=Q(e,["safetyAttributes","categories"]);null!=n&&X(t,["categories"],n);let l=Q(e,["safetyAttributes","scores"]);null!=l&&X(t,["scores"],l);let i=Q(e,["contentType"]);return null!=i&&X(t,["contentType"],i),t}function tR(e){let t={},n=Q(e,["name"]);null!=n&&X(t,["name"],n);let l=Q(e,["displayName"]);null!=l&&X(t,["displayName"],l);let i=Q(e,["description"]);null!=i&&X(t,["description"],i);let r=Q(e,["version"]);null!=r&&X(t,["version"],r);let o=Q(e,["_self"]);null!=o&&X(t,["tunedModelInfo"],function(e){let t={},n=Q(e,["baseModel"]);null!=n&&X(t,["baseModel"],n);let l=Q(e,["createTime"]);null!=l&&X(t,["createTime"],l);let i=Q(e,["updateTime"]);return null!=i&&X(t,["updateTime"],i),t}(o));let a=Q(e,["inputTokenLimit"]);null!=a&&X(t,["inputTokenLimit"],a);let s=Q(e,["outputTokenLimit"]);null!=s&&X(t,["outputTokenLimit"],s);let u=Q(e,["supportedGenerationMethods"]);return null!=u&&X(t,["supportedActions"],u),t}function tN(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["candidates"]);if(null!=l){let e=l;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["content"]);null!=n&&X(t,["content"],function(e){let t={},n=Q(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["videoMetadata"]);null!=n&&X(t,["videoMetadata"],function(e){let t={},n=Q(e,["fps"]);null!=n&&X(t,["fps"],n);let l=Q(e,["endOffset"]);null!=l&&X(t,["endOffset"],l);let i=Q(e,["startOffset"]);return null!=i&&X(t,["startOffset"],i),t}(n));let l=Q(e,["thought"]);null!=l&&X(t,["thought"],l);let i=Q(e,["inlineData"]);null!=i&&X(t,["inlineData"],function(e){let t={},n=Q(e,["displayName"]);null!=n&&X(t,["displayName"],n);let l=Q(e,["data"]);null!=l&&X(t,["data"],l);let i=Q(e,["mimeType"]);return null!=i&&X(t,["mimeType"],i),t}(i));let r=Q(e,["fileData"]);null!=r&&X(t,["fileData"],function(e){let t={},n=Q(e,["displayName"]);null!=n&&X(t,["displayName"],n);let l=Q(e,["fileUri"]);null!=l&&X(t,["fileUri"],l);let i=Q(e,["mimeType"]);return null!=i&&X(t,["mimeType"],i),t}(r));let o=Q(e,["thoughtSignature"]);null!=o&&X(t,["thoughtSignature"],o);let a=Q(e,["codeExecutionResult"]);null!=a&&X(t,["codeExecutionResult"],a);let s=Q(e,["executableCode"]);null!=s&&X(t,["executableCode"],s);let u=Q(e,["functionCall"]);null!=u&&X(t,["functionCall"],u);let p=Q(e,["functionResponse"]);null!=p&&X(t,["functionResponse"],p);let d=Q(e,["text"]);return null!=d&&X(t,["text"],d),t})(e))),X(t,["parts"],e)}let l=Q(e,["role"]);return null!=l&&X(t,["role"],l),t}(n));let l=Q(e,["citationMetadata"]);null!=l&&X(t,["citationMetadata"],function(e){let t={},n=Q(e,["citations"]);return null!=n&&X(t,["citations"],n),t}(l));let i=Q(e,["finishMessage"]);null!=i&&X(t,["finishMessage"],i);let r=Q(e,["finishReason"]);null!=r&&X(t,["finishReason"],r);let o=Q(e,["urlContextMetadata"]);null!=o&&X(t,["urlContextMetadata"],function(e){let t={},n=Q(e,["urlMetadata"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["retrievedUrl"]);null!=n&&X(t,["retrievedUrl"],n);let l=Q(e,["urlRetrievalStatus"]);return null!=l&&X(t,["urlRetrievalStatus"],l),t})(e))),X(t,["urlMetadata"],e)}return t}(o));let a=Q(e,["avgLogprobs"]);null!=a&&X(t,["avgLogprobs"],a);let s=Q(e,["groundingMetadata"]);null!=s&&X(t,["groundingMetadata"],s);let u=Q(e,["index"]);null!=u&&X(t,["index"],u);let p=Q(e,["logprobsResult"]);null!=p&&X(t,["logprobsResult"],p);let d=Q(e,["safetyRatings"]);return null!=d&&X(t,["safetyRatings"],d),t})(e))),X(t,["candidates"],e)}let i=Q(e,["createTime"]);null!=i&&X(t,["createTime"],i);let r=Q(e,["modelVersion"]);null!=r&&X(t,["modelVersion"],r);let o=Q(e,["promptFeedback"]);null!=o&&X(t,["promptFeedback"],o);let a=Q(e,["responseId"]);null!=a&&X(t,["responseId"],a);let s=Q(e,["usageMetadata"]);return null!=s&&X(t,["usageMetadata"],s),t}function tP(e){let t={},n=Q(e,["safetyAttributes","categories"]);null!=n&&X(t,["categories"],n);let l=Q(e,["safetyAttributes","scores"]);null!=l&&X(t,["scores"],l);let i=Q(e,["contentType"]);return null!=i&&X(t,["contentType"],i),t}function tM(e){let t={},n=Q(e,["_self"]);null!=n&&X(t,["image"],function(e){let t={},n=Q(e,["gcsUri"]);null!=n&&X(t,["gcsUri"],n);let l=Q(e,["bytesBase64Encoded"]);null!=l&&X(t,["imageBytes"],Z(l));let i=Q(e,["mimeType"]);return null!=i&&X(t,["mimeType"],i),t}(n));let l=Q(e,["raiFilteredReason"]);null!=l&&X(t,["raiFilteredReason"],l);let i=Q(e,["_self"]);null!=i&&X(t,["safetyAttributes"],tP(i));let r=Q(e,["prompt"]);return null!=r&&X(t,["enhancedPrompt"],r),t}function tD(e){let t={},n=Q(e,["name"]);null!=n&&X(t,["name"],n);let l=Q(e,["displayName"]);null!=l&&X(t,["displayName"],l);let i=Q(e,["description"]);null!=i&&X(t,["description"],i);let r=Q(e,["versionId"]);null!=r&&X(t,["version"],r);let o=Q(e,["deployedModels"]);if(null!=o){let e=o;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["endpoint"]);null!=n&&X(t,["name"],n);let l=Q(e,["deployedModelId"]);return null!=l&&X(t,["deployedModelId"],l),t})(e))),X(t,["endpoints"],e)}let a=Q(e,["labels"]);null!=a&&X(t,["labels"],a);let s=Q(e,["_self"]);null!=s&&X(t,["tunedModelInfo"],function(e){let t={},n=Q(e,["labels","google-vertex-llm-tuning-base-model-id"]);null!=n&&X(t,["baseModel"],n);let l=Q(e,["createTime"]);null!=l&&X(t,["createTime"],l);let i=Q(e,["updateTime"]);return null!=i&&X(t,["updateTime"],i),t}(s));let u=Q(e,["defaultCheckpointId"]);null!=u&&X(t,["defaultCheckpointId"],u);let p=Q(e,["checkpoints"]);if(null!=p){let e=p;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["checkpointId"]);null!=n&&X(t,["checkpointId"],n);let l=Q(e,["epoch"]);null!=l&&X(t,["epoch"],l);let i=Q(e,["step"]);return null!=i&&X(t,["step"],i),t})(e))),X(t,["checkpoints"],e)}return t}let tw="x-goog-api-client",tx=/^data: (.*)(?:\n\n|\r\r|\r\n\r\n)/;class tU{constructor(e){var t,n;this.clientOptions=Object.assign(Object.assign({},e),{project:e.project,location:e.location,apiKey:e.apiKey,vertexai:e.vertexai});let l={};this.clientOptions.vertexai?(l.apiVersion=null!=(t=this.clientOptions.apiVersion)?t:"v1beta1",l.baseUrl=this.baseUrlFromProjectLocation(),this.normalizeAuthParameters()):(l.apiVersion=null!=(n=this.clientOptions.apiVersion)?n:"v1beta",l.baseUrl="https://generativelanguage.googleapis.com/"),l.headers=this.getDefaultHeaders(),this.clientOptions.httpOptions=l,e.httpOptions&&(this.clientOptions.httpOptions=this.patchHttpOptions(l,e.httpOptions))}baseUrlFromProjectLocation(){return this.clientOptions.project&&this.clientOptions.location&&"global"!==this.clientOptions.location?`https://${this.clientOptions.location}-aiplatform.googleapis.com/`:"https://aiplatform.googleapis.com/"}normalizeAuthParameters(){if(this.clientOptions.project&&this.clientOptions.location){this.clientOptions.apiKey=void 0;return}this.clientOptions.project=void 0,this.clientOptions.location=void 0}isVertexAI(){var e;return null!=(e=this.clientOptions.vertexai)&&e}getProject(){return this.clientOptions.project}getLocation(){return this.clientOptions.location}getApiVersion(){if(this.clientOptions.httpOptions&&void 0!==this.clientOptions.httpOptions.apiVersion)return this.clientOptions.httpOptions.apiVersion;throw Error("API version is not set.")}getBaseUrl(){if(this.clientOptions.httpOptions&&void 0!==this.clientOptions.httpOptions.baseUrl)return this.clientOptions.httpOptions.baseUrl;throw Error("Base URL is not set.")}getRequestUrl(){return this.getRequestUrlInternal(this.clientOptions.httpOptions)}getHeaders(){if(this.clientOptions.httpOptions&&void 0!==this.clientOptions.httpOptions.headers)return this.clientOptions.httpOptions.headers;throw Error("Headers are not set.")}getRequestUrlInternal(e){if(!e||void 0===e.baseUrl||void 0===e.apiVersion)throw Error("HTTP options are not correctly set.");let t=[e.baseUrl.endsWith("/")?e.baseUrl.slice(0,-1):e.baseUrl];return e.apiVersion&&""!==e.apiVersion&&t.push(e.apiVersion),t.join("/")}getBaseResourcePath(){return`projects/${this.clientOptions.project}/locations/${this.clientOptions.location}`}getApiKey(){return this.clientOptions.apiKey}getWebsocketBaseUrl(){let e=new URL(this.getBaseUrl());return e.protocol="http:"==e.protocol?"ws":"wss",e.toString()}setBaseUrl(e){if(this.clientOptions.httpOptions)this.clientOptions.httpOptions.baseUrl=e;else throw Error("HTTP options are not correctly set.")}constructUrl(e,t,n){let l=[this.getRequestUrlInternal(t)];return n&&l.push(this.getBaseResourcePath()),""!==e&&l.push(e),new URL(`${l.join("/")}`)}shouldPrependVertexProjectPath(e){return!(this.clientOptions.apiKey||!this.clientOptions.vertexai||e.path.startsWith("projects/")||"GET"===e.httpMethod&&e.path.startsWith("publishers/google/models"))}async request(e){let t=this.clientOptions.httpOptions;e.httpOptions&&(t=this.patchHttpOptions(this.clientOptions.httpOptions,e.httpOptions));let n=this.shouldPrependVertexProjectPath(e),l=this.constructUrl(e.path,t,n);if(e.queryParams)for(let[t,n]of Object.entries(e.queryParams))l.searchParams.append(t,String(n));let i={};if("GET"===e.httpMethod){if(e.body&&"{}"!==e.body)throw Error("Request body should be empty for GET request, but got non empty request body")}else i.body=e.body;return i=await this.includeExtraHttpOptionsToRequestInit(i,t,e.abortSignal),this.unaryApiCall(l,i,e.httpMethod)}patchHttpOptions(e,t){let n=JSON.parse(JSON.stringify(e));for(let[e,l]of Object.entries(t))"object"==typeof l?n[e]=Object.assign(Object.assign({},n[e]),l):void 0!==l&&(n[e]=l);return n}async requestStream(e){let t=this.clientOptions.httpOptions;e.httpOptions&&(t=this.patchHttpOptions(this.clientOptions.httpOptions,e.httpOptions));let n=this.shouldPrependVertexProjectPath(e),l=this.constructUrl(e.path,t,n);l.searchParams.has("alt")&&"sse"===l.searchParams.get("alt")||l.searchParams.set("alt","sse");let i={};return i.body=e.body,i=await this.includeExtraHttpOptionsToRequestInit(i,t,e.abortSignal),this.streamApiCall(l,i,e.httpMethod)}async includeExtraHttpOptionsToRequestInit(e,t,n){if(t&&t.timeout||n){let l=new AbortController,i=l.signal;if(t.timeout&&(null==t?void 0:t.timeout)>0){let e=setTimeout(()=>l.abort(),t.timeout);e&&"function"==typeof e.unref&&e.unref()}n&&n.addEventListener("abort",()=>{l.abort()}),e.signal=i}return t&&null!==t.extraBody&&function(e,t){if(!t||0===Object.keys(t).length)return;if(e.body instanceof Blob)return console.warn("includeExtraBodyToRequestInit: extraBody provided but current request body is a Blob. extraBody will be ignored as merging is not supported for Blob bodies.");let n={};if("string"==typeof e.body&&e.body.length>0)try{let t=JSON.parse(e.body);if("object"!=typeof t||null===t||Array.isArray(t))return void console.warn("includeExtraBodyToRequestInit: Original request body is valid JSON but not a non-array object. Skip applying extraBody to the request body.");n=t}catch(e){console.warn("includeExtraBodyToRequestInit: Original request body is not valid JSON. Skip applying extraBody to the request body.");return}e.body=JSON.stringify(function e(t,n){let l=Object.assign({},t);for(let t in n)if(Object.prototype.hasOwnProperty.call(n,t)){let i=n[t],r=l[t];i&&"object"==typeof i&&!Array.isArray(i)&&r&&"object"==typeof r&&!Array.isArray(r)?l[t]=e(r,i):(r&&i&&typeof r!=typeof i&&console.warn(`includeExtraBodyToRequestInit:deepMerge: Type mismatch for key "${t}". Original type: ${typeof r}, New type: ${typeof i}. Overwriting.`),l[t]=i)}return l}(n,t))}(e,t.extraBody),e.headers=await this.getHeadersInternal(t),e}async unaryApiCall(e,t,n){return this.apiCall(e.toString(),Object.assign(Object.assign({},t),{method:n})).then(async e=>(await tk(e),new et(e))).catch(e=>{if(e instanceof Error)throw e;throw Error(JSON.stringify(e))})}async streamApiCall(e,t,n){return this.apiCall(e.toString(),Object.assign(Object.assign({},t),{method:n})).then(async e=>(await tk(e),this.processStreamResponse(e))).catch(e=>{if(e instanceof Error)throw e;throw Error(JSON.stringify(e))})}processStreamResponse(e){var t;return te(this,arguments,function*(){let n=null==(t=null==e?void 0:e.body)?void 0:t.getReader(),l=new TextDecoder("utf-8");if(!n)throw Error("Response body is empty");try{let t="";for(;;){let{done:i,value:r}=yield e7(n.read());if(i){if(t.trim().length>0)throw Error("Incomplete JSON segment at the end");break}let o=l.decode(r,{stream:!0});try{let e=JSON.parse(o);if("error"in e){let t=JSON.parse(JSON.stringify(e.error)),n=t.status,l=t.code,i=`got status: ${n}. ${JSON.stringify(e)}`;if(l>=400&&l<600)throw new to({message:i,status:l})}}catch(e){if("ApiError"===e.name)throw e}let a=(t+=o).match(tx);for(;a;){let n=a[1];try{let l=new Response(n,{headers:null==e?void 0:e.headers,status:null==e?void 0:e.status,statusText:null==e?void 0:e.statusText});yield yield e7(new et(l)),a=(t=t.slice(a[0].length)).match(tx)}catch(e){throw Error(`exception parsing stream chunk ${n}. ${e}`)}}}}finally{n.releaseLock()}})}async apiCall(e,t){return fetch(e,t).catch(e=>{throw Error(`exception ${e} sending request`)})}getDefaultHeaders(){let e={},t="google-genai-sdk/1.13.0 "+this.clientOptions.userAgentExtra;return e["User-Agent"]=t,e[tw]=t,e["Content-Type"]="application/json",e}async getHeadersInternal(e){let t=new Headers;if(e&&e.headers){for(let[n,l]of Object.entries(e.headers))t.append(n,l);e.timeout&&e.timeout>0&&t.append("X-Server-Timeout",String(Math.ceil(e.timeout/1e3)))}return await this.clientOptions.auth.addAuthHeaders(t),t}async uploadFile(e,t){var n;let l={};null!=t&&(l.mimeType=t.mimeType,l.name=t.name,l.displayName=t.displayName),l.name&&!l.name.startsWith("files/")&&(l.name=`files/${l.name}`);let i=this.clientOptions.uploader,r=await i.stat(e);l.sizeBytes=String(r.size);let o=null!=(n=null==t?void 0:t.mimeType)?n:r.type;if(void 0===o||""===o)throw Error("Can not determine mimeType. Please provide mimeType in the config.");l.mimeType=o;let a=await this.fetchUploadUrl(l,t);return i.upload(e,a,this)}async downloadFile(e){let t=this.clientOptions.downloader;await t.download(e,this)}async fetchUploadUrl(e,t){var n;let l={};l=(null==t?void 0:t.httpOptions)?t.httpOptions:{apiVersion:"",headers:{"Content-Type":"application/json","X-Goog-Upload-Protocol":"resumable","X-Goog-Upload-Command":"start","X-Goog-Upload-Header-Content-Length":`${e.sizeBytes}`,"X-Goog-Upload-Header-Content-Type":`${e.mimeType}`}};let i={file:e},r=await this.request({path:z("upload/v1beta/files",i._url),body:JSON.stringify(i),httpMethod:"POST",httpOptions:l});if(!r||!(null==r?void 0:r.headers))throw Error("Server did not return an HttpResponse or the returned HttpResponse did not have headers.");let o=null==(n=null==r?void 0:r.headers)?void 0:n["x-goog-upload-url"];if(void 0===o)throw Error("Failed to get upload url. Server did not return the x-google-upload-url in the headers");return o}}async function tk(e){var t;if(void 0===e)throw Error("response is undefined");if(!e.ok){let n,l=e.status,i=JSON.stringify((null==(t=e.headers.get("content-type"))?void 0:t.includes("application/json"))?await e.json():{error:{message:await e.text(),code:e.status,status:e.statusText}});if(l>=400&&l<600)throw new to({message:i,status:l});throw Error(i)}}function tL(e){for(let n of e){var t;if(null!==(t=n)&&"object"==typeof t&&t instanceof tG||"object"==typeof n&&"inputSchema"in n)return!0}return!1}function tq(e){var t;let n=null!=(t=e[tw])?t:"";e[tw]=(n+" mcp_used/unknown").trimStart()}class tG{constructor(e=[],t){this.mcpTools=[],this.functionNameToMcpClient={},this.mcpClients=e,this.config=t}static create(e,t){return new tG(e,t)}async initialize(){var e,t,n,l;if(this.mcpTools.length>0)return;let i={},r=[];for(let u of this.mcpClients)try{for(var o,a=!0,s=(t=void 0,tt(function(e,t=100){return te(this,arguments,function*(){let n,l=0;for(;l<t;){let t=yield e7(e.listTools({cursor:n}));for(let e of t.tools)yield yield e7(e),l++;if(!t.nextCursor)break;n=t.nextCursor}})}(u)));!(e=(o=await s.next()).done);a=!0){l=o.value,a=!1,r.push(l);let e=l.name;if(i[e])throw Error(`Duplicate function name ${e} found in MCP tools. Please ensure function names are unique.`);i[e]=u}}catch(e){t={error:e}}finally{try{!a&&!e&&(n=s.return)&&await n.call(s)}finally{if(t)throw t.error}}this.mcpTools=r,this.functionNameToMcpClient=i}async tool(){return await this.initialize(),function(e,t={}){let n=[],l=new Set;for(let i of e){let e=i.name;if(l.has(e))throw Error(`Duplicate function name ${e} found in MCP tools. Please ensure function names are unique.`);l.add(e);let r=function(e,t={}){let n={name:e.name,description:e.description,parametersJsonSchema:e.inputSchema};return t.behavior&&(n.behavior=t.behavior),{functionDeclarations:[n]}}(i,t);r.functionDeclarations&&n.push(...r.functionDeclarations)}return{functionDeclarations:n}}(this.mcpTools,this.config)}async callTool(e){await this.initialize();let t=[];for(let n of e)if(n.name in this.functionNameToMcpClient){let e,l=this.functionNameToMcpClient[n.name];this.config.timeout&&(e={timeout:this.config.timeout});let i=await l.callTool({name:n.name,arguments:n.args},void 0,e);t.push({functionResponse:{name:n.name,response:i.isError?{error:i}:i}})}return t}}async function tH(e,t,n){let l,i=new eA;Object.assign(i,function(e){let t={};null!=Q(e,["setupComplete"])&&X(t,["setupComplete"],{});let n=Q(e,["serverContent"]);null!=n&&X(t,["serverContent"],function(e){let t={},n=Q(e,["audioChunks"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["data"]);null!=n&&X(t,["data"],n);let l=Q(e,["mimeType"]);null!=l&&X(t,["mimeType"],l);let i=Q(e,["sourceMetadata"]);return null!=i&&X(t,["sourceMetadata"],function(e){let t={},n=Q(e,["clientContent"]);null!=n&&X(t,["clientContent"],function(e){let t={},n=Q(e,["weightedPrompts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["text"]);null!=n&&X(t,["text"],n);let l=Q(e,["weight"]);return null!=l&&X(t,["weight"],l),t})(e))),X(t,["weightedPrompts"],e)}return t}(n));let l=Q(e,["musicGenerationConfig"]);return null!=l&&X(t,["musicGenerationConfig"],function(e){let t={},n=Q(e,["temperature"]);null!=n&&X(t,["temperature"],n);let l=Q(e,["topK"]);null!=l&&X(t,["topK"],l);let i=Q(e,["seed"]);null!=i&&X(t,["seed"],i);let r=Q(e,["guidance"]);null!=r&&X(t,["guidance"],r);let o=Q(e,["bpm"]);null!=o&&X(t,["bpm"],o);let a=Q(e,["density"]);null!=a&&X(t,["density"],a);let s=Q(e,["brightness"]);null!=s&&X(t,["brightness"],s);let u=Q(e,["scale"]);null!=u&&X(t,["scale"],u);let p=Q(e,["muteBass"]);null!=p&&X(t,["muteBass"],p);let d=Q(e,["muteDrums"]);null!=d&&X(t,["muteDrums"],d);let c=Q(e,["onlyBassAndDrums"]);null!=c&&X(t,["onlyBassAndDrums"],c);let f=Q(e,["musicGenerationMode"]);return null!=f&&X(t,["musicGenerationMode"],f),t}(l)),t}(i)),t})(e))),X(t,["audioChunks"],e)}return t}(n));let l=Q(e,["filteredPrompt"]);return null!=l&&X(t,["filteredPrompt"],function(e){let t={},n=Q(e,["text"]);null!=n&&X(t,["text"],n);let l=Q(e,["filteredReason"]);return null!=l&&X(t,["filteredReason"],l),t}(l)),t}(n.data instanceof Blob?JSON.parse(await n.data.text()):JSON.parse(n.data))),t(i)}class tF{constructor(e,t,n){this.apiClient=e,this.auth=t,this.webSocketFactory=n}async connect(e){var t,n;if(this.apiClient.isVertexAI())throw Error("Live music is not supported for Vertex AI.");console.warn("Live music generation is experimental and may change in future versions.");let l=this.apiClient.getWebsocketBaseUrl(),i=this.apiClient.getApiVersion(),r=function(e){let t=new Headers;for(let[n,l]of Object.entries(e))t.append(n,l);return t}(this.apiClient.getDefaultHeaders()),o=this.apiClient.getApiKey(),a=`${l}/ws/google.ai.generativelanguage.${i}.GenerativeService.BidiGenerateMusic?key=${o}`,s=()=>{},u=new Promise(e=>{s=e}),p=e.callbacks,d=this.apiClient,c={onopen:function(){s({})},onmessage:e=>{tH(d,p.onmessage,e)},onerror:null!=(t=null==p?void 0:p.onerror)?t:function(e){},onclose:null!=(n=null==p?void 0:p.onclose)?n:function(e){}},f=this.webSocketFactory.create(a,function(e){let t={};return e.forEach((e,n)=>{t[n]=e}),t}(r),c);f.connect(),await u;let m=tm({setup:tc({model:e_(this.apiClient,e.model)})});return f.send(JSON.stringify(m)),new tV(f,this.apiClient)}}class tV{constructor(e,t){this.conn=e,this.apiClient=t}async setWeightedPrompts(e){if(!e.weightedPrompts||0===Object.keys(e.weightedPrompts).length)throw Error("Weighted prompts must be set and contain at least one entry.");let t=tf(function(e){let t={},n=Q(e,["weightedPrompts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>tp(e))),X(t,["weightedPrompts"],e)}return t}(e));this.conn.send(JSON.stringify({clientContent:t}))}async setMusicGenerationConfig(e){e.musicGenerationConfig||(e.musicGenerationConfig={});let t=tm(function(e){let t={},n=Q(e,["musicGenerationConfig"]);return null!=n&&X(t,["musicGenerationConfig"],td(n)),t}(e));this.conn.send(JSON.stringify(t))}sendPlaybackControl(e){let t=tm({playbackControl:e});this.conn.send(JSON.stringify(t))}play(){this.sendPlaybackControl(B.PLAY)}pause(){this.sendPlaybackControl(B.PAUSE)}stop(){this.sendPlaybackControl(B.STOP)}resetContext(){this.sendPlaybackControl(B.RESET_CONTEXT)}close(){this.conn.close()}}async function tj(e,t,n){let l,i=new eE,r=JSON.parse(n.data instanceof Blob?await n.data.text():n.data instanceof ArrayBuffer?new TextDecoder().decode(n.data):n.data);e.isVertexAI()?Object.assign(i,function(e){let t={},n=Q(e,["setupComplete"]);null!=n&&X(t,["setupComplete"],function(e){let t={},n=Q(e,["sessionId"]);return null!=n&&X(t,["sessionId"],n),t}(n));let l=Q(e,["serverContent"]);null!=l&&X(t,["serverContent"],function(e){let t={},n=Q(e,["modelTurn"]);null!=n&&X(t,["modelTurn"],function(e){let t={},n=Q(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["videoMetadata"]);null!=n&&X(t,["videoMetadata"],function(e){let t={},n=Q(e,["fps"]);null!=n&&X(t,["fps"],n);let l=Q(e,["endOffset"]);null!=l&&X(t,["endOffset"],l);let i=Q(e,["startOffset"]);return null!=i&&X(t,["startOffset"],i),t}(n));let l=Q(e,["thought"]);null!=l&&X(t,["thought"],l);let i=Q(e,["inlineData"]);null!=i&&X(t,["inlineData"],function(e){let t={},n=Q(e,["displayName"]);null!=n&&X(t,["displayName"],n);let l=Q(e,["data"]);null!=l&&X(t,["data"],l);let i=Q(e,["mimeType"]);return null!=i&&X(t,["mimeType"],i),t}(i));let r=Q(e,["fileData"]);null!=r&&X(t,["fileData"],function(e){let t={},n=Q(e,["displayName"]);null!=n&&X(t,["displayName"],n);let l=Q(e,["fileUri"]);null!=l&&X(t,["fileUri"],l);let i=Q(e,["mimeType"]);return null!=i&&X(t,["mimeType"],i),t}(r));let o=Q(e,["thoughtSignature"]);null!=o&&X(t,["thoughtSignature"],o);let a=Q(e,["codeExecutionResult"]);null!=a&&X(t,["codeExecutionResult"],a);let s=Q(e,["executableCode"]);null!=s&&X(t,["executableCode"],s);let u=Q(e,["functionCall"]);null!=u&&X(t,["functionCall"],u);let p=Q(e,["functionResponse"]);null!=p&&X(t,["functionResponse"],p);let d=Q(e,["text"]);return null!=d&&X(t,["text"],d),t})(e))),X(t,["parts"],e)}let l=Q(e,["role"]);return null!=l&&X(t,["role"],l),t}(n));let l=Q(e,["turnComplete"]);null!=l&&X(t,["turnComplete"],l);let i=Q(e,["interrupted"]);null!=i&&X(t,["interrupted"],i);let r=Q(e,["groundingMetadata"]);null!=r&&X(t,["groundingMetadata"],r);let o=Q(e,["generationComplete"]);null!=o&&X(t,["generationComplete"],o);let a=Q(e,["inputTranscription"]);null!=a&&X(t,["inputTranscription"],ty(a));let s=Q(e,["outputTranscription"]);return null!=s&&X(t,["outputTranscription"],ty(s)),t}(l));let i=Q(e,["toolCall"]);null!=i&&X(t,["toolCall"],function(e){let t={},n=Q(e,["functionCalls"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["args"]);null!=n&&X(t,["args"],n);let l=Q(e,["name"]);return null!=l&&X(t,["name"],l),t})(e))),X(t,["functionCalls"],e)}return t}(i));let r=Q(e,["toolCallCancellation"]);null!=r&&X(t,["toolCallCancellation"],function(e){let t={},n=Q(e,["ids"]);return null!=n&&X(t,["ids"],n),t}(r));let o=Q(e,["usageMetadata"]);null!=o&&X(t,["usageMetadata"],function(e){let t={},n=Q(e,["promptTokenCount"]);null!=n&&X(t,["promptTokenCount"],n);let l=Q(e,["cachedContentTokenCount"]);null!=l&&X(t,["cachedContentTokenCount"],l);let i=Q(e,["candidatesTokenCount"]);null!=i&&X(t,["responseTokenCount"],i);let r=Q(e,["toolUsePromptTokenCount"]);null!=r&&X(t,["toolUsePromptTokenCount"],r);let o=Q(e,["thoughtsTokenCount"]);null!=o&&X(t,["thoughtsTokenCount"],o);let a=Q(e,["totalTokenCount"]);null!=a&&X(t,["totalTokenCount"],a);let s=Q(e,["promptTokensDetails"]);if(null!=s){let e=s;Array.isArray(e)&&(e=e.map(e=>tC(e))),X(t,["promptTokensDetails"],e)}let u=Q(e,["cacheTokensDetails"]);if(null!=u){let e=u;Array.isArray(e)&&(e=e.map(e=>tC(e))),X(t,["cacheTokensDetails"],e)}let p=Q(e,["candidatesTokensDetails"]);if(null!=p){let e=p;Array.isArray(e)&&(e=e.map(e=>tC(e))),X(t,["responseTokensDetails"],e)}let d=Q(e,["toolUsePromptTokensDetails"]);if(null!=d){let e=d;Array.isArray(e)&&(e=e.map(e=>tC(e))),X(t,["toolUsePromptTokensDetails"],e)}let c=Q(e,["trafficType"]);return null!=c&&X(t,["trafficType"],c),t}(o));let a=Q(e,["goAway"]);null!=a&&X(t,["goAway"],function(e){let t={},n=Q(e,["timeLeft"]);return null!=n&&X(t,["timeLeft"],n),t}(a));let s=Q(e,["sessionResumptionUpdate"]);return null!=s&&X(t,["sessionResumptionUpdate"],function(e){let t={},n=Q(e,["newHandle"]);null!=n&&X(t,["newHandle"],n);let l=Q(e,["resumable"]);null!=l&&X(t,["resumable"],l);let i=Q(e,["lastConsumedClientMessageIndex"]);return null!=i&&X(t,["lastConsumedClientMessageIndex"],i),t}(s)),t}(r)):Object.assign(i,function(e){let t={};null!=Q(e,["setupComplete"])&&X(t,["setupComplete"],{});let n=Q(e,["serverContent"]);null!=n&&X(t,["serverContent"],function(e){let t={},n=Q(e,["modelTurn"]);null!=n&&X(t,["modelTurn"],function(e){let t={},n=Q(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["videoMetadata"]);null!=n&&X(t,["videoMetadata"],function(e){let t={},n=Q(e,["fps"]);null!=n&&X(t,["fps"],n);let l=Q(e,["endOffset"]);null!=l&&X(t,["endOffset"],l);let i=Q(e,["startOffset"]);return null!=i&&X(t,["startOffset"],i),t}(n));let l=Q(e,["thought"]);null!=l&&X(t,["thought"],l);let i=Q(e,["inlineData"]);null!=i&&X(t,["inlineData"],function(e){let t={},n=Q(e,["data"]);null!=n&&X(t,["data"],n);let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(i));let r=Q(e,["fileData"]);null!=r&&X(t,["fileData"],function(e){let t={},n=Q(e,["fileUri"]);null!=n&&X(t,["fileUri"],n);let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(r));let o=Q(e,["thoughtSignature"]);null!=o&&X(t,["thoughtSignature"],o);let a=Q(e,["codeExecutionResult"]);null!=a&&X(t,["codeExecutionResult"],a);let s=Q(e,["executableCode"]);null!=s&&X(t,["executableCode"],s);let u=Q(e,["functionCall"]);null!=u&&X(t,["functionCall"],u);let p=Q(e,["functionResponse"]);null!=p&&X(t,["functionResponse"],p);let d=Q(e,["text"]);return null!=d&&X(t,["text"],d),t})(e))),X(t,["parts"],e)}let l=Q(e,["role"]);return null!=l&&X(t,["role"],l),t}(n));let l=Q(e,["turnComplete"]);null!=l&&X(t,["turnComplete"],l);let i=Q(e,["interrupted"]);null!=i&&X(t,["interrupted"],i);let r=Q(e,["groundingMetadata"]);null!=r&&X(t,["groundingMetadata"],r);let o=Q(e,["generationComplete"]);null!=o&&X(t,["generationComplete"],o);let a=Q(e,["inputTranscription"]);null!=a&&X(t,["inputTranscription"],th(a));let s=Q(e,["outputTranscription"]);null!=s&&X(t,["outputTranscription"],th(s));let u=Q(e,["urlContextMetadata"]);return null!=u&&X(t,["urlContextMetadata"],function(e){let t={},n=Q(e,["urlMetadata"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["retrievedUrl"]);null!=n&&X(t,["retrievedUrl"],n);let l=Q(e,["urlRetrievalStatus"]);return null!=l&&X(t,["urlRetrievalStatus"],l),t})(e))),X(t,["urlMetadata"],e)}return t}(u)),t}(n));let l=Q(e,["toolCall"]);null!=l&&X(t,["toolCall"],function(e){let t={},n=Q(e,["functionCalls"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["id"]);null!=n&&X(t,["id"],n);let l=Q(e,["args"]);null!=l&&X(t,["args"],l);let i=Q(e,["name"]);return null!=i&&X(t,["name"],i),t})(e))),X(t,["functionCalls"],e)}return t}(l));let i=Q(e,["toolCallCancellation"]);null!=i&&X(t,["toolCallCancellation"],function(e){let t={},n=Q(e,["ids"]);return null!=n&&X(t,["ids"],n),t}(i));let r=Q(e,["usageMetadata"]);null!=r&&X(t,["usageMetadata"],function(e){let t={},n=Q(e,["promptTokenCount"]);null!=n&&X(t,["promptTokenCount"],n);let l=Q(e,["cachedContentTokenCount"]);null!=l&&X(t,["cachedContentTokenCount"],l);let i=Q(e,["responseTokenCount"]);null!=i&&X(t,["responseTokenCount"],i);let r=Q(e,["toolUsePromptTokenCount"]);null!=r&&X(t,["toolUsePromptTokenCount"],r);let o=Q(e,["thoughtsTokenCount"]);null!=o&&X(t,["thoughtsTokenCount"],o);let a=Q(e,["totalTokenCount"]);null!=a&&X(t,["totalTokenCount"],a);let s=Q(e,["promptTokensDetails"]);if(null!=s){let e=s;Array.isArray(e)&&(e=e.map(e=>tg(e))),X(t,["promptTokensDetails"],e)}let u=Q(e,["cacheTokensDetails"]);if(null!=u){let e=u;Array.isArray(e)&&(e=e.map(e=>tg(e))),X(t,["cacheTokensDetails"],e)}let p=Q(e,["responseTokensDetails"]);if(null!=p){let e=p;Array.isArray(e)&&(e=e.map(e=>tg(e))),X(t,["responseTokensDetails"],e)}let d=Q(e,["toolUsePromptTokensDetails"]);if(null!=d){let e=d;Array.isArray(e)&&(e=e.map(e=>tg(e))),X(t,["toolUsePromptTokensDetails"],e)}return t}(r));let o=Q(e,["goAway"]);null!=o&&X(t,["goAway"],function(e){let t={},n=Q(e,["timeLeft"]);return null!=n&&X(t,["timeLeft"],n),t}(o));let a=Q(e,["sessionResumptionUpdate"]);return null!=a&&X(t,["sessionResumptionUpdate"],function(e){let t={},n=Q(e,["newHandle"]);null!=n&&X(t,["newHandle"],n);let l=Q(e,["resumable"]);null!=l&&X(t,["resumable"],l);let i=Q(e,["lastConsumedClientMessageIndex"]);return null!=i&&X(t,["lastConsumedClientMessageIndex"],i),t}(a)),t}(r)),t(i)}class tJ{constructor(e,t,n){this.apiClient=e,this.auth=t,this.webSocketFactory=n,this.music=new tF(this.apiClient,this.auth,this.webSocketFactory)}async connect(e){var t,n,l,i,r,o;let a;if(e.config&&e.config.httpOptions)throw Error("The Live module does not support httpOptions at request-level in LiveConnectConfig yet. Please use the client-level httpOptions configuration instead.");let s=this.apiClient.getWebsocketBaseUrl(),u=this.apiClient.getApiVersion(),p=this.apiClient.getHeaders();e.config&&e.config.tools&&tL(e.config.tools)&&tq(p);let d=function(e){let t=new Headers;for(let[n,l]of Object.entries(e))t.append(n,l);return t}(p);if(this.apiClient.isVertexAI())a=`${s}/ws/google.cloud.aiplatform.${u}.LlmBidiService/BidiGenerateContent`,await this.auth.addAuthHeaders(d);else{let e=this.apiClient.getApiKey(),t="BidiGenerateContent",n="key";(null==e?void 0:e.startsWith("auth_tokens/"))&&(console.warn("Warning: Ephemeral token support is experimental and may change in future versions."),"v1alpha"!==u&&console.warn("Warning: The SDK's ephemeral token support is in v1alpha only. Please use const ai = new GoogleGenAI({apiKey: token.name, httpOptions: { apiVersion: 'v1alpha' }}); before session connection."),t="BidiGenerateContentConstrained",n="access_token"),a=`${s}/ws/google.ai.generativelanguage.${u}.GenerativeService.${t}?${n}=${e}`}let c=()=>{},f=new Promise(e=>{c=e}),m=e.callbacks,h=this.apiClient,g={onopen:function(){var e;null==(e=null==m?void 0:m.onopen)||e.call(m),c({})},onmessage:e=>{tj(h,m.onmessage,e)},onerror:null!=(t=null==m?void 0:m.onerror)?t:function(e){},onclose:null!=(n=null==m?void 0:m.onclose)?n:function(e){}},y=this.webSocketFactory.create(a,function(e){let t={};return e.forEach((e,n)=>{t[n]=e}),t}(d),g);y.connect(),await f;let C=e_(this.apiClient,e.model);if(this.apiClient.isVertexAI()&&C.startsWith("publishers/")){let e=this.apiClient.getProject(),t=this.apiClient.getLocation();C=`projects/${e}/locations/${t}/`+C}let E={};this.apiClient.isVertexAI()&&(null==(l=e.config)?void 0:l.responseModalities)===void 0&&(void 0===e.config?e.config={responseModalities:[T.AUDIO]}:e.config.responseModalities=[T.AUDIO]),(null==(i=e.config)?void 0:i.generationConfig)&&console.warn("Setting `LiveConnectConfig.generation_config` is deprecated, please set the fields on `LiveConnectConfig` directly. This will become an error in a future version (not before Q3 2025).");let v=null!=(o=null==(r=e.config)?void 0:r.tools)?o:[],A=[];for(let e of v)this.isCallableTool(e)?A.push(await e.tool()):A.push(e);A.length>0&&(e.config.tools=A);let _={model:C,config:e.config,callbacks:e.callbacks};return E=this.apiClient.isVertexAI()?function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["setup","model"],e_(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],function(e,t){let n=Q(e,["generationConfig"]);void 0!==t&&null!=n&&X(t,["setup","generationConfig"],n);let l=Q(e,["responseModalities"]);void 0!==t&&null!=l&&X(t,["setup","generationConfig","responseModalities"],l);let i=Q(e,["temperature"]);void 0!==t&&null!=i&&X(t,["setup","generationConfig","temperature"],i);let r=Q(e,["topP"]);void 0!==t&&null!=r&&X(t,["setup","generationConfig","topP"],r);let o=Q(e,["topK"]);void 0!==t&&null!=o&&X(t,["setup","generationConfig","topK"],o);let a=Q(e,["maxOutputTokens"]);void 0!==t&&null!=a&&X(t,["setup","generationConfig","maxOutputTokens"],a);let s=Q(e,["mediaResolution"]);void 0!==t&&null!=s&&X(t,["setup","generationConfig","mediaResolution"],s);let u=Q(e,["seed"]);void 0!==t&&null!=u&&X(t,["setup","generationConfig","seed"],u);let p=Q(e,["speechConfig"]);void 0!==t&&null!=p&&X(t,["setup","generationConfig","speechConfig"],function(e){let t={},n=Q(e,["voiceConfig"]);if(null!=n&&X(t,["voiceConfig"],function(e){let t={},n=Q(e,["prebuiltVoiceConfig"]);return null!=n&&X(t,["prebuiltVoiceConfig"],function(e){let t={},n=Q(e,["voiceName"]);return null!=n&&X(t,["voiceName"],n),t}(n)),t}(n)),void 0!==Q(e,["multiSpeakerVoiceConfig"]))throw Error("multiSpeakerVoiceConfig parameter is not supported in Vertex AI.");let l=Q(e,["languageCode"]);return null!=l&&X(t,["languageCode"],l),t}(eG(p)));let d=Q(e,["enableAffectiveDialog"]);void 0!==t&&null!=d&&X(t,["setup","generationConfig","enableAffectiveDialog"],d);let c=Q(e,["systemInstruction"]);void 0!==t&&null!=c&&X(t,["setup","systemInstruction"],function(e){let t={},n=Q(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["videoMetadata"]);null!=n&&X(t,["videoMetadata"],function(e){let t={},n=Q(e,["fps"]);null!=n&&X(t,["fps"],n);let l=Q(e,["endOffset"]);null!=l&&X(t,["endOffset"],l);let i=Q(e,["startOffset"]);return null!=i&&X(t,["startOffset"],i),t}(n));let l=Q(e,["thought"]);null!=l&&X(t,["thought"],l);let i=Q(e,["inlineData"]);null!=i&&X(t,["inlineData"],function(e){let t={},n=Q(e,["displayName"]);null!=n&&X(t,["displayName"],n);let l=Q(e,["data"]);null!=l&&X(t,["data"],l);let i=Q(e,["mimeType"]);return null!=i&&X(t,["mimeType"],i),t}(i));let r=Q(e,["fileData"]);null!=r&&X(t,["fileData"],function(e){let t={},n=Q(e,["displayName"]);null!=n&&X(t,["displayName"],n);let l=Q(e,["fileUri"]);null!=l&&X(t,["fileUri"],l);let i=Q(e,["mimeType"]);return null!=i&&X(t,["mimeType"],i),t}(r));let o=Q(e,["thoughtSignature"]);null!=o&&X(t,["thoughtSignature"],o);let a=Q(e,["codeExecutionResult"]);null!=a&&X(t,["codeExecutionResult"],a);let s=Q(e,["executableCode"]);null!=s&&X(t,["executableCode"],s);let u=Q(e,["functionCall"]);null!=u&&X(t,["functionCall"],u);let p=Q(e,["functionResponse"]);null!=p&&X(t,["functionResponse"],p);let d=Q(e,["text"]);return null!=d&&X(t,["text"],d),t})(e))),X(t,["parts"],e)}let l=Q(e,["role"]);return null!=l&&X(t,["role"],l),t}(ex(c)));let f=Q(e,["tools"]);if(void 0!==t&&null!=f){let e=eF(f);Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["functionDeclarations"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={};if(void 0!==Q(e,["behavior"]))throw Error("behavior parameter is not supported in Vertex AI.");let n=Q(e,["description"]);null!=n&&X(t,["description"],n);let l=Q(e,["name"]);null!=l&&X(t,["name"],l);let i=Q(e,["parameters"]);null!=i&&X(t,["parameters"],i);let r=Q(e,["parametersJsonSchema"]);null!=r&&X(t,["parametersJsonSchema"],r);let o=Q(e,["response"]);null!=o&&X(t,["response"],o);let a=Q(e,["responseJsonSchema"]);return null!=a&&X(t,["responseJsonSchema"],a),t})(e))),X(t,["functionDeclarations"],e)}let l=Q(e,["retrieval"]);null!=l&&X(t,["retrieval"],l);let i=Q(e,["googleSearch"]);null!=i&&X(t,["googleSearch"],function(e){let t={},n=Q(e,["timeRangeFilter"]);return null!=n&&X(t,["timeRangeFilter"],function(e){let t={},n=Q(e,["startTime"]);null!=n&&X(t,["startTime"],n);let l=Q(e,["endTime"]);return null!=l&&X(t,["endTime"],l),t}(n)),t}(i));let r=Q(e,["googleSearchRetrieval"]);null!=r&&X(t,["googleSearchRetrieval"],function(e){let t={},n=Q(e,["dynamicRetrievalConfig"]);return null!=n&&X(t,["dynamicRetrievalConfig"],function(e){let t={},n=Q(e,["mode"]);null!=n&&X(t,["mode"],n);let l=Q(e,["dynamicThreshold"]);return null!=l&&X(t,["dynamicThreshold"],l),t}(n)),t}(r)),null!=Q(e,["enterpriseWebSearch"])&&X(t,["enterpriseWebSearch"],{});let o=Q(e,["googleMaps"]);null!=o&&X(t,["googleMaps"],function(e){let t={},n=Q(e,["authConfig"]);return null!=n&&X(t,["authConfig"],function(e){let t={},n=Q(e,["apiKeyConfig"]);null!=n&&X(t,["apiKeyConfig"],function(e){let t={},n=Q(e,["apiKeyString"]);return null!=n&&X(t,["apiKeyString"],n),t}(n));let l=Q(e,["authType"]);null!=l&&X(t,["authType"],l);let i=Q(e,["googleServiceAccountConfig"]);null!=i&&X(t,["googleServiceAccountConfig"],i);let r=Q(e,["httpBasicAuthConfig"]);null!=r&&X(t,["httpBasicAuthConfig"],r);let o=Q(e,["oauthConfig"]);null!=o&&X(t,["oauthConfig"],o);let a=Q(e,["oidcConfig"]);return null!=a&&X(t,["oidcConfig"],a),t}(n)),t}(o)),null!=Q(e,["urlContext"])&&X(t,["urlContext"],{});let a=Q(e,["codeExecution"]);null!=a&&X(t,["codeExecution"],a);let s=Q(e,["computerUse"]);return null!=s&&X(t,["computerUse"],s),t})(eH(e)))),X(t,["setup","tools"],e)}let m=Q(e,["sessionResumption"]);void 0!==t&&null!=m&&X(t,["setup","sessionResumption"],function(e){let t={},n=Q(e,["handle"]);null!=n&&X(t,["handle"],n);let l=Q(e,["transparent"]);return null!=l&&X(t,["transparent"],l),t}(m));let h=Q(e,["inputAudioTranscription"]);void 0!==t&&null!=h&&X(t,["setup","inputAudioTranscription"],{});let g=Q(e,["outputAudioTranscription"]);void 0!==t&&null!=g&&X(t,["setup","outputAudioTranscription"],{});let y=Q(e,["realtimeInputConfig"]);void 0!==t&&null!=y&&X(t,["setup","realtimeInputConfig"],function(e){let t={},n=Q(e,["automaticActivityDetection"]);null!=n&&X(t,["automaticActivityDetection"],function(e){let t={},n=Q(e,["disabled"]);null!=n&&X(t,["disabled"],n);let l=Q(e,["startOfSpeechSensitivity"]);null!=l&&X(t,["startOfSpeechSensitivity"],l);let i=Q(e,["endOfSpeechSensitivity"]);null!=i&&X(t,["endOfSpeechSensitivity"],i);let r=Q(e,["prefixPaddingMs"]);null!=r&&X(t,["prefixPaddingMs"],r);let o=Q(e,["silenceDurationMs"]);return null!=o&&X(t,["silenceDurationMs"],o),t}(n));let l=Q(e,["activityHandling"]);null!=l&&X(t,["activityHandling"],l);let i=Q(e,["turnCoverage"]);return null!=i&&X(t,["turnCoverage"],i),t}(y));let C=Q(e,["contextWindowCompression"]);void 0!==t&&null!=C&&X(t,["setup","contextWindowCompression"],function(e){let t={},n=Q(e,["triggerTokens"]);null!=n&&X(t,["triggerTokens"],n);let l=Q(e,["slidingWindow"]);return null!=l&&X(t,["slidingWindow"],function(e){let t={},n=Q(e,["targetTokens"]);return null!=n&&X(t,["targetTokens"],n),t}(l)),t}(C));let T=Q(e,["proactivity"]);return void 0!==t&&null!=T&&X(t,["setup","proactivity"],function(e){let t={},n=Q(e,["proactiveAudio"]);return null!=n&&X(t,["proactiveAudio"],n),t}(T)),{}}(i,n)),n}(this.apiClient,_):function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["setup","model"],e_(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],function(e,t){let n=Q(e,["generationConfig"]);void 0!==t&&null!=n&&X(t,["setup","generationConfig"],n);let l=Q(e,["responseModalities"]);void 0!==t&&null!=l&&X(t,["setup","generationConfig","responseModalities"],l);let i=Q(e,["temperature"]);void 0!==t&&null!=i&&X(t,["setup","generationConfig","temperature"],i);let r=Q(e,["topP"]);void 0!==t&&null!=r&&X(t,["setup","generationConfig","topP"],r);let o=Q(e,["topK"]);void 0!==t&&null!=o&&X(t,["setup","generationConfig","topK"],o);let a=Q(e,["maxOutputTokens"]);void 0!==t&&null!=a&&X(t,["setup","generationConfig","maxOutputTokens"],a);let s=Q(e,["mediaResolution"]);void 0!==t&&null!=s&&X(t,["setup","generationConfig","mediaResolution"],s);let u=Q(e,["seed"]);void 0!==t&&null!=u&&X(t,["setup","generationConfig","seed"],u);let p=Q(e,["speechConfig"]);void 0!==t&&null!=p&&X(t,["setup","generationConfig","speechConfig"],function(e){let t={},n=Q(e,["voiceConfig"]);null!=n&&X(t,["voiceConfig"],tu(n));let l=Q(e,["multiSpeakerVoiceConfig"]);null!=l&&X(t,["multiSpeakerVoiceConfig"],function(e){let t={},n=Q(e,["speakerVoiceConfigs"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["speaker"]);null!=n&&X(t,["speaker"],n);let l=Q(e,["voiceConfig"]);return null!=l&&X(t,["voiceConfig"],tu(l)),t})(e))),X(t,["speakerVoiceConfigs"],e)}return t}(l));let i=Q(e,["languageCode"]);return null!=i&&X(t,["languageCode"],i),t}(eG(p)));let d=Q(e,["enableAffectiveDialog"]);void 0!==t&&null!=d&&X(t,["setup","generationConfig","enableAffectiveDialog"],d);let c=Q(e,["systemInstruction"]);void 0!==t&&null!=c&&X(t,["setup","systemInstruction"],function(e){let t={},n=Q(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["videoMetadata"]);null!=n&&X(t,["videoMetadata"],function(e){let t={},n=Q(e,["fps"]);null!=n&&X(t,["fps"],n);let l=Q(e,["endOffset"]);null!=l&&X(t,["endOffset"],l);let i=Q(e,["startOffset"]);return null!=i&&X(t,["startOffset"],i),t}(n));let l=Q(e,["thought"]);null!=l&&X(t,["thought"],l);let i=Q(e,["inlineData"]);null!=i&&X(t,["inlineData"],function(e){let t={};if(void 0!==Q(e,["displayName"]))throw Error("displayName parameter is not supported in Gemini API.");let n=Q(e,["data"]);null!=n&&X(t,["data"],n);let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(i));let r=Q(e,["fileData"]);null!=r&&X(t,["fileData"],function(e){let t={};if(void 0!==Q(e,["displayName"]))throw Error("displayName parameter is not supported in Gemini API.");let n=Q(e,["fileUri"]);null!=n&&X(t,["fileUri"],n);let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(r));let o=Q(e,["thoughtSignature"]);null!=o&&X(t,["thoughtSignature"],o);let a=Q(e,["codeExecutionResult"]);null!=a&&X(t,["codeExecutionResult"],a);let s=Q(e,["executableCode"]);null!=s&&X(t,["executableCode"],s);let u=Q(e,["functionCall"]);null!=u&&X(t,["functionCall"],u);let p=Q(e,["functionResponse"]);null!=p&&X(t,["functionResponse"],p);let d=Q(e,["text"]);return null!=d&&X(t,["text"],d),t})(e))),X(t,["parts"],e)}let l=Q(e,["role"]);return null!=l&&X(t,["role"],l),t}(ex(c)));let f=Q(e,["tools"]);if(void 0!==t&&null!=f){let e=eF(f);Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["functionDeclarations"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["behavior"]);null!=n&&X(t,["behavior"],n);let l=Q(e,["description"]);null!=l&&X(t,["description"],l);let i=Q(e,["name"]);null!=i&&X(t,["name"],i);let r=Q(e,["parameters"]);null!=r&&X(t,["parameters"],r);let o=Q(e,["parametersJsonSchema"]);null!=o&&X(t,["parametersJsonSchema"],o);let a=Q(e,["response"]);null!=a&&X(t,["response"],a);let s=Q(e,["responseJsonSchema"]);return null!=s&&X(t,["responseJsonSchema"],s),t})(e))),X(t,["functionDeclarations"],e)}if(void 0!==Q(e,["retrieval"]))throw Error("retrieval parameter is not supported in Gemini API.");let l=Q(e,["googleSearch"]);null!=l&&X(t,["googleSearch"],function(e){let t={},n=Q(e,["timeRangeFilter"]);return null!=n&&X(t,["timeRangeFilter"],function(e){let t={},n=Q(e,["startTime"]);null!=n&&X(t,["startTime"],n);let l=Q(e,["endTime"]);return null!=l&&X(t,["endTime"],l),t}(n)),t}(l));let i=Q(e,["googleSearchRetrieval"]);if(null!=i&&X(t,["googleSearchRetrieval"],function(e){let t={},n=Q(e,["dynamicRetrievalConfig"]);return null!=n&&X(t,["dynamicRetrievalConfig"],function(e){let t={},n=Q(e,["mode"]);null!=n&&X(t,["mode"],n);let l=Q(e,["dynamicThreshold"]);return null!=l&&X(t,["dynamicThreshold"],l),t}(n)),t}(i)),void 0!==Q(e,["enterpriseWebSearch"]))throw Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(void 0!==Q(e,["googleMaps"]))throw Error("googleMaps parameter is not supported in Gemini API.");null!=Q(e,["urlContext"])&&X(t,["urlContext"],{});let r=Q(e,["codeExecution"]);null!=r&&X(t,["codeExecution"],r);let o=Q(e,["computerUse"]);return null!=o&&X(t,["computerUse"],o),t})(eH(e)))),X(t,["setup","tools"],e)}let m=Q(e,["sessionResumption"]);void 0!==t&&null!=m&&X(t,["setup","sessionResumption"],function(e){let t={},n=Q(e,["handle"]);if(null!=n&&X(t,["handle"],n),void 0!==Q(e,["transparent"]))throw Error("transparent parameter is not supported in Gemini API.");return t}(m));let h=Q(e,["inputAudioTranscription"]);void 0!==t&&null!=h&&X(t,["setup","inputAudioTranscription"],{});let g=Q(e,["outputAudioTranscription"]);void 0!==t&&null!=g&&X(t,["setup","outputAudioTranscription"],{});let y=Q(e,["realtimeInputConfig"]);void 0!==t&&null!=y&&X(t,["setup","realtimeInputConfig"],function(e){let t={},n=Q(e,["automaticActivityDetection"]);null!=n&&X(t,["automaticActivityDetection"],function(e){let t={},n=Q(e,["disabled"]);null!=n&&X(t,["disabled"],n);let l=Q(e,["startOfSpeechSensitivity"]);null!=l&&X(t,["startOfSpeechSensitivity"],l);let i=Q(e,["endOfSpeechSensitivity"]);null!=i&&X(t,["endOfSpeechSensitivity"],i);let r=Q(e,["prefixPaddingMs"]);null!=r&&X(t,["prefixPaddingMs"],r);let o=Q(e,["silenceDurationMs"]);return null!=o&&X(t,["silenceDurationMs"],o),t}(n));let l=Q(e,["activityHandling"]);null!=l&&X(t,["activityHandling"],l);let i=Q(e,["turnCoverage"]);return null!=i&&X(t,["turnCoverage"],i),t}(y));let C=Q(e,["contextWindowCompression"]);void 0!==t&&null!=C&&X(t,["setup","contextWindowCompression"],function(e){let t={},n=Q(e,["triggerTokens"]);null!=n&&X(t,["triggerTokens"],n);let l=Q(e,["slidingWindow"]);return null!=l&&X(t,["slidingWindow"],function(e){let t={},n=Q(e,["targetTokens"]);return null!=n&&X(t,["targetTokens"],n),t}(l)),t}(C));let T=Q(e,["proactivity"]);return void 0!==t&&null!=T&&X(t,["setup","proactivity"],function(e){let t={},n=Q(e,["proactiveAudio"]);return null!=n&&X(t,["proactiveAudio"],n),t}(T)),{}}(i,n)),n}(this.apiClient,_),delete E.config,y.send(JSON.stringify(E)),new tY(y,this.apiClient)}isCallableTool(e){return"callTool"in e&&"function"==typeof e.callTool}}let tB={turnComplete:!0};class tY{constructor(e,t){this.conn=e,this.apiClient=t}tLiveClientContent(e,t){if(null!==t.turns&&void 0!==t.turns){let n=[];try{n=ek(t.turns),n=e.isVertexAI()?n.map(e=>tA(e)):n.map(e=>tT(e))}catch(e){throw Error(`Failed to parse client content "turns", type: '${typeof t.turns}'`)}return{clientContent:{turns:n,turnComplete:t.turnComplete}}}return{clientContent:{turnComplete:t.turnComplete}}}tLiveClienttToolResponse(e,t){let n=[];if(null==t.functionResponses||0===(n=Array.isArray(t.functionResponses)?t.functionResponses:[t.functionResponses]).length)throw Error("functionResponses is required.");for(let t of n){if("object"!=typeof t||null===t||!("name"in t)||!("response"in t))throw Error(`Could not parse function response, type '${typeof t}'.`);if(!e.isVertexAI()&&!("id"in t))throw Error("FunctionResponse request must have an `id` field from the response of a ToolCall.FunctionalCalls in Google AI.")}return{toolResponse:{functionResponses:n}}}sendClientContent(e){e=Object.assign(Object.assign({},tB),e);let t=this.tLiveClientContent(this.apiClient,e);this.conn.send(JSON.stringify(t))}sendRealtimeInput(e){let t={};t=this.apiClient.isVertexAI()?{realtimeInput:function(e){let t={},n=Q(e,["media"]);null!=n&&X(t,["mediaChunks"],eI(n));let l=Q(e,["audio"]);null!=l&&X(t,["audio"],eR(l));let i=Q(e,["audioStreamEnd"]);null!=i&&X(t,["audioStreamEnd"],i);let r=Q(e,["video"]);null!=r&&X(t,["video"],eb(r));let o=Q(e,["text"]);return null!=o&&X(t,["text"],o),null!=Q(e,["activityStart"])&&X(t,["activityStart"],{}),null!=Q(e,["activityEnd"])&&X(t,["activityEnd"],{}),t}(e)}:{realtimeInput:function(e){let t={},n=Q(e,["media"]);null!=n&&X(t,["mediaChunks"],eI(n));let l=Q(e,["audio"]);null!=l&&X(t,["audio"],eR(l));let i=Q(e,["audioStreamEnd"]);null!=i&&X(t,["audioStreamEnd"],i);let r=Q(e,["video"]);null!=r&&X(t,["video"],eb(r));let o=Q(e,["text"]);return null!=o&&X(t,["text"],o),null!=Q(e,["activityStart"])&&X(t,["activityStart"],{}),null!=Q(e,["activityEnd"])&&X(t,["activityEnd"],{}),t}(e)},this.conn.send(JSON.stringify(t))}sendToolResponse(e){if(null==e.functionResponses)throw Error("Tool response parameters are required.");let t=this.tLiveClienttToolResponse(this.apiClient,e);this.conn.send(JSON.stringify(t))}close(){this.conn.close()}}function tW(e){var t,n,l;if(null==(t=null==e?void 0:e.automaticFunctionCalling)?void 0:t.disable)return!0;let i=!1;for(let t of null!=(n=null==e?void 0:e.tools)?n:[])if(tK(t)){i=!0;break}if(!i)return!0;let r=null==(l=null==e?void 0:e.automaticFunctionCalling)?void 0:l.maximumRemoteCalls;return(!(!r||!(r<0)&&Number.isInteger(r))||0==r)&&(console.warn("Invalid maximumRemoteCalls value provided for automatic function calling. Disabled automatic function calling. Please provide a valid integer value greater than 0. maximumRemoteCalls provided:",r),!0)}function tK(e){return"callTool"in e&&"function"==typeof e.callTool}function t$(e){var t;return!(null==(t=null==e?void 0:e.automaticFunctionCalling)?void 0:t.ignoreCallHistory)}class tz extends ${constructor(e){super(),this.apiClient=e,this.generateContent=async e=>{var t,n,l,i,r;let o,a,s=await this.processParamsMaybeAddMcpUsage(e);if(this.maybeMoveToResponseJsonSchem(e),!function(e){var t,n,l;return null!=(l=null==(n=null==(t=e.config)?void 0:t.tools)?void 0:n.some(e=>tK(e)))&&l}(e)||tW(e.config))return await this.generateContentInternal(s);if(function(e){var t,n,l;return null!=(l=null==(n=null==(t=e.config)?void 0:t.tools)?void 0:n.some(e=>!tK(e)))&&l}(e))throw Error("Automatic function calling with CallableTools and Tools is not yet supported.");let u=ek(s.contents),p=null!=(l=null==(n=null==(t=s.config)?void 0:t.automaticFunctionCalling)?void 0:n.maximumRemoteCalls)?l:10,d=0;for(;d<p&&(o=await this.generateContentInternal(s)).functionCalls&&0!==o.functionCalls.length;){let t=o.candidates[0].content,n=[];for(let t of null!=(r=null==(i=e.config)?void 0:i.tools)?r:[])if(tK(t)){let e=await t.callTool(o.functionCalls);n.push(...e)}d++,a={role:"user",parts:n},s.contents=ek(s.contents),s.contents.push(t),s.contents.push(a),t$(s.config)&&(u.push(t),u.push(a))}return t$(s.config)&&(o.automaticFunctionCallingHistory=u),o},this.generateContentStream=async e=>{if(this.maybeMoveToResponseJsonSchem(e),!tW(e.config))return await this.processAfcStream(e);{let t=await this.processParamsMaybeAddMcpUsage(e);return await this.generateContentStreamInternal(t)}},this.generateImages=async e=>await this.generateImagesInternal(e).then(e=>{var t;let n,l,i=[];if(null==e?void 0:e.generatedImages)for(let l of e.generatedImages)l&&(null==l?void 0:l.safetyAttributes)&&(null==(t=null==l?void 0:l.safetyAttributes)?void 0:t.contentType)==="Positive Prompt"?n=null==l?void 0:l.safetyAttributes:i.push(l);return n?{generatedImages:i,positivePromptSafetyAttributes:n,sdkHttpResponse:e.sdkHttpResponse}:{generatedImages:i,sdkHttpResponse:e.sdkHttpResponse}}),this.list=async e=>{var t;let n={config:Object.assign(Object.assign({},{queryBase:!0}),null==e?void 0:e.config)};if(this.apiClient.isVertexAI()&&!n.config.queryBase)if(null==(t=n.config)?void 0:t.filter)throw Error("Filtering tuned models list for Vertex AI is not currently supported");else n.config.filter="labels.tune-type:*";return new e4(Y.PAGED_ITEM_MODELS,e=>this.listInternal(e),await this.listInternal(n),n)},this.editImage=async e=>{let t={model:e.model,prompt:e.prompt,referenceImages:[],config:e.config};return e.referenceImages&&e.referenceImages&&(t.referenceImages=e.referenceImages.map(e=>e.toReferenceImageAPI())),await this.editImageInternal(t)},this.upscaleImage=async e=>{let t={numberOfImages:1,mode:"upscale"};e.config&&(t=Object.assign(Object.assign({},t),e.config));let n={model:e.model,image:e.image,upscaleFactor:e.upscaleFactor,config:t};return await this.upscaleImageInternal(n)},this.generateVideos=async e=>await this.generateVideosInternal(e)}maybeMoveToResponseJsonSchem(e){e.config&&e.config.responseSchema&&!e.config.responseJsonSchema&&Object.keys(e.config.responseSchema).includes("$schema")&&(e.config.responseJsonSchema=e.config.responseSchema,delete e.config.responseSchema)}async processParamsMaybeAddMcpUsage(e){var t,n,l;let i=null==(t=e.config)?void 0:t.tools;if(!i)return e;let r=await Promise.all(i.map(async e=>tK(e)?await e.tool():e)),o={model:e.model,contents:e.contents,config:Object.assign(Object.assign({},e.config),{tools:r})};if(o.config.tools=r,e.config&&e.config.tools&&tL(e.config.tools)){let t=Object.assign({},null!=(l=null==(n=e.config.httpOptions)?void 0:n.headers)?l:{});0===Object.keys(t).length&&(t=this.apiClient.getDefaultHeaders()),tq(t),o.config.httpOptions=Object.assign(Object.assign({},e.config.httpOptions),{headers:t})}return o}async initAfcToolsMap(e){var t,n,l;let i=new Map;for(let r of null!=(n=null==(t=e.config)?void 0:t.tools)?n:[])if(tK(r))for(let e of null!=(l=(await r.tool()).functionDeclarations)?l:[]){if(!e.name)throw Error("Function declaration name is required.");if(i.has(e.name))throw Error(`Duplicate tool declaration name: ${e.name}`);i.set(e.name,r)}return i}async processAfcStream(e){var t,n,l;let i=null!=(l=null==(n=null==(t=e.config)?void 0:t.automaticFunctionCalling)?void 0:n.maximumRemoteCalls)?l:10,r=!1,o=0;return function(e,t,n){var l,a;return te(this,arguments,function*(){for(var s,u,p,d;o<i;){r&&(o++,r=!1);let h=yield e7(e.processParamsMaybeAddMcpUsage(n)),g=yield e7(e.generateContentStreamInternal(h)),y=[],C=[];try{for(var c,f=!0,m=(u=void 0,tt(g));!(s=(c=yield e7(m.next())).done);f=!0)if(d=c.value,f=!1,yield yield e7(d),d.candidates&&(null==(l=d.candidates[0])?void 0:l.content)){for(let e of(C.push(d.candidates[0].content),null!=(a=d.candidates[0].content.parts)?a:[]))if(o<i&&e.functionCall){if(!e.functionCall.name)throw Error("Function call name was not returned by the model.");if(t.has(e.functionCall.name)){let n=yield e7(t.get(e.functionCall.name).callTool([e.functionCall]));y.push(...n)}else throw Error(`Automatic function calling was requested, but not all the tools the model used implement the CallableTool interface. Available tools: ${t.keys()}, mising tool: ${e.functionCall.name}`)}}}catch(e){u={error:e}}finally{try{!f&&!s&&(p=m.return)&&(yield e7(p.call(m)))}finally{if(u)throw u.error}}if(y.length>0){r=!0;let e=new en;e.candidates=[{content:{role:"user",parts:y}}],yield yield e7(e);let t=[];t.push(...C),t.push({role:"user",parts:y});let l=ek(n.contents).concat(t);n.contents=l}else break}})}(this,await this.initAfcToolsMap(e),e)}async generateContentInternal(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=tO(this.apiClient,e);return o=z("{model}:generateContent",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=tN(e),n=new en;return Object.assign(n,t),n})}{let t=tv(this.apiClient,e);return o=z("{model}:generateContent",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=tS(e),n=new en;return Object.assign(n,t),n})}}async generateContentStreamInternal(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=tO(this.apiClient,e);return o=z("{model}:streamGenerateContent?alt=sse",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.requestStream({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(function(e){return te(this,arguments,function*(){var t,n,l,i;try{for(var r,o=!0,a=tt(e);!(t=(r=yield e7(a.next())).done);o=!0){i=r.value,o=!1;let e=tN((yield e7(i.json())));e.sdkHttpResponse={headers:i.headers};let t=new en;Object.assign(t,e),yield yield e7(t)}}catch(e){n={error:e}}finally{try{!o&&!t&&(l=a.return)&&(yield e7(l.call(a)))}finally{if(n)throw n.error}}})})}{let t=tv(this.apiClient,e);return o=z("{model}:streamGenerateContent?alt=sse",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.requestStream({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(function(e){return te(this,arguments,function*(){var t,n,l,i;try{for(var r,o=!0,a=tt(e);!(t=(r=yield e7(a.next())).done);o=!0){i=r.value,o=!1;let e=tS((yield e7(i.json())));e.sdkHttpResponse={headers:i.headers};let t=new en;Object.assign(t,e),yield yield e7(t)}}catch(e){n={error:e}}finally{try{!o&&!t&&(l=a.return)&&(yield e7(l.call(a)))}finally{if(n)throw n.error}}})})}}async embedContent(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","model"],e_(e,l));let i=Q(t,["contents"]);null!=i&&X(n,["instances[]","content"],eU(e,i));let r=Q(t,["config"]);return null!=r&&X(n,["config"],function(e,t){let n=Q(e,["taskType"]);void 0!==t&&null!=n&&X(t,["instances[]","task_type"],n);let l=Q(e,["title"]);void 0!==t&&null!=l&&X(t,["instances[]","title"],l);let i=Q(e,["outputDimensionality"]);void 0!==t&&null!=i&&X(t,["parameters","outputDimensionality"],i);let r=Q(e,["mimeType"]);void 0!==t&&null!=r&&X(t,["instances[]","mimeType"],r);let o=Q(e,["autoTruncate"]);return void 0!==t&&null!=o&&X(t,["parameters","autoTruncate"],o),{}}(r,n)),n}(this.apiClient,e);return o=z("{model}:predict",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["predictions[]","embeddings"]);if(null!=l){let e=l;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["values"]);null!=n&&X(t,["values"],n);let l=Q(e,["statistics"]);return null!=l&&X(t,["statistics"],function(e){let t={},n=Q(e,["truncated"]);null!=n&&X(t,["truncated"],n);let l=Q(e,["token_count"]);return null!=l&&X(t,["tokenCount"],l),t}(l)),t})(e))),X(t,["embeddings"],e)}let i=Q(e,["metadata"]);return null!=i&&X(t,["metadata"],function(e){let t={},n=Q(e,["billableCharacterCount"]);return null!=n&&X(t,["billableCharacterCount"],n),t}(i)),t}(e),n=new el;return Object.assign(n,t),n})}{let t=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","model"],e_(e,l));let i=Q(t,["contents"]);null!=i&&X(n,["requests[]","content"],eU(e,i));let r=Q(t,["config"]);null!=r&&X(n,["config"],function(e,t){let n=Q(e,["taskType"]);void 0!==t&&null!=n&&X(t,["requests[]","taskType"],n);let l=Q(e,["title"]);void 0!==t&&null!=l&&X(t,["requests[]","title"],l);let i=Q(e,["outputDimensionality"]);if(void 0!==t&&null!=i&&X(t,["requests[]","outputDimensionality"],i),void 0!==Q(e,["mimeType"]))throw Error("mimeType parameter is not supported in Gemini API.");if(void 0!==Q(e,["autoTruncate"]))throw Error("autoTruncate parameter is not supported in Gemini API.");return{}}(r,n));let o=Q(t,["model"]);return void 0!==o&&X(n,["requests[]","model"],e_(e,o)),n}(this.apiClient,e);return o=z("{model}:batchEmbedContents",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["embeddings"]);if(null!=l){let e=l;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["values"]);return null!=n&&X(t,["values"],n),t})(e))),X(t,["embeddings"],e)}return null!=Q(e,["metadata"])&&X(t,["metadata"],{}),t}(e),n=new el;return Object.assign(n,t),n})}}async generateImagesInternal(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","model"],e_(e,l));let i=Q(t,["prompt"]);null!=i&&X(n,["instances[0]","prompt"],i);let r=Q(t,["config"]);return null!=r&&X(n,["config"],function(e,t){let n=Q(e,["outputGcsUri"]);void 0!==t&&null!=n&&X(t,["parameters","storageUri"],n);let l=Q(e,["negativePrompt"]);void 0!==t&&null!=l&&X(t,["parameters","negativePrompt"],l);let i=Q(e,["numberOfImages"]);void 0!==t&&null!=i&&X(t,["parameters","sampleCount"],i);let r=Q(e,["aspectRatio"]);void 0!==t&&null!=r&&X(t,["parameters","aspectRatio"],r);let o=Q(e,["guidanceScale"]);void 0!==t&&null!=o&&X(t,["parameters","guidanceScale"],o);let a=Q(e,["seed"]);void 0!==t&&null!=a&&X(t,["parameters","seed"],a);let s=Q(e,["safetyFilterLevel"]);void 0!==t&&null!=s&&X(t,["parameters","safetySetting"],s);let u=Q(e,["personGeneration"]);void 0!==t&&null!=u&&X(t,["parameters","personGeneration"],u);let p=Q(e,["includeSafetyAttributes"]);void 0!==t&&null!=p&&X(t,["parameters","includeSafetyAttributes"],p);let d=Q(e,["includeRaiReason"]);void 0!==t&&null!=d&&X(t,["parameters","includeRaiReason"],d);let c=Q(e,["language"]);void 0!==t&&null!=c&&X(t,["parameters","language"],c);let f=Q(e,["outputMimeType"]);void 0!==t&&null!=f&&X(t,["parameters","outputOptions","mimeType"],f);let m=Q(e,["outputCompressionQuality"]);void 0!==t&&null!=m&&X(t,["parameters","outputOptions","compressionQuality"],m);let h=Q(e,["addWatermark"]);void 0!==t&&null!=h&&X(t,["parameters","addWatermark"],h);let g=Q(e,["imageSize"]);void 0!==t&&null!=g&&X(t,["parameters","sampleImageSize"],g);let y=Q(e,["enhancePrompt"]);return void 0!==t&&null!=y&&X(t,["parameters","enhancePrompt"],y),{}}(r,n)),n}(this.apiClient,e);return o=z("{model}:predict",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["predictions"]);if(null!=l){let e=l;Array.isArray(e)&&(e=e.map(e=>tM(e))),X(t,["generatedImages"],e)}let i=Q(e,["positivePromptSafetyAttributes"]);return null!=i&&X(t,["positivePromptSafetyAttributes"],tP(i)),t}(e),n=new ei;return Object.assign(n,t),n})}{let t=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","model"],e_(e,l));let i=Q(t,["prompt"]);null!=i&&X(n,["instances[0]","prompt"],i);let r=Q(t,["config"]);return null!=r&&X(n,["config"],function(e,t){if(void 0!==Q(e,["outputGcsUri"]))throw Error("outputGcsUri parameter is not supported in Gemini API.");if(void 0!==Q(e,["negativePrompt"]))throw Error("negativePrompt parameter is not supported in Gemini API.");let n=Q(e,["numberOfImages"]);void 0!==t&&null!=n&&X(t,["parameters","sampleCount"],n);let l=Q(e,["aspectRatio"]);void 0!==t&&null!=l&&X(t,["parameters","aspectRatio"],l);let i=Q(e,["guidanceScale"]);if(void 0!==t&&null!=i&&X(t,["parameters","guidanceScale"],i),void 0!==Q(e,["seed"]))throw Error("seed parameter is not supported in Gemini API.");let r=Q(e,["safetyFilterLevel"]);void 0!==t&&null!=r&&X(t,["parameters","safetySetting"],r);let o=Q(e,["personGeneration"]);void 0!==t&&null!=o&&X(t,["parameters","personGeneration"],o);let a=Q(e,["includeSafetyAttributes"]);void 0!==t&&null!=a&&X(t,["parameters","includeSafetyAttributes"],a);let s=Q(e,["includeRaiReason"]);void 0!==t&&null!=s&&X(t,["parameters","includeRaiReason"],s);let u=Q(e,["language"]);void 0!==t&&null!=u&&X(t,["parameters","language"],u);let p=Q(e,["outputMimeType"]);void 0!==t&&null!=p&&X(t,["parameters","outputOptions","mimeType"],p);let d=Q(e,["outputCompressionQuality"]);if(void 0!==t&&null!=d&&X(t,["parameters","outputOptions","compressionQuality"],d),void 0!==Q(e,["addWatermark"]))throw Error("addWatermark parameter is not supported in Gemini API.");let c=Q(e,["imageSize"]);if(void 0!==t&&null!=c&&X(t,["parameters","sampleImageSize"],c),void 0!==Q(e,["enhancePrompt"]))throw Error("enhancePrompt parameter is not supported in Gemini API.");return{}}(r,n)),n}(this.apiClient,e);return o=z("{model}:predict",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["predictions"]);if(null!=l){let e=l;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["_self"]);null!=n&&X(t,["image"],function(e){let t={},n=Q(e,["bytesBase64Encoded"]);null!=n&&X(t,["imageBytes"],Z(n));let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(n));let l=Q(e,["raiFilteredReason"]);null!=l&&X(t,["raiFilteredReason"],l);let i=Q(e,["_self"]);return null!=i&&X(t,["safetyAttributes"],tb(i)),t})(e))),X(t,["generatedImages"],e)}let i=Q(e,["positivePromptSafetyAttributes"]);return null!=i&&X(t,["positivePromptSafetyAttributes"],tb(i)),t}(e),n=new ei;return Object.assign(n,t),n})}}async editImageInternal(e){var t,n;let l="",i={};if(this.apiClient.isVertexAI()){let r=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","model"],e_(e,l));let i=Q(t,["prompt"]);null!=i&&X(n,["instances[0]","prompt"],i);let r=Q(t,["referenceImages"]);if(null!=r){let e=r;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["referenceImage"]);null!=n&&X(t,["referenceImage"],tI(n));let l=Q(e,["referenceId"]);null!=l&&X(t,["referenceId"],l);let i=Q(e,["referenceType"]);null!=i&&X(t,["referenceType"],i);let r=Q(e,["maskImageConfig"]);null!=r&&X(t,["maskImageConfig"],function(e){let t={},n=Q(e,["maskMode"]);null!=n&&X(t,["maskMode"],n);let l=Q(e,["segmentationClasses"]);null!=l&&X(t,["maskClasses"],l);let i=Q(e,["maskDilation"]);return null!=i&&X(t,["dilation"],i),t}(r));let o=Q(e,["controlImageConfig"]);null!=o&&X(t,["controlImageConfig"],function(e){let t={},n=Q(e,["controlType"]);null!=n&&X(t,["controlType"],n);let l=Q(e,["enableControlImageComputation"]);return null!=l&&X(t,["computeControl"],l),t}(o));let a=Q(e,["styleImageConfig"]);null!=a&&X(t,["styleImageConfig"],function(e){let t={},n=Q(e,["styleDescription"]);return null!=n&&X(t,["styleDescription"],n),t}(a));let s=Q(e,["subjectImageConfig"]);return null!=s&&X(t,["subjectImageConfig"],function(e){let t={},n=Q(e,["subjectType"]);null!=n&&X(t,["subjectType"],n);let l=Q(e,["subjectDescription"]);return null!=l&&X(t,["subjectDescription"],l),t}(s)),t})(e))),X(n,["instances[0]","referenceImages"],e)}let o=Q(t,["config"]);return null!=o&&X(n,["config"],function(e,t){let n=Q(e,["outputGcsUri"]);void 0!==t&&null!=n&&X(t,["parameters","storageUri"],n);let l=Q(e,["negativePrompt"]);void 0!==t&&null!=l&&X(t,["parameters","negativePrompt"],l);let i=Q(e,["numberOfImages"]);void 0!==t&&null!=i&&X(t,["parameters","sampleCount"],i);let r=Q(e,["aspectRatio"]);void 0!==t&&null!=r&&X(t,["parameters","aspectRatio"],r);let o=Q(e,["guidanceScale"]);void 0!==t&&null!=o&&X(t,["parameters","guidanceScale"],o);let a=Q(e,["seed"]);void 0!==t&&null!=a&&X(t,["parameters","seed"],a);let s=Q(e,["safetyFilterLevel"]);void 0!==t&&null!=s&&X(t,["parameters","safetySetting"],s);let u=Q(e,["personGeneration"]);void 0!==t&&null!=u&&X(t,["parameters","personGeneration"],u);let p=Q(e,["includeSafetyAttributes"]);void 0!==t&&null!=p&&X(t,["parameters","includeSafetyAttributes"],p);let d=Q(e,["includeRaiReason"]);void 0!==t&&null!=d&&X(t,["parameters","includeRaiReason"],d);let c=Q(e,["language"]);void 0!==t&&null!=c&&X(t,["parameters","language"],c);let f=Q(e,["outputMimeType"]);void 0!==t&&null!=f&&X(t,["parameters","outputOptions","mimeType"],f);let m=Q(e,["outputCompressionQuality"]);void 0!==t&&null!=m&&X(t,["parameters","outputOptions","compressionQuality"],m);let h=Q(e,["addWatermark"]);void 0!==t&&null!=h&&X(t,["parameters","addWatermark"],h);let g=Q(e,["editMode"]);void 0!==t&&null!=g&&X(t,["parameters","editMode"],g);let y=Q(e,["baseSteps"]);return void 0!==t&&null!=y&&X(t,["parameters","editConfig","baseSteps"],y),{}}(o,n)),n}(this.apiClient,e);return l=z("{model}:predict",r._url),i=r._query,delete r.config,delete r._url,delete r._query,this.apiClient.request({path:l,queryParams:i,body:JSON.stringify(r),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["predictions"]);if(null!=l){let e=l;Array.isArray(e)&&(e=e.map(e=>tM(e))),X(t,["generatedImages"],e)}return t}(e),n=new er;return Object.assign(n,t),n})}throw Error("This method is only supported by the Vertex AI.")}async upscaleImageInternal(e){var t,n;let l="",i={};if(this.apiClient.isVertexAI()){let r=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","model"],e_(e,l));let i=Q(t,["image"]);null!=i&&X(n,["instances[0]","image"],tI(i));let r=Q(t,["upscaleFactor"]);null!=r&&X(n,["parameters","upscaleConfig","upscaleFactor"],r);let o=Q(t,["config"]);return null!=o&&X(n,["config"],function(e,t){let n=Q(e,["includeRaiReason"]);void 0!==t&&null!=n&&X(t,["parameters","includeRaiReason"],n);let l=Q(e,["outputMimeType"]);void 0!==t&&null!=l&&X(t,["parameters","outputOptions","mimeType"],l);let i=Q(e,["outputCompressionQuality"]);void 0!==t&&null!=i&&X(t,["parameters","outputOptions","compressionQuality"],i);let r=Q(e,["enhanceInputImage"]);void 0!==t&&null!=r&&X(t,["parameters","upscaleConfig","enhanceInputImage"],r);let o=Q(e,["imagePreservationFactor"]);void 0!==t&&null!=o&&X(t,["parameters","upscaleConfig","imagePreservationFactor"],o);let a=Q(e,["numberOfImages"]);void 0!==t&&null!=a&&X(t,["parameters","sampleCount"],a);let s=Q(e,["mode"]);return void 0!==t&&null!=s&&X(t,["parameters","mode"],s),{}}(o,n)),n}(this.apiClient,e);return l=z("{model}:predict",r._url),i=r._query,delete r.config,delete r._url,delete r._query,this.apiClient.request({path:l,queryParams:i,body:JSON.stringify(r),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["predictions"]);if(null!=l){let e=l;Array.isArray(e)&&(e=e.map(e=>tM(e))),X(t,["generatedImages"],e)}return t}(e),n=new eo;return Object.assign(n,t),n})}throw Error("This method is only supported by the Vertex AI.")}async recontextImage(e){var t,n;let l="",i={};if(this.apiClient.isVertexAI()){let r=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","model"],e_(e,l));let i=Q(t,["source"]);null!=i&&X(n,["config"],function(e,t){let n=Q(e,["prompt"]);void 0!==t&&null!=n&&X(t,["instances[0]","prompt"],n);let l=Q(e,["personImage"]);void 0!==t&&null!=l&&X(t,["instances[0]","personImage","image"],tI(l));let i=Q(e,["productImages"]);if(void 0!==t&&null!=i){let e=i;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["productImage"]);return null!=n&&X(t,["image"],tI(n)),t})(e))),X(t,["instances[0]","productImages"],e)}return{}}(i,n));let r=Q(t,["config"]);return null!=r&&X(n,["config"],function(e,t){let n=Q(e,["numberOfImages"]);void 0!==t&&null!=n&&X(t,["parameters","sampleCount"],n);let l=Q(e,["baseSteps"]);void 0!==t&&null!=l&&X(t,["parameters","editConfig","baseSteps"],l);let i=Q(e,["outputGcsUri"]);void 0!==t&&null!=i&&X(t,["parameters","storageUri"],i);let r=Q(e,["seed"]);void 0!==t&&null!=r&&X(t,["parameters","seed"],r);let o=Q(e,["safetyFilterLevel"]);void 0!==t&&null!=o&&X(t,["parameters","safetySetting"],o);let a=Q(e,["personGeneration"]);void 0!==t&&null!=a&&X(t,["parameters","personGeneration"],a);let s=Q(e,["outputMimeType"]);void 0!==t&&null!=s&&X(t,["parameters","outputOptions","mimeType"],s);let u=Q(e,["outputCompressionQuality"]);void 0!==t&&null!=u&&X(t,["parameters","outputOptions","compressionQuality"],u);let p=Q(e,["enhancePrompt"]);return void 0!==t&&null!=p&&X(t,["parameters","enhancePrompt"],p),{}}(r,n)),n}(this.apiClient,e);return l=z("{model}:predict",r._url),i=r._query,delete r.config,delete r._url,delete r._query,this.apiClient.request({path:l,queryParams:i,body:JSON.stringify(r),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json()).then(e=>{let t=function(e){let t={},n=Q(e,["predictions"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>tM(e))),X(t,["generatedImages"],e)}return t}(e),n=new ea;return Object.assign(n,t),n})}throw Error("This method is only supported by the Vertex AI.")}async get(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","name"],e_(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],i),n}(this.apiClient,e);return o=z("{name}",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"GET",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json()).then(e=>tD(e))}{let t=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","name"],e_(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],i),n}(this.apiClient,e);return o=z("{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json()).then(e=>tR(e))}}async listInternal(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e,t){let n={},l=Q(t,["config"]);return null!=l&&X(n,["config"],function(e,t,n){let l=Q(t,["pageSize"]);void 0!==n&&null!=l&&X(n,["_query","pageSize"],l);let i=Q(t,["pageToken"]);void 0!==n&&null!=i&&X(n,["_query","pageToken"],i);let r=Q(t,["filter"]);void 0!==n&&null!=r&&X(n,["_query","filter"],r);let o=Q(t,["queryBase"]);return void 0!==n&&null!=o&&X(n,["_url","models_url"],eB(e,o)),{}}(e,l,n)),n}(this.apiClient,e);return o=z("{models_url}",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"GET",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["nextPageToken"]);null!=l&&X(t,["nextPageToken"],l);let i=Q(e,["_self"]);if(null!=i){let e=eY(i);Array.isArray(e)&&(e=e.map(e=>tD(e))),X(t,["models"],e)}return t}(e),n=new es;return Object.assign(n,t),n})}{let t=function(e,t){let n={},l=Q(t,["config"]);return null!=l&&X(n,["config"],function(e,t,n){let l=Q(t,["pageSize"]);void 0!==n&&null!=l&&X(n,["_query","pageSize"],l);let i=Q(t,["pageToken"]);void 0!==n&&null!=i&&X(n,["_query","pageToken"],i);let r=Q(t,["filter"]);void 0!==n&&null!=r&&X(n,["_query","filter"],r);let o=Q(t,["queryBase"]);return void 0!==n&&null!=o&&X(n,["_url","models_url"],eB(e,o)),{}}(e,l,n)),n}(this.apiClient,e);return o=z("{models_url}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["nextPageToken"]);null!=l&&X(t,["nextPageToken"],l);let i=Q(e,["_self"]);if(null!=i){let e=eY(i);Array.isArray(e)&&(e=e.map(e=>tR(e))),X(t,["models"],e)}return t}(e),n=new es;return Object.assign(n,t),n})}}async update(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","model"],e_(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],function(e,t){let n=Q(e,["displayName"]);void 0!==t&&null!=n&&X(t,["displayName"],n);let l=Q(e,["description"]);void 0!==t&&null!=l&&X(t,["description"],l);let i=Q(e,["defaultCheckpointId"]);return void 0!==t&&null!=i&&X(t,["defaultCheckpointId"],i),{}}(i,n)),n}(this.apiClient,e);return o=z("{model}",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"PATCH",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json()).then(e=>tD(e))}{let t=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","name"],e_(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],function(e,t){let n=Q(e,["displayName"]);void 0!==t&&null!=n&&X(t,["displayName"],n);let l=Q(e,["description"]);void 0!==t&&null!=l&&X(t,["description"],l);let i=Q(e,["defaultCheckpointId"]);return void 0!==t&&null!=i&&X(t,["defaultCheckpointId"],i),{}}(i,n)),n}(this.apiClient,e);return o=z("{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"PATCH",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json()).then(e=>tR(e))}}async delete(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","name"],e_(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],i),n}(this.apiClient,e);return o=z("{name}",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"DELETE",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json()).then(()=>{let e=new eu;return Object.assign(e,{}),e})}{let t=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","name"],e_(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],i),n}(this.apiClient,e);return o=z("{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"DELETE",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json()).then(()=>{let e=new eu;return Object.assign(e,{}),e})}}async countTokens(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","model"],e_(e,l));let i=Q(t,["contents"]);if(null!=i){let e=ek(i);Array.isArray(e)&&(e=e.map(e=>tA(e))),X(n,["contents"],e)}let r=Q(t,["config"]);return null!=r&&X(n,["config"],function(e,t){let n=Q(e,["systemInstruction"]);void 0!==t&&null!=n&&X(t,["systemInstruction"],tA(ex(n)));let l=Q(e,["tools"]);if(void 0!==t&&null!=l){let e=l;Array.isArray(e)&&(e=e.map(e=>t_(e))),X(t,["tools"],e)}let i=Q(e,["generationConfig"]);return void 0!==t&&null!=i&&X(t,["generationConfig"],i),{}}(r,n)),n}(this.apiClient,e);return o=z("{model}:countTokens",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["totalTokens"]);return null!=l&&X(t,["totalTokens"],l),t}(e),n=new ep;return Object.assign(n,t),n})}{let t=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","model"],e_(e,l));let i=Q(t,["contents"]);if(null!=i){let e=ek(i);Array.isArray(e)&&(e=e.map(e=>tT(e))),X(n,["contents"],e)}let r=Q(t,["config"]);return null!=r&&X(n,["config"],function(e){if(void 0!==Q(e,["systemInstruction"]))throw Error("systemInstruction parameter is not supported in Gemini API.");if(void 0!==Q(e,["tools"]))throw Error("tools parameter is not supported in Gemini API.");if(void 0!==Q(e,["generationConfig"]))throw Error("generationConfig parameter is not supported in Gemini API.");return{}}(r)),n}(this.apiClient,e);return o=z("{model}:countTokens",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["totalTokens"]);null!=l&&X(t,["totalTokens"],l);let i=Q(e,["cachedContentTokenCount"]);return null!=i&&X(t,["cachedContentTokenCount"],i),t}(e),n=new ep;return Object.assign(n,t),n})}}async computeTokens(e){var t,n;let l="",i={};if(this.apiClient.isVertexAI()){let r=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","model"],e_(e,l));let i=Q(t,["contents"]);if(null!=i){let e=ek(i);Array.isArray(e)&&(e=e.map(e=>tA(e))),X(n,["contents"],e)}let r=Q(t,["config"]);return null!=r&&X(n,["config"],r),n}(this.apiClient,e);return l=z("{model}:computeTokens",r._url),i=r._query,delete r.config,delete r._url,delete r._query,this.apiClient.request({path:l,queryParams:i,body:JSON.stringify(r),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["tokensInfo"]);return null!=l&&X(t,["tokensInfo"],l),t}(e),n=new ed;return Object.assign(n,t),n})}throw Error("This method is only supported by the Vertex AI.")}async generateVideosInternal(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","model"],e_(e,l));let i=Q(t,["prompt"]);null!=i&&X(n,["instances[0]","prompt"],i);let r=Q(t,["image"]);null!=r&&X(n,["instances[0]","image"],tI(r));let o=Q(t,["video"]);null!=o&&X(n,["instances[0]","video"],function(e){let t={},n=Q(e,["uri"]);null!=n&&X(t,["gcsUri"],n);let l=Q(e,["videoBytes"]);null!=l&&X(t,["bytesBase64Encoded"],Z(l));let i=Q(e,["mimeType"]);return null!=i&&X(t,["mimeType"],i),t}(o));let a=Q(t,["config"]);return null!=a&&X(n,["config"],function(e,t){let n=Q(e,["numberOfVideos"]);void 0!==t&&null!=n&&X(t,["parameters","sampleCount"],n);let l=Q(e,["outputGcsUri"]);void 0!==t&&null!=l&&X(t,["parameters","storageUri"],l);let i=Q(e,["fps"]);void 0!==t&&null!=i&&X(t,["parameters","fps"],i);let r=Q(e,["durationSeconds"]);void 0!==t&&null!=r&&X(t,["parameters","durationSeconds"],r);let o=Q(e,["seed"]);void 0!==t&&null!=o&&X(t,["parameters","seed"],o);let a=Q(e,["aspectRatio"]);void 0!==t&&null!=a&&X(t,["parameters","aspectRatio"],a);let s=Q(e,["resolution"]);void 0!==t&&null!=s&&X(t,["parameters","resolution"],s);let u=Q(e,["personGeneration"]);void 0!==t&&null!=u&&X(t,["parameters","personGeneration"],u);let p=Q(e,["pubsubTopic"]);void 0!==t&&null!=p&&X(t,["parameters","pubsubTopic"],p);let d=Q(e,["negativePrompt"]);void 0!==t&&null!=d&&X(t,["parameters","negativePrompt"],d);let c=Q(e,["enhancePrompt"]);void 0!==t&&null!=c&&X(t,["parameters","enhancePrompt"],c);let f=Q(e,["generateAudio"]);void 0!==t&&null!=f&&X(t,["parameters","generateAudio"],f);let m=Q(e,["lastFrame"]);void 0!==t&&null!=m&&X(t,["instances[0]","lastFrame"],tI(m));let h=Q(e,["compressionQuality"]);return void 0!==t&&null!=h&&X(t,["parameters","compressionQuality"],h),{}}(a,n)),n}(this.apiClient,e);return o=z("{model}:predictLongRunning",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json()).then(e=>{let t=function(e){let t={},n=Q(e,["name"]);null!=n&&X(t,["name"],n);let l=Q(e,["metadata"]);null!=l&&X(t,["metadata"],l);let i=Q(e,["done"]);null!=i&&X(t,["done"],i);let r=Q(e,["error"]);null!=r&&X(t,["error"],r);let o=Q(e,["response"]);return null!=o&&X(t,["response"],function(e){let t={},n=Q(e,["videos"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["_self"]);return null!=n&&X(t,["video"],function(e){let t={},n=Q(e,["gcsUri"]);null!=n&&X(t,["uri"],n);let l=Q(e,["bytesBase64Encoded"]);null!=l&&X(t,["videoBytes"],Z(l));let i=Q(e,["mimeType"]);return null!=i&&X(t,["mimeType"],i),t}(n)),t})(e))),X(t,["generatedVideos"],e)}let l=Q(e,["raiMediaFilteredCount"]);null!=l&&X(t,["raiMediaFilteredCount"],l);let i=Q(e,["raiMediaFilteredReasons"]);return null!=i&&X(t,["raiMediaFilteredReasons"],i),t}(o)),t}(e),n=new ev;return Object.assign(n,t),n})}{let t=function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["_url","model"],e_(e,l));let i=Q(t,["prompt"]);null!=i&&X(n,["instances[0]","prompt"],i);let r=Q(t,["image"]);if(null!=r&&X(n,["instances[0]","image"],function(e){let t={};if(void 0!==Q(e,["gcsUri"]))throw Error("gcsUri parameter is not supported in Gemini API.");let n=Q(e,["imageBytes"]);null!=n&&X(t,["bytesBase64Encoded"],Z(n));let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(r)),void 0!==Q(t,["video"]))throw Error("video parameter is not supported in Gemini API.");let o=Q(t,["config"]);return null!=o&&X(n,["config"],function(e,t){let n=Q(e,["numberOfVideos"]);if(void 0!==t&&null!=n&&X(t,["parameters","sampleCount"],n),void 0!==Q(e,["outputGcsUri"]))throw Error("outputGcsUri parameter is not supported in Gemini API.");if(void 0!==Q(e,["fps"]))throw Error("fps parameter is not supported in Gemini API.");let l=Q(e,["durationSeconds"]);if(void 0!==t&&null!=l&&X(t,["parameters","durationSeconds"],l),void 0!==Q(e,["seed"]))throw Error("seed parameter is not supported in Gemini API.");let i=Q(e,["aspectRatio"]);if(void 0!==t&&null!=i&&X(t,["parameters","aspectRatio"],i),void 0!==Q(e,["resolution"]))throw Error("resolution parameter is not supported in Gemini API.");let r=Q(e,["personGeneration"]);if(void 0!==t&&null!=r&&X(t,["parameters","personGeneration"],r),void 0!==Q(e,["pubsubTopic"]))throw Error("pubsubTopic parameter is not supported in Gemini API.");let o=Q(e,["negativePrompt"]);void 0!==t&&null!=o&&X(t,["parameters","negativePrompt"],o);let a=Q(e,["enhancePrompt"]);if(void 0!==t&&null!=a&&X(t,["parameters","enhancePrompt"],a),void 0!==Q(e,["generateAudio"]))throw Error("generateAudio parameter is not supported in Gemini API.");if(void 0!==Q(e,["lastFrame"]))throw Error("lastFrame parameter is not supported in Gemini API.");if(void 0!==Q(e,["compressionQuality"]))throw Error("compressionQuality parameter is not supported in Gemini API.");return{}}(o,n)),n}(this.apiClient,e);return o=z("{model}:predictLongRunning",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json()).then(e=>{let t=function(e){let t={},n=Q(e,["name"]);null!=n&&X(t,["name"],n);let l=Q(e,["metadata"]);null!=l&&X(t,["metadata"],l);let i=Q(e,["done"]);null!=i&&X(t,["done"],i);let r=Q(e,["error"]);null!=r&&X(t,["error"],r);let o=Q(e,["response","generateVideoResponse"]);return null!=o&&X(t,["response"],function(e){let t={},n=Q(e,["generatedSamples"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["_self"]);return null!=n&&X(t,["video"],function(e){let t={},n=Q(e,["video","uri"]);null!=n&&X(t,["uri"],n);let l=Q(e,["video","encodedVideo"]);null!=l&&X(t,["videoBytes"],Z(l));let i=Q(e,["encoding"]);return null!=i&&X(t,["mimeType"],i),t}(n)),t})(e))),X(t,["generatedVideos"],e)}let l=Q(e,["raiMediaFilteredCount"]);null!=l&&X(t,["raiMediaFilteredCount"],l);let i=Q(e,["raiMediaFilteredReasons"]);return null!=i&&X(t,["raiMediaFilteredReasons"],i),t}(o)),t}(e),n=new ev;return Object.assign(n,t),n})}}}class tX extends ${constructor(e){super(),this.apiClient=e}async getVideosOperation(e){let t=e.operation,n=e.config;if(void 0===t.name||""===t.name)throw Error("Operation name is required.");if(this.apiClient.isVertexAI()){let e,l=t.name.split("/operations/")[0];n&&"httpOptions"in n&&(e=n.httpOptions);let i=await this.fetchPredictVideosOperationInternal({operationName:t.name,resourceName:l,config:{httpOptions:e}});return t._fromAPIResponse({apiResponse:i,isVertexAI:!0})}{let e=await this.getVideosOperationInternal({operationName:t.name,config:n});return t._fromAPIResponse({apiResponse:e,isVertexAI:!1})}}async get(e){let t=e.operation,n=e.config;if(void 0===t.name||""===t.name)throw Error("Operation name is required.");if(this.apiClient.isVertexAI()){let e,l=t.name.split("/operations/")[0];n&&"httpOptions"in n&&(e=n.httpOptions);let i=await this.fetchPredictVideosOperationInternal({operationName:t.name,resourceName:l,config:{httpOptions:e}});return t._fromAPIResponse({apiResponse:i,isVertexAI:!0})}{let e=await this.getVideosOperationInternal({operationName:t.name,config:n});return t._fromAPIResponse({apiResponse:e,isVertexAI:!1})}}async getVideosOperationInternal(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e){let t={},n=Q(e,["operationName"]);null!=n&&X(t,["_url","operationName"],n);let l=Q(e,["config"]);return null!=l&&X(t,["config"],l),t}(e);return o=z("{operationName}",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"GET",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json())}{let t=function(e){let t={},n=Q(e,["operationName"]);null!=n&&X(t,["_url","operationName"],n);let l=Q(e,["config"]);return null!=l&&X(t,["config"],l),t}(e);return o=z("{operationName}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json())}}async fetchPredictVideosOperationInternal(e){var t,n;let l="",i={};if(this.apiClient.isVertexAI()){let r=function(e){let t={},n=Q(e,["operationName"]);null!=n&&X(t,["operationName"],n);let l=Q(e,["resourceName"]);null!=l&&X(t,["_url","resourceName"],l);let i=Q(e,["config"]);return null!=i&&X(t,["config"],i),t}(e);return l=z("{resourceName}:fetchPredictOperation",r._url),i=r._query,delete r.config,delete r._url,delete r._query,this.apiClient.request({path:l,queryParams:i,body:JSON.stringify(r),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json())}throw Error("This method is only supported by the Vertex AI.")}}function tQ(e){let t={},n=Q(e,["prebuiltVoiceConfig"]);return null!=n&&X(t,["prebuiltVoiceConfig"],function(e){let t={},n=Q(e,["voiceName"]);return null!=n&&X(t,["voiceName"],n),t}(n)),t}class tZ extends ${constructor(e){super(),this.apiClient=e}async create(e){var t,n;let l="",i={};if(this.apiClient.isVertexAI())throw Error("The client.tokens.create method is only supported by the Gemini Developer API.");{let r=function(e,t){let n={},l=Q(t,["config"]);return null!=l&&X(n,["config"],function(e,t,n){let l=Q(t,["expireTime"]);void 0!==n&&null!=l&&X(n,["expireTime"],l);let i=Q(t,["newSessionExpireTime"]);void 0!==n&&null!=i&&X(n,["newSessionExpireTime"],i);let r=Q(t,["uses"]);void 0!==n&&null!=r&&X(n,["uses"],r);let o=Q(t,["liveConnectConstraints"]);void 0!==n&&null!=o&&X(n,["bidiGenerateContentSetup"],function(e,t){let n={},l=Q(t,["model"]);null!=l&&X(n,["setup","model"],e_(e,l));let i=Q(t,["config"]);return null!=i&&X(n,["config"],function(e,t){let n=Q(e,["generationConfig"]);void 0!==t&&null!=n&&X(t,["setup","generationConfig"],n);let l=Q(e,["responseModalities"]);void 0!==t&&null!=l&&X(t,["setup","generationConfig","responseModalities"],l);let i=Q(e,["temperature"]);void 0!==t&&null!=i&&X(t,["setup","generationConfig","temperature"],i);let r=Q(e,["topP"]);void 0!==t&&null!=r&&X(t,["setup","generationConfig","topP"],r);let o=Q(e,["topK"]);void 0!==t&&null!=o&&X(t,["setup","generationConfig","topK"],o);let a=Q(e,["maxOutputTokens"]);void 0!==t&&null!=a&&X(t,["setup","generationConfig","maxOutputTokens"],a);let s=Q(e,["mediaResolution"]);void 0!==t&&null!=s&&X(t,["setup","generationConfig","mediaResolution"],s);let u=Q(e,["seed"]);void 0!==t&&null!=u&&X(t,["setup","generationConfig","seed"],u);let p=Q(e,["speechConfig"]);void 0!==t&&null!=p&&X(t,["setup","generationConfig","speechConfig"],function(e){let t={},n=Q(e,["voiceConfig"]);null!=n&&X(t,["voiceConfig"],tQ(n));let l=Q(e,["multiSpeakerVoiceConfig"]);null!=l&&X(t,["multiSpeakerVoiceConfig"],function(e){let t={},n=Q(e,["speakerVoiceConfigs"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["speaker"]);null!=n&&X(t,["speaker"],n);let l=Q(e,["voiceConfig"]);return null!=l&&X(t,["voiceConfig"],tQ(l)),t})(e))),X(t,["speakerVoiceConfigs"],e)}return t}(l));let i=Q(e,["languageCode"]);return null!=i&&X(t,["languageCode"],i),t}(eG(p)));let d=Q(e,["enableAffectiveDialog"]);void 0!==t&&null!=d&&X(t,["setup","generationConfig","enableAffectiveDialog"],d);let c=Q(e,["systemInstruction"]);void 0!==t&&null!=c&&X(t,["setup","systemInstruction"],function(e){let t={},n=Q(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["videoMetadata"]);null!=n&&X(t,["videoMetadata"],function(e){let t={},n=Q(e,["fps"]);null!=n&&X(t,["fps"],n);let l=Q(e,["endOffset"]);null!=l&&X(t,["endOffset"],l);let i=Q(e,["startOffset"]);return null!=i&&X(t,["startOffset"],i),t}(n));let l=Q(e,["thought"]);null!=l&&X(t,["thought"],l);let i=Q(e,["inlineData"]);null!=i&&X(t,["inlineData"],function(e){let t={};if(void 0!==Q(e,["displayName"]))throw Error("displayName parameter is not supported in Gemini API.");let n=Q(e,["data"]);null!=n&&X(t,["data"],n);let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(i));let r=Q(e,["fileData"]);null!=r&&X(t,["fileData"],function(e){let t={};if(void 0!==Q(e,["displayName"]))throw Error("displayName parameter is not supported in Gemini API.");let n=Q(e,["fileUri"]);null!=n&&X(t,["fileUri"],n);let l=Q(e,["mimeType"]);return null!=l&&X(t,["mimeType"],l),t}(r));let o=Q(e,["thoughtSignature"]);null!=o&&X(t,["thoughtSignature"],o);let a=Q(e,["codeExecutionResult"]);null!=a&&X(t,["codeExecutionResult"],a);let s=Q(e,["executableCode"]);null!=s&&X(t,["executableCode"],s);let u=Q(e,["functionCall"]);null!=u&&X(t,["functionCall"],u);let p=Q(e,["functionResponse"]);null!=p&&X(t,["functionResponse"],p);let d=Q(e,["text"]);return null!=d&&X(t,["text"],d),t})(e))),X(t,["parts"],e)}let l=Q(e,["role"]);return null!=l&&X(t,["role"],l),t}(ex(c)));let f=Q(e,["tools"]);if(void 0!==t&&null!=f){let e=eF(f);Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["functionDeclarations"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["behavior"]);null!=n&&X(t,["behavior"],n);let l=Q(e,["description"]);null!=l&&X(t,["description"],l);let i=Q(e,["name"]);null!=i&&X(t,["name"],i);let r=Q(e,["parameters"]);null!=r&&X(t,["parameters"],r);let o=Q(e,["parametersJsonSchema"]);null!=o&&X(t,["parametersJsonSchema"],o);let a=Q(e,["response"]);null!=a&&X(t,["response"],a);let s=Q(e,["responseJsonSchema"]);return null!=s&&X(t,["responseJsonSchema"],s),t})(e))),X(t,["functionDeclarations"],e)}if(void 0!==Q(e,["retrieval"]))throw Error("retrieval parameter is not supported in Gemini API.");let l=Q(e,["googleSearch"]);null!=l&&X(t,["googleSearch"],function(e){let t={},n=Q(e,["timeRangeFilter"]);return null!=n&&X(t,["timeRangeFilter"],function(e){let t={},n=Q(e,["startTime"]);null!=n&&X(t,["startTime"],n);let l=Q(e,["endTime"]);return null!=l&&X(t,["endTime"],l),t}(n)),t}(l));let i=Q(e,["googleSearchRetrieval"]);if(null!=i&&X(t,["googleSearchRetrieval"],function(e){let t={},n=Q(e,["dynamicRetrievalConfig"]);return null!=n&&X(t,["dynamicRetrievalConfig"],function(e){let t={},n=Q(e,["mode"]);null!=n&&X(t,["mode"],n);let l=Q(e,["dynamicThreshold"]);return null!=l&&X(t,["dynamicThreshold"],l),t}(n)),t}(i)),void 0!==Q(e,["enterpriseWebSearch"]))throw Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(void 0!==Q(e,["googleMaps"]))throw Error("googleMaps parameter is not supported in Gemini API.");null!=Q(e,["urlContext"])&&X(t,["urlContext"],{});let r=Q(e,["codeExecution"]);null!=r&&X(t,["codeExecution"],r);let o=Q(e,["computerUse"]);return null!=o&&X(t,["computerUse"],o),t})(eH(e)))),X(t,["setup","tools"],e)}let m=Q(e,["sessionResumption"]);void 0!==t&&null!=m&&X(t,["setup","sessionResumption"],function(e){let t={},n=Q(e,["handle"]);if(null!=n&&X(t,["handle"],n),void 0!==Q(e,["transparent"]))throw Error("transparent parameter is not supported in Gemini API.");return t}(m));let h=Q(e,["inputAudioTranscription"]);void 0!==t&&null!=h&&X(t,["setup","inputAudioTranscription"],{});let g=Q(e,["outputAudioTranscription"]);void 0!==t&&null!=g&&X(t,["setup","outputAudioTranscription"],{});let y=Q(e,["realtimeInputConfig"]);void 0!==t&&null!=y&&X(t,["setup","realtimeInputConfig"],function(e){let t={},n=Q(e,["automaticActivityDetection"]);null!=n&&X(t,["automaticActivityDetection"],function(e){let t={},n=Q(e,["disabled"]);null!=n&&X(t,["disabled"],n);let l=Q(e,["startOfSpeechSensitivity"]);null!=l&&X(t,["startOfSpeechSensitivity"],l);let i=Q(e,["endOfSpeechSensitivity"]);null!=i&&X(t,["endOfSpeechSensitivity"],i);let r=Q(e,["prefixPaddingMs"]);null!=r&&X(t,["prefixPaddingMs"],r);let o=Q(e,["silenceDurationMs"]);return null!=o&&X(t,["silenceDurationMs"],o),t}(n));let l=Q(e,["activityHandling"]);null!=l&&X(t,["activityHandling"],l);let i=Q(e,["turnCoverage"]);return null!=i&&X(t,["turnCoverage"],i),t}(y));let C=Q(e,["contextWindowCompression"]);void 0!==t&&null!=C&&X(t,["setup","contextWindowCompression"],function(e){let t={},n=Q(e,["triggerTokens"]);null!=n&&X(t,["triggerTokens"],n);let l=Q(e,["slidingWindow"]);return null!=l&&X(t,["slidingWindow"],function(e){let t={},n=Q(e,["targetTokens"]);return null!=n&&X(t,["targetTokens"],n),t}(l)),t}(C));let T=Q(e,["proactivity"]);return void 0!==t&&null!=T&&X(t,["setup","proactivity"],function(e){let t={},n=Q(e,["proactiveAudio"]);return null!=n&&X(t,["proactiveAudio"],n),t}(T)),{}}(i,n)),n}(e,o));let a=Q(t,["lockAdditionalFields"]);return void 0!==n&&null!=a&&X(n,["fieldMask"],a),{}}(e,l,n)),n}(this.apiClient,e);l=z("auth_tokens",r._url),i=r._query,delete r.config,delete r._url,delete r._query;let o=function(e,t){let n=null,l=e.bidiGenerateContentSetup;if("object"==typeof l&&null!==l&&"setup"in l){let t=l.setup;"object"==typeof t&&null!==t?(e.bidiGenerateContentSetup=t,n=t):delete e.bidiGenerateContentSetup}else void 0!==l&&delete e.bidiGenerateContentSetup;let i=e.fieldMask;if(n){let l=function(e){let t=[];for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)){let l=e[n];if("object"==typeof l&&null!=l&&Object.keys(l).length>0){let e=Object.keys(l).map(e=>`${n}.${e}`);t.push(...e)}else t.push(n)}return t.join(",")}(n);if(Array.isArray(null==t?void 0:t.lockAdditionalFields)&&(null==t?void 0:t.lockAdditionalFields.length)===0)l?e.fieldMask=l:delete e.fieldMask;else if((null==t?void 0:t.lockAdditionalFields)&&t.lockAdditionalFields.length>0&&null!==i&&Array.isArray(i)&&i.length>0){let t=["temperature","topK","topP","maxOutputTokens","responseModalities","seed","speechConfig"],n=[];i.length>0&&(n=i.map(e=>t.includes(e)?`generationConfig.${e}`:e));let r=[];l&&r.push(l),n.length>0&&r.push(...n),r.length>0?e.fieldMask=r.join(","):delete e.fieldMask}else delete e.fieldMask}else null!==i&&Array.isArray(i)&&i.length>0?e.fieldMask=i.join(","):delete e.fieldMask;return e}(r,e.config);return this.apiClient.request({path:l,queryParams:i,body:JSON.stringify(o),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json()).then(e=>(function(e){let t={},n=Q(e,["name"]);return null!=n&&X(t,["name"],n),t})(e))}}}function t0(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["name"]);null!=l&&X(t,["name"],l);let i=Q(e,["state"]);null!=i&&X(t,["state"],ej(i));let r=Q(e,["createTime"]);null!=r&&X(t,["createTime"],r);let o=Q(e,["tuningTask","startTime"]);null!=o&&X(t,["startTime"],o);let a=Q(e,["tuningTask","completeTime"]);null!=a&&X(t,["endTime"],a);let s=Q(e,["updateTime"]);null!=s&&X(t,["updateTime"],s);let u=Q(e,["description"]);null!=u&&X(t,["description"],u);let p=Q(e,["baseModel"]);null!=p&&X(t,["baseModel"],p);let d=Q(e,["_self"]);null!=d&&X(t,["tunedModel"],function(e){let t={},n=Q(e,["name"]);null!=n&&X(t,["model"],n);let l=Q(e,["name"]);return null!=l&&X(t,["endpoint"],l),t}(d));let c=Q(e,["distillationSpec"]);null!=c&&X(t,["distillationSpec"],c);let f=Q(e,["experiment"]);null!=f&&X(t,["experiment"],f);let m=Q(e,["labels"]);null!=m&&X(t,["labels"],m);let h=Q(e,["pipelineJob"]);null!=h&&X(t,["pipelineJob"],h);let g=Q(e,["satisfiesPzi"]);null!=g&&X(t,["satisfiesPzi"],g);let y=Q(e,["satisfiesPzs"]);null!=y&&X(t,["satisfiesPzs"],y);let C=Q(e,["serviceAccount"]);null!=C&&X(t,["serviceAccount"],C);let T=Q(e,["tunedModelDisplayName"]);return null!=T&&X(t,["tunedModelDisplayName"],T),t}function t1(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["name"]);null!=l&&X(t,["name"],l);let i=Q(e,["state"]);null!=i&&X(t,["state"],ej(i));let r=Q(e,["createTime"]);null!=r&&X(t,["createTime"],r);let o=Q(e,["startTime"]);null!=o&&X(t,["startTime"],o);let a=Q(e,["endTime"]);null!=a&&X(t,["endTime"],a);let s=Q(e,["updateTime"]);null!=s&&X(t,["updateTime"],s);let u=Q(e,["error"]);null!=u&&X(t,["error"],u);let p=Q(e,["description"]);null!=p&&X(t,["description"],p);let d=Q(e,["baseModel"]);null!=d&&X(t,["baseModel"],d);let c=Q(e,["tunedModel"]);null!=c&&X(t,["tunedModel"],function(e){let t={},n=Q(e,["model"]);null!=n&&X(t,["model"],n);let l=Q(e,["endpoint"]);null!=l&&X(t,["endpoint"],l);let i=Q(e,["checkpoints"]);if(null!=i){let e=i;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["checkpointId"]);null!=n&&X(t,["checkpointId"],n);let l=Q(e,["epoch"]);null!=l&&X(t,["epoch"],l);let i=Q(e,["step"]);null!=i&&X(t,["step"],i);let r=Q(e,["endpoint"]);return null!=r&&X(t,["endpoint"],r),t})(e))),X(t,["checkpoints"],e)}return t}(c));let f=Q(e,["supervisedTuningSpec"]);null!=f&&X(t,["supervisedTuningSpec"],f);let m=Q(e,["tuningDataStats"]);null!=m&&X(t,["tuningDataStats"],m);let h=Q(e,["encryptionSpec"]);null!=h&&X(t,["encryptionSpec"],h);let g=Q(e,["partnerModelTuningSpec"]);null!=g&&X(t,["partnerModelTuningSpec"],g);let y=Q(e,["distillationSpec"]);null!=y&&X(t,["distillationSpec"],y);let C=Q(e,["experiment"]);null!=C&&X(t,["experiment"],C);let T=Q(e,["labels"]);null!=T&&X(t,["labels"],T);let E=Q(e,["pipelineJob"]);null!=E&&X(t,["pipelineJob"],E);let v=Q(e,["satisfiesPzi"]);null!=v&&X(t,["satisfiesPzi"],v);let A=Q(e,["satisfiesPzs"]);null!=A&&X(t,["satisfiesPzs"],A);let _=Q(e,["serviceAccount"]);null!=_&&X(t,["serviceAccount"],_);let O=Q(e,["tunedModelDisplayName"]);return null!=O&&X(t,["tunedModelDisplayName"],O),t}class t4 extends ${constructor(e){super(),this.apiClient=e,this.get=async e=>await this.getInternal(e),this.list=async(e={})=>new e4(Y.PAGED_ITEM_TUNING_JOBS,e=>this.listInternal(e),await this.listInternal(e),e),this.tune=async e=>{if(this.apiClient.isVertexAI())return await this.tuneInternal(e);{let t=await this.tuneMldevInternal(e),n="";return void 0!==t.metadata&&void 0!==t.metadata.tunedModel?n=t.metadata.tunedModel:void 0!==t.name&&t.name.includes("/operations/")&&(n=t.name.split("/operations/")[0]),{name:n,state:v.JOB_STATE_QUEUED}}}}async getInternal(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e){let t={},n=Q(e,["name"]);null!=n&&X(t,["_url","name"],n);let l=Q(e,["config"]);return null!=l&&X(t,["config"],l),t}(e);return o=z("{name}",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"GET",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>t1(e))}{let t=function(e){let t={},n=Q(e,["name"]);null!=n&&X(t,["_url","name"],n);let l=Q(e,["config"]);return null!=l&&X(t,["config"],l),t}(e);return o=z("{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>t0(e))}}async listInternal(e){var t,n,l,i;let r,o="",a={};if(this.apiClient.isVertexAI()){let l=function(e){let t={},n=Q(e,["config"]);return null!=n&&X(t,["config"],function(e,t){let n=Q(e,["pageSize"]);void 0!==t&&null!=n&&X(t,["_query","pageSize"],n);let l=Q(e,["pageToken"]);void 0!==t&&null!=l&&X(t,["_query","pageToken"],l);let i=Q(e,["filter"]);return void 0!==t&&null!=i&&X(t,["_query","filter"],i),{}}(n,t)),t}(e);return o=z("tuningJobs",l._url),a=l._query,delete l.config,delete l._url,delete l._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(l),httpMethod:"GET",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["nextPageToken"]);null!=l&&X(t,["nextPageToken"],l);let i=Q(e,["tuningJobs"]);if(null!=i){let e=i;Array.isArray(e)&&(e=e.map(e=>t1(e))),X(t,["tuningJobs"],e)}return t}(e),n=new ef;return Object.assign(n,t),n})}{let t=function(e){let t={},n=Q(e,["config"]);return null!=n&&X(t,["config"],function(e,t){let n=Q(e,["pageSize"]);void 0!==t&&null!=n&&X(t,["_query","pageSize"],n);let l=Q(e,["pageToken"]);void 0!==t&&null!=l&&X(t,["_query","pageToken"],l);let i=Q(e,["filter"]);return void 0!==t&&null!=i&&X(t,["_query","filter"],i),{}}(n,t)),t}(e);return o=z("tunedModels",t._url),a=t._query,delete t.config,delete t._url,delete t._query,this.apiClient.request({path:o,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null==(l=e.config)?void 0:l.httpOptions,abortSignal:null==(i=e.config)?void 0:i.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>{let t=function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["nextPageToken"]);null!=l&&X(t,["nextPageToken"],l);let i=Q(e,["tunedModels"]);if(null!=i){let e=i;Array.isArray(e)&&(e=e.map(e=>t0(e))),X(t,["tuningJobs"],e)}return t}(e),n=new ef;return Object.assign(n,t),n})}}async tuneInternal(e){var t,n;let l="",i={};if(this.apiClient.isVertexAI()){let r=function(e){let t={},n=Q(e,["baseModel"]);null!=n&&X(t,["baseModel"],n);let l=Q(e,["trainingDataset"]);null!=l&&X(t,["supervisedTuningSpec","trainingDatasetUri"],function(e,t){let n=Q(e,["gcsUri"]);void 0!==t&&null!=n&&X(t,["supervisedTuningSpec","trainingDatasetUri"],n);let l=Q(e,["vertexDatasetResource"]);if(void 0!==t&&null!=l&&X(t,["supervisedTuningSpec","trainingDatasetUri"],l),void 0!==Q(e,["examples"]))throw Error("examples parameter is not supported in Vertex AI.");return{}}(l,t));let i=Q(e,["config"]);return null!=i&&X(t,["config"],function(e,t){let n={},l=Q(e,["validationDataset"]);void 0!==t&&null!=l&&X(t,["supervisedTuningSpec"],function(e,t){let n={},l=Q(e,["gcsUri"]);null!=l&&X(n,["validationDatasetUri"],l);let i=Q(e,["vertexDatasetResource"]);return void 0!==t&&null!=i&&X(t,["supervisedTuningSpec","trainingDatasetUri"],i),n}(l,n));let i=Q(e,["tunedModelDisplayName"]);void 0!==t&&null!=i&&X(t,["tunedModelDisplayName"],i);let r=Q(e,["description"]);void 0!==t&&null!=r&&X(t,["description"],r);let o=Q(e,["epochCount"]);void 0!==t&&null!=o&&X(t,["supervisedTuningSpec","hyperParameters","epochCount"],o);let a=Q(e,["learningRateMultiplier"]);void 0!==t&&null!=a&&X(t,["supervisedTuningSpec","hyperParameters","learningRateMultiplier"],a);let s=Q(e,["exportLastCheckpointOnly"]);void 0!==t&&null!=s&&X(t,["supervisedTuningSpec","exportLastCheckpointOnly"],s);let u=Q(e,["adapterSize"]);if(void 0!==t&&null!=u&&X(t,["supervisedTuningSpec","hyperParameters","adapterSize"],u),void 0!==Q(e,["batchSize"]))throw Error("batchSize parameter is not supported in Vertex AI.");if(void 0!==Q(e,["learningRate"]))throw Error("learningRate parameter is not supported in Vertex AI.");return n}(i,t)),t}(e);return l=z("tuningJobs",r._url),i=r._query,delete r.config,delete r._url,delete r._query,this.apiClient.request({path:l,queryParams:i,body:JSON.stringify(r),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>t1(e))}throw Error("This method is only supported by the Vertex AI.")}async tuneMldevInternal(e){var t,n;let l="",i={};if(this.apiClient.isVertexAI())throw Error("This method is only supported by the Gemini Developer API.");{let r=function(e){let t={},n=Q(e,["baseModel"]);null!=n&&X(t,["baseModel"],n);let l=Q(e,["trainingDataset"]);null!=l&&X(t,["tuningTask","trainingData"],function(e){let t={};if(void 0!==Q(e,["gcsUri"]))throw Error("gcsUri parameter is not supported in Gemini API.");if(void 0!==Q(e,["vertexDatasetResource"]))throw Error("vertexDatasetResource parameter is not supported in Gemini API.");let n=Q(e,["examples"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map(e=>(function(e){let t={},n=Q(e,["textInput"]);null!=n&&X(t,["textInput"],n);let l=Q(e,["output"]);return null!=l&&X(t,["output"],l),t})(e))),X(t,["examples","examples"],e)}return t}(l));let i=Q(e,["config"]);return null!=i&&X(t,["config"],function(e,t){let n={};if(void 0!==Q(e,["validationDataset"]))throw Error("validationDataset parameter is not supported in Gemini API.");let l=Q(e,["tunedModelDisplayName"]);if(void 0!==t&&null!=l&&X(t,["displayName"],l),void 0!==Q(e,["description"]))throw Error("description parameter is not supported in Gemini API.");let i=Q(e,["epochCount"]);void 0!==t&&null!=i&&X(t,["tuningTask","hyperparameters","epochCount"],i);let r=Q(e,["learningRateMultiplier"]);if(null!=r&&X(n,["tuningTask","hyperparameters","learningRateMultiplier"],r),void 0!==Q(e,["exportLastCheckpointOnly"]))throw Error("exportLastCheckpointOnly parameter is not supported in Gemini API.");if(void 0!==Q(e,["adapterSize"]))throw Error("adapterSize parameter is not supported in Gemini API.");let o=Q(e,["batchSize"]);void 0!==t&&null!=o&&X(t,["tuningTask","hyperparameters","batchSize"],o);let a=Q(e,["learningRate"]);return void 0!==t&&null!=a&&X(t,["tuningTask","hyperparameters","learningRate"],a),n}(i,t)),t}(e);return l=z("tunedModels",r._url),i=r._query,delete r.config,delete r._url,delete r._query,this.apiClient.request({path:l,queryParams:i,body:JSON.stringify(r),httpMethod:"POST",httpOptions:null==(t=e.config)?void 0:t.httpOptions,abortSignal:null==(n=e.config)?void 0:n.abortSignal}).then(e=>e.json().then(t=>(t.sdkHttpResponse={headers:e.headers},t))).then(e=>(function(e){let t={},n=Q(e,["sdkHttpResponse"]);null!=n&&X(t,["sdkHttpResponse"],n);let l=Q(e,["name"]);null!=l&&X(t,["name"],l);let i=Q(e,["metadata"]);null!=i&&X(t,["metadata"],i);let r=Q(e,["done"]);null!=r&&X(t,["done"],r);let o=Q(e,["error"]);return null!=o&&X(t,["error"],o),t})(e))}}}class t6{async download(e,t){throw Error("Download to file is not supported in the browser, please use a browser compliant download like an <a> tag.")}}let t2="x-goog-upload-status";async function t9(e,t,n){var l,i,r;let o=0,a=0,s=new et(new Response),u="upload";for(o=e.size;a<o;){let r=Math.min(8388608,o-a),p=e.slice(a,a+r);a+r>=o&&(u+=", finalize");let d=0,c=1e3;for(;d<3&&(null==(l=null==(s=await n.request({path:"",body:p,httpMethod:"POST",httpOptions:{apiVersion:"",baseUrl:t,headers:{"X-Goog-Upload-Command":u,"X-Goog-Upload-Offset":String(a),"Content-Length":String(r)}}}))?void 0:s.headers)||!l[t2]);)d++,await function(e){return new Promise(t=>setTimeout(t,e))}(c),c*=2;if(a+=r,(null==(i=null==s?void 0:s.headers)?void 0:i[t2])!=="active")break;if(o<=a)throw Error("All content has been uploaded, but the upload status is not finalized.")}let p=await (null==s?void 0:s.json());if((null==(r=null==s?void 0:s.headers)?void 0:r[t2])!=="final")throw Error("Failed to upload file: Upload status is not finalized.");return p.file}async function t3(e){return{size:e.size,type:e.type}}class t8{async upload(e,t,n){if("string"==typeof e)throw Error("File path is not supported in browser uploader.");return await t9(e,t,n)}async stat(e){if("string"!=typeof e)return await t3(e);throw Error("File path is not supported in browser uploader.")}}class t5{create(e,t,n){return new t7(e,t,n)}}class t7{constructor(e,t,n){this.url=e,this.headers=t,this.callbacks=n}connect(){this.ws=new WebSocket(this.url),this.ws.onopen=this.callbacks.onopen,this.ws.onerror=this.callbacks.onerror,this.ws.onclose=this.callbacks.onclose,this.ws.onmessage=this.callbacks.onmessage}send(e){if(void 0===this.ws)throw Error("WebSocket is not connected");this.ws.send(e)}close(){if(void 0===this.ws)throw Error("WebSocket is not connected");this.ws.close()}}let ne="x-goog-api-key";class nt{constructor(e){this.apiKey=e}async addAuthHeaders(e){if(null===e.get(ne)){if(this.apiKey.startsWith("auth_tokens/"))throw Error("Ephemeral tokens are only supported by the live API.");if(!this.apiKey)throw Error("API key is missing. Please provide a valid API key.");e.append(ne,this.apiKey)}}}class nn{constructor(e){var t;if(null==e.apiKey)throw Error("An API Key must be set when running in a browser");if(e.project||e.location)throw Error("Vertex AI project based authentication is not supported on browser runtimes. Please do not provide a project or location.");this.vertexai=null!=(t=e.vertexai)&&t,this.apiKey=e.apiKey;let n=function(e,t,n,l){var i,r;if(!(null==e?void 0:e.baseUrl)){let e={geminiUrl:W,vertexUrl:K};return t?null!=(i=e.vertexUrl)?i:n:null!=(r=e.geminiUrl)?r:l}return e.baseUrl}(e.httpOptions,e.vertexai,void 0,void 0);n&&(e.httpOptions?e.httpOptions.baseUrl=n:e.httpOptions={baseUrl:n}),this.apiVersion=e.apiVersion;let l=new nt(this.apiKey);this.apiClient=new tU({auth:l,apiVersion:this.apiVersion,apiKey:this.apiKey,vertexai:this.vertexai,httpOptions:e.httpOptions,userAgentExtra:"gl-node/web",uploader:new t8,downloader:new t6}),this.models=new tz(this.apiClient),this.live=new tJ(this.apiClient,l,new t5),this.batches=new e6(this.apiClient),this.chats=new ti(this.models,this.apiClient),this.caches=new e5(this.apiClient),this.files=new ts(this.apiClient),this.operations=new tX(this.apiClient),this.authTokens=new tZ(this.apiClient),this.tunings=new t4(this.apiClient)}}}}]);