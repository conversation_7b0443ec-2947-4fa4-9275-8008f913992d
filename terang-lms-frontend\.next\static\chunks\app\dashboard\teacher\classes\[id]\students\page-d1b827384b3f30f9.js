try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="77fd6e29-0e2e-4d44-8b9c-b4e995aa36af",e._sentryDebugIdIdentifier="sentry-dbid-77fd6e29-0e2e-4d44-8b9c-b4e995aa36af")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8],{287:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>w});var s=a(95155),r=a(12115),n=a(20063),l=a(66094),o=a(20764),i=a(31936),d=a(88864),c=a(38004),u=a(25532),m=a(35299),x=a(35626),f=a(96583),g=a(86651),y=a(71360),p=a(91169),h=a(52619),v=a.n(h),b=a(47886),j=a(18720);function w(){(0,n.useRouter)();let e=(0,n.useParams)().id,[t,a]=(0,r.useState)(""),[h,w]=(0,r.useState)([]),[N,S]=(0,r.useState)([]),[C,T]=(0,r.useState)(null),[D,k]=(0,r.useState)(!0),[I,P]=(0,r.useState)(!1),[A,B]=(0,r.useState)(null),[_,z]=(0,r.useState)(""),[R,E]=(0,r.useState)(!1);(0,r.useEffect)(()=>{e&&(U(),H(),L())},[e]);let U=async()=>{try{let t=b.qs.getUser();if(!t)return;let a=await fetch("/api/classes/".concat(e,"?teacherId=").concat(t.id)),s=await a.json();s.success&&s.class&&T(s.class)}catch(e){console.error("Error fetching class:",e)}},H=async()=>{try{if(!b.qs.getUser())return void j.oR.error("Please log in to view students");let t=await fetch("/api/class-enrollments?classId=".concat(e)),a=await t.json();if(a.success){let e=a.data.map(e=>({id:e.studentId,name:e.studentName,email:e.studentEmail,enrolledAt:e.enrolledAt}));w(e||[])}else j.oR.error(a.error||"Failed to fetch students")}catch(e){console.error("Error fetching students:",e),j.oR.error("Failed to fetch students")}finally{k(!1)}},L=async()=>{try{let a=b.qs.getUser();if(!a)return;let s="/api/users?role=student&institutionId=".concat(a.institutionId,"&excludeClassId=").concat(e);console.log("\uD83D\uDD0D Fetching available students from:",s),console.log("\uD83D\uDC64 Current user:",{id:a.id,institutionId:a.institutionId});let r=await fetch(s),n=await r.json();if(console.log("\uD83D\uDCE5 API Response:",n),console.log("✅ Response success:",n.success),console.log("\uD83D\uDC65 Users in response:",n.users),n.success){var t;let e=(null==(t=n.data)?void 0:t.users)||[];console.log("\uD83C\uDFAF Setting availableStudents to:",e),S(e),console.log("\uD83D\uDCCA Available students count:",e.length)}else console.error("❌ API returned error:",n.error)}catch(e){console.error("\uD83D\uDCA5 Error fetching available students:",e)}},F=async()=>{if(!_)return void j.oR.error("Please select a student to add");P(!0);try{if(!b.qs.getUser())return void j.oR.error("Please log in to add students");let t=await fetch("/api/class-enrollments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({studentId:parseInt(_),classId:parseInt(e),status:"active"})}),a=await t.json();a.success?(j.oR.success("Student added to class successfully!"),E(!1),z(""),H(),L()):j.oR.error(a.error||"Failed to add student to class")}catch(e){console.error("Error adding student:",e),j.oR.error("Failed to add student to class")}finally{P(!1)}},M=async t=>{if(confirm("Are you sure you want to remove this student from the class?")){B(t);try{if(!b.qs.getUser())return void j.oR.error("Please log in to remove students");let a=await fetch("/api/class-enrollments?studentId=".concat(t,"&classId=").concat(e)),s=await a.json();if(s.success&&s.data.length>0){let e=s.data[0].id,t=await fetch("/api/class-enrollments/".concat(e),{method:"DELETE"}),a=await t.json();a.success?(j.oR.success("Student removed from class successfully!"),H(),L()):j.oR.error(a.error||"Failed to remove student from class")}else j.oR.error("Enrollment not found")}catch(e){console.error("Error removing student:",e),j.oR.error("Failed to remove student from class")}finally{B(null)}}},O=h.filter(e=>e.name.toLowerCase().includes(t.toLowerCase())||e.email.toLowerCase().includes(t.toLowerCase()));return D?(0,s.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,s.jsx)(m.A,{className:"h-8 w-8 animate-spin"}),(0,s.jsx)("span",{className:"ml-2",children:"Loading students..."})]}):(0,s.jsxs)("div",{className:"space-y-6","data-sentry-component":"ClassStudentsPage","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(v(),{href:"/dashboard/teacher/classes/".concat(e),"data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(o.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(x.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ArrowLeft","data-sentry-source-file":"page.tsx"}),"Back"]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Manage Students"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:C?"Students in ".concat(C.name):"Loading class..."})]})]}),(0,s.jsxs)(l.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(l.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Class Students"}),(0,s.jsx)(l.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Manage students enrolled in this class"})]}),(0,s.jsxs)(c.lG,{open:R,onOpenChange:E,"data-sentry-element":"Dialog","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(c.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(o.$,{"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(f.A,{className:"mr-2 h-4 w-4","data-sentry-element":"UserPlus","data-sentry-source-file":"page.tsx"}),"Add Student"]})}),(0,s.jsxs)(c.Cf,{"data-sentry-element":"DialogContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(c.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(c.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"page.tsx",children:"Add Student to Class"}),(0,s.jsx)(c.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"page.tsx",children:"Select a student from your institution to add to this class."})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(u.l6,{value:_,onValueChange:z,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(u.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(u.yv,{placeholder:"Select a student","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,s.jsx)(u.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:(console.log("\uD83C\uDFA8 Rendering dropdown with availableStudents:",N),console.log("\uD83D\uDCDD Available students length:",N.length),N.map(e=>(console.log("\uD83D\uDC64 Rendering student:",e),(0,s.jsxs)(u.eb,{value:e.id.toString(),children:[e.name," (",e.email,")"]},e.id))))})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,s.jsx)(o.$,{variant:"outline",onClick:()=>{E(!1),z("")},"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Cancel"}),(0,s.jsxs)(o.$,{onClick:F,disabled:I,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[I?(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(f.A,{className:"mr-2 h-4 w-4"}),I?"Adding...":"Add Student"]})]})]})]})]})]})}),(0,s.jsxs)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"mb-4 flex items-center space-x-2",children:(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(g.A,{className:"text-muted-foreground absolute top-2.5 left-2 h-4 w-4","data-sentry-element":"Search","data-sentry-source-file":"page.tsx"}),(0,s.jsx)(i.p,{placeholder:"Search students...",value:t,onChange:e=>a(e.target.value),className:"pl-8","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]})}),(0,s.jsx)("div",{className:"rounded-md border",children:(0,s.jsxs)(d.Table,{"data-sentry-element":"Table","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(d.TableHeader,{"data-sentry-element":"TableHeader","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(d.TableRow,{"data-sentry-element":"TableRow","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(d.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Student Name"}),(0,s.jsx)(d.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Email"}),(0,s.jsx)(d.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Enrolled Date"}),(0,s.jsx)(d.TableHead,{className:"w-[70px]","data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Actions"})]})}),(0,s.jsx)(d.TableBody,{"data-sentry-element":"TableBody","data-sentry-source-file":"page.tsx",children:O.map(e=>(0,s.jsxs)(d.TableRow,{children:[(0,s.jsx)(d.TableCell,{children:(0,s.jsx)("div",{className:"font-medium",children:e.name})}),(0,s.jsx)(d.TableCell,{children:(0,s.jsx)("div",{className:"text-muted-foreground",children:e.email})}),(0,s.jsx)(d.TableCell,{children:(0,s.jsx)("span",{className:"text-sm",children:new Date(e.enrolledAt).toLocaleDateString()})}),(0,s.jsx)(d.TableCell,{children:(0,s.jsx)(o.$,{variant:"ghost",size:"sm",className:"text-red-600 hover:text-red-700",onClick:()=>M(e.id),disabled:A===e.id,children:A===e.id?(0,s.jsx)(m.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(y.A,{className:"h-4 w-4"})})})]},e.id))})]})}),0===O.length&&(0,s.jsxs)("div",{className:"py-8 text-center",children:[(0,s.jsx)(p.A,{className:"text-muted-foreground mx-auto h-12 w-12"}),(0,s.jsx)("h3",{className:"mt-2 text-sm font-semibold",children:"No students found"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-1 text-sm",children:t?"Try adjusting your search terms.":"No students are enrolled in this class yet."}),!t&&(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsxs)(o.$,{onClick:()=>E(!0),children:[(0,s.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Add First Student"]})})]})]})]})]})}},20764:(e,t,a)=>{"use strict";a.d(t,{$:()=>i,r:()=>o});var s=a(95155);a(12115);var r=a(32467),n=a(83101),l=a(64269);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:a,size:n,asChild:i=!1,...d}=e,c=i?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,l.cn)(o({variant:a,size:n,className:t})),...d,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},25532:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>x,gC:()=>m,l6:()=>d,yv:()=>c});var s=a(95155);a(12115);var r=a(47887),n=a(24033),l=a(5917),o=a(12108),i=a(64269);function d(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t,"data-sentry-element":"SelectPrimitive.Root","data-sentry-component":"Select","data-sentry-source-file":"select.tsx"})}function c(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t,"data-sentry-element":"SelectPrimitive.Value","data-sentry-component":"SelectValue","data-sentry-source-file":"select.tsx"})}function u(e){let{className:t,size:a="default",children:l,...o}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,"data-sentry-element":"SelectPrimitive.Trigger","data-sentry-component":"SelectTrigger","data-sentry-source-file":"select.tsx",children:[l,(0,s.jsx)(r.In,{asChild:!0,"data-sentry-element":"SelectPrimitive.Icon","data-sentry-source-file":"select.tsx",children:(0,s.jsx)(n.A,{className:"size-4 opacity-50","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})]})}function m(e){let{className:t,children:a,position:n="popper",...l}=e;return(0,s.jsx)(r.ZL,{"data-sentry-element":"SelectPrimitive.Portal","data-sentry-component":"SelectContent","data-sentry-source-file":"select.tsx",children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...l,"data-sentry-element":"SelectPrimitive.Content","data-sentry-source-file":"select.tsx",children:[(0,s.jsx)(f,{"data-sentry-element":"SelectScrollUpButton","data-sentry-source-file":"select.tsx"}),(0,s.jsx)(r.LM,{className:(0,i.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),"data-sentry-element":"SelectPrimitive.Viewport","data-sentry-source-file":"select.tsx",children:a}),(0,s.jsx)(g,{"data-sentry-element":"SelectScrollDownButton","data-sentry-source-file":"select.tsx"})]})})}function x(e){let{className:t,children:a,...n}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,"data-sentry-element":"SelectPrimitive.Item","data-sentry-component":"SelectItem","data-sentry-source-file":"select.tsx",children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{"data-sentry-element":"SelectPrimitive.ItemIndicator","data-sentry-source-file":"select.tsx",children:(0,s.jsx)(l.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"select.tsx"})})}),(0,s.jsx)(r.p4,{"data-sentry-element":"SelectPrimitive.ItemText","data-sentry-source-file":"select.tsx",children:a})]})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,"data-sentry-element":"SelectPrimitive.ScrollUpButton","data-sentry-component":"SelectScrollUpButton","data-sentry-source-file":"select.tsx",children:(0,s.jsx)(o.A,{className:"size-4","data-sentry-element":"ChevronUpIcon","data-sentry-source-file":"select.tsx"})})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,"data-sentry-element":"SelectPrimitive.ScrollDownButton","data-sentry-component":"SelectScrollDownButton","data-sentry-source-file":"select.tsx",children:(0,s.jsx)(n.A,{className:"size-4","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})}},31936:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var s=a(95155);a(12115);var r=a(64269);function n(e){let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},38004:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>x,L3:()=>f,c7:()=>m,lG:()=>o,rr:()=>g,zM:()=>i});var s=a(95155);a(12115);var r=a(89511),n=a(65229),l=a(64269);function o(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"dialog",...t,"data-sentry-element":"DialogPrimitive.Root","data-sentry-component":"Dialog","data-sentry-source-file":"dialog.tsx"})}function i(e){let{...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"dialog-trigger",...t,"data-sentry-element":"DialogPrimitive.Trigger","data-sentry-component":"DialogTrigger","data-sentry-source-file":"dialog.tsx"})}function d(e){let{...t}=e;return(0,s.jsx)(r.ZL,{"data-slot":"dialog-portal",...t,"data-sentry-element":"DialogPrimitive.Portal","data-sentry-component":"DialogPortal","data-sentry-source-file":"dialog.tsx"})}function c(e){let{className:t,...a}=e;return(0,s.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a,"data-sentry-element":"DialogPrimitive.Overlay","data-sentry-component":"DialogOverlay","data-sentry-source-file":"dialog.tsx"})}function u(e){let{className:t,children:a,...o}=e;return(0,s.jsxs)(d,{"data-slot":"dialog-portal","data-sentry-element":"DialogPortal","data-sentry-component":"DialogContent","data-sentry-source-file":"dialog.tsx",children:[(0,s.jsx)(c,{"data-sentry-element":"DialogOverlay","data-sentry-source-file":"dialog.tsx"}),(0,s.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...o,"data-sentry-element":"DialogPrimitive.Content","data-sentry-source-file":"dialog.tsx",children:[a,(0,s.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","data-sentry-element":"DialogPrimitive.Close","data-sentry-source-file":"dialog.tsx",children:[(0,s.jsx)(n.A,{"data-sentry-element":"XIcon","data-sentry-source-file":"dialog.tsx"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a,"data-sentry-component":"DialogHeader","data-sentry-source-file":"dialog.tsx"})}function x(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a,"data-sentry-component":"DialogFooter","data-sentry-source-file":"dialog.tsx"})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",t),...a,"data-sentry-element":"DialogPrimitive.Title","data-sentry-component":"DialogTitle","data-sentry-source-file":"dialog.tsx"})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-element":"DialogPrimitive.Description","data-sentry-component":"DialogDescription","data-sentry-source-file":"dialog.tsx"})}},40519:(e,t,a)=>{Promise.resolve().then(a.bind(a,287))},47886:(e,t,a)=>{"use strict";a.d(t,{WG:()=>r,cl:()=>l,qs:()=>s});let s={setUser:e=>{localStorage.setItem("auth_user",JSON.stringify(e))},getUser:()=>{{let e=localStorage.getItem("auth_user");return e?JSON.parse(e):null}},removeUser:()=>{localStorage.removeItem("auth_user")},isAuthenticated:()=>null!==s.getUser(),hasRole:e=>{let t=s.getUser();return(null==t?void 0:t.role)===e},isSuperAdmin:()=>s.hasRole("super_admin"),isTeacher:()=>s.hasRole("teacher"),isStudent:()=>s.hasRole("student")},r=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},n=()=>{let e=s.getUser();return e||(window.location.href="/auth/sign-in",null)},l=e=>{let t=n();return t?t.role!==e?(window.location.href=r(t),null):t:null}},64269:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n,z:()=>l});var s=a(2821),r=a(75889);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}function l(e){var t,a;let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:r=0,sizeType:n="normal"}=s;if(0===e)return"0 Byte";let l=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,l)).toFixed(r)," ").concat("accurate"===n?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][l])?t:"Bytest":null!=(a=["Bytes","KB","MB","GB","TB"][l])?a:"Bytes")}},66094:(e,t,a)=>{"use strict";a.d(t,{BT:()=>i,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>l,wL:()=>c});var s=a(95155);a(12115);var r=a(64269);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},88864:(e,t,a)=>{"use strict";a.d(t,{Table:()=>n,TableBody:()=>o,TableCell:()=>c,TableHead:()=>d,TableHeader:()=>l,TableRow:()=>i});var s=a(95155);a(12115);var r=a(64269);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto","data-sentry-component":"Table","data-sentry-source-file":"table.tsx",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",t),...a})})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",t),...a,"data-sentry-component":"TableHeader","data-sentry-source-file":"table.tsx"})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",t),...a,"data-sentry-component":"TableBody","data-sentry-source-file":"table.tsx"})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a,"data-sentry-component":"TableRow","data-sentry-source-file":"table.tsx"})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a,"data-sentry-component":"TableHead","data-sentry-source-file":"table.tsx"})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a,"data-sentry-component":"TableCell","data-sentry-source-file":"table.tsx"})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,7055,4736,660,8720,6093,9568,8172,4850,8441,3840,7358],()=>t(40519)),_N_E=e.O()}]);