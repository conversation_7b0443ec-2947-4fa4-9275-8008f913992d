try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="848af18f-961a-4f84-8994-c2b81138f9a4",e._sentryDebugIdIdentifier="sentry-dbid-848af18f-961a-4f84-8994-c2b81138f9a4")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[350],{10489:(e,t,a)=>{"use strict";a.d(t,{b:()=>d});var r=a(12115),s=a(97602),n=a(95155),i=r.forwardRef((e,t)=>(0,n.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var d=i},20063:(e,t,a)=>{"use strict";var r=a(47260);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},20764:(e,t,a)=>{"use strict";a.d(t,{$:()=>l,r:()=>d});var r=a(95155);a(12115);var s=a(32467),n=a(83101),i=a(64269);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:n,asChild:l=!1,...o}=e,c=l?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:a,size:n,className:t})),...o,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},21786:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(71847).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},31936:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var r=a(95155);a(12115);var s=a(64269);function n(e){let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},35626:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(71847).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},39893:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var r=a(95155),s=a(12115),n=a(20063),i=a(66094),d=a(20764),l=a(31936),o=a(42526),c=a(47254),u=a(35626),m=a(65229),p=a(21786),x=a(46046),h=a(52619),f=a.n(h),g=a(47886),y=a(18720),v=a(15239);function b(){let e=(0,n.useRouter)(),[t,a]=(0,s.useState)(!1),[h,b]=(0,s.useState)({name:"",description:""}),[j,w]=(0,s.useState)(null),[N,C]=(0,s.useState)(null),[k,B]=(0,s.useState)(!1),R=async e=>{try{B(!0);let t=new FormData;t.append("file",e);let a=await fetch("/api/upload",{method:"POST",body:t});if(!a.ok)throw Error("Failed to upload image");return(await a.json()).url}catch(e){return console.error("Error uploading image:",e),y.oR.error("Failed to upload image"),null}finally{B(!1)}},P=async t=>{t.preventDefault(),a(!0);try{let t=g.qs.getUser();if(!t)return void y.oR.error("Please log in to create classes");if(!t.institutionId)return void y.oR.error("You must be assigned to an institution to create classes");if(!h.name.trim())return void y.oR.error("Class name is required");let a=null;if(j&&!(a=await R(j)))return;let r=await fetch("/api/classes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:h.name.trim(),description:h.description.trim(),teacherId:t.id,institutionId:t.institutionId,coverPicture:a})}),s=await r.json();s.success?(y.oR.success("Class created successfully!"),e.push("/dashboard/teacher/classes")):y.oR.error(s.error||"Failed to create class")}catch(e){console.error("Error creating class:",e),y.oR.error("Failed to create class")}finally{a(!1)}},S=(e,t)=>{b(a=>({...a,[e]:t}))};return(0,r.jsxs)("div",{className:"space-y-6","data-sentry-component":"NewClassPage","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(f(),{href:"/dashboard/teacher/classes","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)(d.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ArrowLeft","data-sentry-source-file":"page.tsx"}),"Back"]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Create New Class"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Create a new class to organize your students"})]})]}),(0,r.jsxs)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Class Details"}),(0,r.jsx)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Enter the basic information for the new class"})]}),(0,r.jsx)(i.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)("form",{onSubmit:P,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"name","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Class Name"}),(0,r.jsx)(l.p,{id:"name",value:h.name,onChange:e=>S("name",e.target.value),placeholder:"e.g., Mathematics Grade 10A",required:!0,"data-sentry-element":"Input","data-sentry-source-file":"page.tsx"}),(0,r.jsx)("p",{className:"text-muted-foreground text-sm",children:"Choose a descriptive name that includes subject and grade level"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"description","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Description"}),(0,r.jsx)(c.T,{id:"description",value:h.description,onChange:e=>S("description",e.target.value),placeholder:"Brief description of the class and its objectives",rows:4,"data-sentry-element":"Textarea","data-sentry-source-file":"page.tsx"}),(0,r.jsx)("p",{className:"text-muted-foreground text-sm",children:"Provide a brief description of what this class covers"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"coverImage","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Cover Image"}),(0,r.jsx)("div",{className:"space-y-4",children:N?(0,r.jsxs)("div",{className:"relative w-full max-w-md",children:[(0,r.jsx)(v.default,{src:N,alt:"Cover preview",width:400,height:200,className:"rounded-lg object-cover w-full h-48"}),(0,r.jsx)(d.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:()=>{w(null),C(null)},children:(0,r.jsx)(m.A,{className:"h-4 w-4"})})]}):(0,r.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[(0,r.jsx)(p.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)(o.J,{htmlFor:"coverImage",className:"cursor-pointer inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:"Choose Image"}),(0,r.jsx)(l.p,{id:"coverImage",type:"file",accept:"image/*",onChange:e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];if(a){if(!a.type.startsWith("image/"))return void y.oR.error("Please select a valid image file");if(a.size>5242880)return void y.oR.error("Image size must be less than 5MB");w(a);let e=new FileReader;e.onload=e=>{var t;C(null==(t=e.target)?void 0:t.result)},e.readAsDataURL(a)}},className:"hidden"})]}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"PNG, JPG, GIF up to 5MB"})]})}),(0,r.jsx)("p",{className:"text-muted-foreground text-sm",children:"Upload a cover image for your class (optional)"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,r.jsx)(f(),{href:"/dashboard/teacher/classes","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(d.$,{variant:"outline",type:"button","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Cancel"})}),(0,r.jsxs)(d.$,{type:"submit",disabled:t||k,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Save","data-sentry-source-file":"page.tsx"}),k?"Uploading Image...":t?"Creating...":"Create Class"]})]})]})})]}),(0,r.jsxs)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Next Steps"}),(0,r.jsx)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"After creating your class, you can:"})]}),(0,r.jsx)(i.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"h-2 w-2 rounded-full bg-blue-500"}),(0,r.jsx)("span",{children:"Add students to your class"})]}),(0,r.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"h-2 w-2 rounded-full bg-blue-500"}),(0,r.jsx)("span",{children:"Create or assign courses to the class"})]}),(0,r.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"h-2 w-2 rounded-full bg-blue-500"}),(0,r.jsx)("span",{children:"Generate course codes for student enrollment"})]}),(0,r.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"h-2 w-2 rounded-full bg-blue-500"}),(0,r.jsx)("span",{children:"Track student progress and performance"})]})]})})]})]})}},42526:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var r=a(95155);a(12115);var s=a(10489),n=a(64269);function i(e){let{className:t,...a}=e;return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a,"data-sentry-element":"LabelPrimitive.Root","data-sentry-component":"Label","data-sentry-source-file":"label.tsx"})}},46046:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(71847).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},47254:(e,t,a)=>{"use strict";a.d(t,{T:()=>n});var r=a(95155);a(12115);var s=a(64269);function n(e){let{className:t,...a}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a,"data-sentry-component":"Textarea","data-sentry-source-file":"textarea.tsx"})}},47886:(e,t,a)=>{"use strict";a.d(t,{WG:()=>s,cl:()=>i,qs:()=>r});let r={setUser:e=>{localStorage.setItem("auth_user",JSON.stringify(e))},getUser:()=>{{let e=localStorage.getItem("auth_user");return e?JSON.parse(e):null}},removeUser:()=>{localStorage.removeItem("auth_user")},isAuthenticated:()=>null!==r.getUser(),hasRole:e=>{let t=r.getUser();return(null==t?void 0:t.role)===e},isSuperAdmin:()=>r.hasRole("super_admin"),isTeacher:()=>r.hasRole("teacher"),isStudent:()=>r.hasRole("student")},s=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},n=()=>{let e=r.getUser();return e||(window.location.href="/auth/sign-in",null)},i=e=>{let t=n();return t?t.role!==e?(window.location.href=s(t),null):t:null}},54332:(e,t,a)=>{Promise.resolve().then(a.bind(a,39893))},64269:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n,z:()=>i});var r=a(2821),s=a(75889);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}function i(e){var t,a;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:s=0,sizeType:n="normal"}=r;if(0===e)return"0 Byte";let i=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,i)).toFixed(s)," ").concat("accurate"===n?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][i])?t:"Bytest":null!=(a=["Bytes","KB","MB","GB","TB"][i])?a:"Bytes")}},65229:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(71847).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},66094:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>i,wL:()=>c});var r=a(95155);a(12115);var s=a(64269);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},97602:(e,t,a)=>{"use strict";a.d(t,{hO:()=>l,sG:()=>d});var r=a(12115),s=a(47650),n=a(32467),i=a(95155),d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let a=(0,n.TL)(`Primitive.${t}`),s=r.forwardRef((e,r)=>{let{asChild:s,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(s?a:t,{...n,ref:r})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function l(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,7055,8720,5239,4850,8441,3840,7358],()=>t(54332)),_N_E=e.O()}]);