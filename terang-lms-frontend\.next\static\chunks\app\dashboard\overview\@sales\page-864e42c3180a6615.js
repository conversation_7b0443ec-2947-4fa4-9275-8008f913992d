try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="bc98c8e1-a012-4e97-8606-806f6c8974e0",e._sentryDebugIdIdentifier="sentry-dbid-bc98c8e1-a012-4e97-8606-806f6c8974e0")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2718],{3468:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,q:()=>i});var n=r(12115),a=r(95155);function i(e,t){let r=n.createContext(t),i=e=>{let{children:t,...i}=e,o=n.useMemo(()=>i,Object.values(i));return(0,a.jsx)(r.Provider,{value:o,children:t})};return i.displayName=e+"Provider",[i,function(a){let i=n.useContext(r);if(i)return i;if(void 0!==t)return t;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function o(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let a=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:a}}),[r,a])}};return i.scopeName=e,[function(t,i){let o=n.createContext(i),l=r.length;r=[...r,i];let u=t=>{let{scope:r,children:i,...u}=t,s=r?.[e]?.[l]||o,c=n.useMemo(()=>u,Object.values(u));return(0,a.jsx)(s.Provider,{value:c,children:i})};return u.displayName=t+"Provider",[u,function(r,a){let u=a?.[e]?.[l]||o,s=n.useContext(u);if(s)return s;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=r.reduce((t,{useScope:r,scopeName:n})=>{let a=r(e)[`__scope${n}`];return{...t,...a}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return r.scopeName=t.scopeName,r}(i,...t)]}},4129:(e,t,r)=>{"use strict";r.d(t,{N:()=>a});var n=r(12115),a=globalThis?.document?n.useLayoutEffect:()=>{}},12547:(e,t,r)=>{"use strict";r.d(t,{Avatar:()=>o,AvatarFallback:()=>u,AvatarImage:()=>l});var n=r(95155);r(12115);var a=r(46591),i=r(64269);function o(e){let{className:t,...r}=e;return(0,n.jsx)(a.bL,{"data-slot":"avatar",className:(0,i.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...r,"data-sentry-element":"AvatarPrimitive.Root","data-sentry-component":"Avatar","data-sentry-source-file":"avatar.tsx"})}function l(e){let{className:t,...r}=e;return(0,n.jsx)(a._V,{"data-slot":"avatar-image",className:(0,i.cn)("aspect-square size-full",t),...r,"data-sentry-element":"AvatarPrimitive.Image","data-sentry-component":"AvatarImage","data-sentry-source-file":"avatar.tsx"})}function u(e){let{className:t,...r}=e;return(0,n.jsx)(a.H4,{"data-slot":"avatar-fallback",className:(0,i.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...r,"data-sentry-element":"AvatarPrimitive.Fallback","data-sentry-component":"AvatarFallback","data-sentry-source-file":"avatar.tsx"})}},14806:(e,t,r)=>{"use strict";e.exports=r(30125)},30125:(e,t,r)=>{"use strict";var n=r(12115),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=n.useState,o=n.useEffect,l=n.useLayoutEffect,u=n.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!a(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=i({inst:{value:r,getSnapshot:t}}),a=n[0].inst,c=n[1];return l(function(){a.value=r,a.getSnapshot=t,s(a)&&c({inst:a})},[e,r,t]),o(function(){return s(a)&&c({inst:a}),e(function(){s(a)&&c({inst:a})})},[e]),u(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},32467:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,Dc:()=>s,TL:()=>o});var n=r(12115),a=r(94446),i=r(95155);function o(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var o;let e,l,u=(o=r,(l=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(l=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),s=function(e,t){let r={...t};for(let n in t){let a=e[n],i=t[n];/^on[A-Z]/.test(n)?a&&i?r[n]=(...e)=>{let t=i(...e);return a(...e),t}:a&&(r[n]=a):"style"===n?r[n]={...a,...i}:"className"===n&&(r[n]=[a,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(s.ref=t?(0,a.t)(t,u):u),n.cloneElement(r,s)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:a,...o}=e,l=n.Children.toArray(a),u=l.find(c);if(u){let e=u.props.children,a=l.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,i.jsx)(t,{...o,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var l=o("Slot"),u=Symbol("radix.slottable");function s(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=u,t}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}},46591:(e,t,r)=>{"use strict";r.d(t,{H4:()=>S,_V:()=>N,bL:()=>E});var n=r(12115),a=r(3468),i=r(70222),o=r(4129),l=r(97602),u=r(14806);function s(){return()=>{}}var c=r(95155),d="Avatar",[f,v]=(0,a.A)(d),[m,p]=f(d),y=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...a}=e,[i,o]=n.useState("idle");return(0,c.jsx)(m,{scope:r,imageLoadingStatus:i,onImageLoadingStatusChange:o,children:(0,c.jsx)(l.sG.span,{...a,ref:t})})});y.displayName=d;var g="AvatarImage",h=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:a,onLoadingStatusChange:d=()=>{},...f}=e,v=p(g,r),m=function(e,t){let{referrerPolicy:r,crossOrigin:a}=t,i=(0,u.useSyncExternalStore)(s,()=>!0,()=>!1),l=n.useRef(null),c=i?(l.current||(l.current=new window.Image),l.current):null,[d,f]=n.useState(()=>x(c,e));return(0,o.N)(()=>{f(x(c,e))},[c,e]),(0,o.N)(()=>{let e=e=>()=>{f(e)};if(!c)return;let t=e("loaded"),n=e("error");return c.addEventListener("load",t),c.addEventListener("error",n),r&&(c.referrerPolicy=r),"string"==typeof a&&(c.crossOrigin=a),()=>{c.removeEventListener("load",t),c.removeEventListener("error",n)}},[c,a,r]),d}(a,f),y=(0,i.c)(e=>{d(e),v.onImageLoadingStatusChange(e)});return(0,o.N)(()=>{"idle"!==m&&y(m)},[m,y]),"loaded"===m?(0,c.jsx)(l.sG.img,{...f,ref:t,src:a}):null});h.displayName=g;var b="AvatarFallback",w=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:a,...i}=e,o=p(b,r),[u,s]=n.useState(void 0===a);return n.useEffect(()=>{if(void 0!==a){let e=window.setTimeout(()=>s(!0),a);return()=>window.clearTimeout(e)}},[a]),u&&"loaded"!==o.imageLoadingStatus?(0,c.jsx)(l.sG.span,{...i,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=b;var E=y,N=h,S=w},46891:(e,t,r)=>{Promise.resolve().then(r.bind(r,12547))},64269:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i,z:()=>o});var n=r(2821),a=r(75889);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,n.$)(t))}function o(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:a=0,sizeType:i="normal"}=n;if(0===e)return"0 Byte";let o=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,o)).toFixed(a)," ").concat("accurate"===i?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][o])?t:"Bytest":null!=(r=["Bytes","KB","MB","GB","TB"][o])?r:"Bytes")}},70222:(e,t,r)=>{"use strict";r.d(t,{c:()=>a});var n=r(12115);function a(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},94446:(e,t,r)=>{"use strict";r.d(t,{s:()=>o,t:()=>i});var n=r(12115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function o(...e){return n.useCallback(i(...e),e)}},97602:(e,t,r)=>{"use strict";r.d(t,{hO:()=>u,sG:()=>l});var n=r(12115),a=r(47650),i=r(32467),o=r(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),a=n.forwardRef((e,n)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(a?r:t,{...i,ref:n})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function u(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,4850,8441,3840,7358],()=>t(46891)),_N_E=e.O()}]);