try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="c71b013f-041f-4c71-9811-839a67dfe9e4",e._sentryDebugIdIdentifier="sentry-dbid-c71b013f-041f-4c71-9811-839a67dfe9e4")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8308],{478:(e,t,r)=>{Promise.resolve().then(r.bind(r,83145))},4662:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>l,TN:()=>d,XL:()=>o});var a=r(95155);r(12115);var n=r(83101),s=r(64269);let i=(0,n.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...n}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,s.cn)(i({variant:r}),t),...n,"data-sentry-component":"Alert","data-sentry-source-file":"alert.tsx"})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,s.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...r,"data-sentry-component":"AlertTitle","data-sentry-source-file":"alert.tsx"})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,s.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r,"data-sentry-component":"AlertDescription","data-sentry-source-file":"alert.tsx"})}},6132:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(71847).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},20063:(e,t,r)=>{"use strict";var a=r(47260);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},20764:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,r:()=>l});var a=r(95155);r(12115);var n=r(32467),s=r(83101),i=r(64269);let l=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:s,asChild:o=!1,...d}=e,c=o?n.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:r,size:s,className:t})),...d,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},32467:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,Dc:()=>d,TL:()=>i});var a=r(12115),n=r(94446),s=r(95155);function i(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...s}=e;if(a.isValidElement(r)){var i;let e,l,o=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let r={...t};for(let a in t){let n=e[a],s=t[a];/^on[A-Z]/.test(a)?n&&s?r[a]=(...e)=>{let t=s(...e);return n(...e),t}:n&&(r[a]=n):"style"===a?r[a]={...n,...s}:"className"===a&&(r[a]=[n,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==a.Fragment&&(d.ref=t?(0,n.t)(t,o):o),a.cloneElement(r,d)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:n,...i}=e,l=a.Children.toArray(n),o=l.find(c);if(o){let e=o.props.children,n=l.map(t=>t!==o?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...i,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,s.jsx)(t,{...i,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var l=i("Slot"),o=Symbol("radix.slottable");function d(e){let t=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function c(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},64269:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s,z:()=>i});var a=r(2821),n=r(75889);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}function i(e){var t,r;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:n=0,sizeType:s="normal"}=a;if(0===e)return"0 Byte";let i=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,i)).toFixed(n)," ").concat("accurate"===s?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][i])?t:"Bytest":null!=(r=["Bytes","KB","MB","GB","TB"][i])?r:"Bytes")}},66094:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>s,aR:()=>i,wL:()=>c});var a=r(95155);r(12115);var n=r(64269);function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...r,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...r,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},71847:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(12115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:d="",children:c,iconNode:u,...f}=e;return(0,a.createElement)("svg",{ref:t,...i,width:n,height:n,stroke:r,strokeWidth:o?24*Number(l)/Number(n):l,className:s("lucide",d),...f},[...u.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),o=(e,t)=>{let r=(0,a.forwardRef)((r,i)=>{let{className:o,...d}=r;return(0,a.createElement)(l,{ref:i,iconNode:t,className:s("lucide-".concat(n(e)),o),...d})});return r.displayName="".concat(e),r}},83101:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var a=r(2821);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=a.$,i=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,o=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],a=null==l?void 0:l[e];if(null===t)return null;let s=n(t)||n(a);return i[e][s]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return s(e,o,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:r,className:a,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...d}[t]):({...l,...d})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},83145:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var a=r(95155),n=r(4662),s=r(20764),i=r(66094),l=r(6132),o=r(20063),d=r(12115),c=r(27122);function u(e){let{error:t,reset:r}=e,u=(0,o.useRouter)(),[f,m]=(0,d.useTransition)();(0,d.useEffect)(()=>{c.Cp(t)},[t]);let p=()=>{m(()=>{u.refresh(),r()})};return(0,a.jsxs)(i.Zp,{className:"border-red-500","data-sentry-element":"Card","data-sentry-component":"StatsError","data-sentry-source-file":"error.tsx",children:[(0,a.jsx)(i.aR,{className:"flex flex-col items-stretch space-y-0 border-b p-0 sm:flex-row","data-sentry-element":"CardHeader","data-sentry-source-file":"error.tsx",children:(0,a.jsx)("div",{className:"flex flex-1 flex-col justify-center gap-1 px-6 py-5 sm:py-6",children:(0,a.jsxs)(n.Fc,{variant:"destructive",className:"border-none","data-sentry-element":"Alert","data-sentry-source-file":"error.tsx",children:[(0,a.jsx)(l.A,{className:"h-4 w-4","data-sentry-element":"AlertCircle","data-sentry-source-file":"error.tsx"}),(0,a.jsx)(n.XL,{"data-sentry-element":"AlertTitle","data-sentry-source-file":"error.tsx",children:"Error"}),(0,a.jsxs)(n.TN,{className:"mt-2","data-sentry-element":"AlertDescription","data-sentry-source-file":"error.tsx",children:["Failed to load statistics: ",t.message]})]})})}),(0,a.jsx)(i.Wu,{className:"flex h-[316px] items-center justify-center p-6","data-sentry-element":"CardContent","data-sentry-source-file":"error.tsx",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-muted-foreground mb-4 text-sm",children:"Unable to display statistics at this time"}),(0,a.jsx)(s.$,{onClick:()=>p(),variant:"outline",className:"min-w-[120px]",disabled:f,"data-sentry-element":"Button","data-sentry-source-file":"error.tsx",children:"Try again"})]})})]})}},94446:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>s});var a=r(12115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,a=e.map(e=>{let a=n(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():n(e[t],null)}}}}function i(...e){return a.useCallback(s(...e),e)}}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,4850,8441,3840,7358],()=>t(478)),_N_E=e.O()}]);