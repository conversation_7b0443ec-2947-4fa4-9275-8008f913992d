try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},a=(new e.Error).stack;a&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[a]="419866fb-6759-42b0-b0cb-ea9bb1da9dc9",e._sentryDebugIdIdentifier="sentry-dbid-419866fb-6759-42b0-b0cb-ea9bb1da9dc9")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2547],{12800:(e,a,s)=>{"use strict";s.d(a,{Xi:()=>d,av:()=>o,j7:()=>c,tU:()=>i});var t=s(95155),r=s(12115),n=s(25667),l=s(64269);let i=n.bL,c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.B8,{ref:a,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...r})});c.displayName=n.B8.displayName;let d=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.l9,{ref:a,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm cursor-pointer",s),...r})});d.displayName=n.l9.displayName;let o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.UC,{ref:a,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...r})});o.displayName=n.UC.displayName},15959:(e,a,s)=>{Promise.resolve().then(s.bind(s,69560))},27048:(e,a,s)=>{"use strict";s.d(a,{h:()=>l});let t={enableCoursePurchase:!0,enableEnrollmentCode:!0,enableCoursePreview:!0,enablePaymentIntegration:!1};function r(){try{let e=localStorage.getItem("feature-flags");if(e)return{...t,...JSON.parse(e)}}catch(e){console.warn("Failed to parse feature flags from localStorage:",e)}return t}function n(e,a){try{let s={...r(),[e]:a};localStorage.setItem("feature-flags",JSON.stringify(s))}catch(e){console.warn("Failed to save feature flags to localStorage:",e)}}function l(){let e=r();return{flags:e,setFlag:n,canPurchase:e.enableCoursePurchase,canEnrollWithCode:e.enableEnrollmentCode,canPreviewCourse:e.enableCoursePreview,hasPaymentIntegration:e.enablePaymentIntegration}}},31936:(e,a,s)=>{"use strict";s.d(a,{p:()=>n});var t=s(95155);s(12115);var r=s(64269);function n(e){let{className:a,type:s,...n}=e;return(0,t.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...n,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},38004:(e,a,s)=>{"use strict";s.d(a,{Cf:()=>m,Es:()=>u,L3:()=>h,c7:()=>x,lG:()=>i,rr:()=>p,zM:()=>c});var t=s(95155);s(12115);var r=s(89511),n=s(65229),l=s(64269);function i(e){let{...a}=e;return(0,t.jsx)(r.bL,{"data-slot":"dialog",...a,"data-sentry-element":"DialogPrimitive.Root","data-sentry-component":"Dialog","data-sentry-source-file":"dialog.tsx"})}function c(e){let{...a}=e;return(0,t.jsx)(r.l9,{"data-slot":"dialog-trigger",...a,"data-sentry-element":"DialogPrimitive.Trigger","data-sentry-component":"DialogTrigger","data-sentry-source-file":"dialog.tsx"})}function d(e){let{...a}=e;return(0,t.jsx)(r.ZL,{"data-slot":"dialog-portal",...a,"data-sentry-element":"DialogPrimitive.Portal","data-sentry-component":"DialogPortal","data-sentry-source-file":"dialog.tsx"})}function o(e){let{className:a,...s}=e;return(0,t.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...s,"data-sentry-element":"DialogPrimitive.Overlay","data-sentry-component":"DialogOverlay","data-sentry-source-file":"dialog.tsx"})}function m(e){let{className:a,children:s,...i}=e;return(0,t.jsxs)(d,{"data-slot":"dialog-portal","data-sentry-element":"DialogPortal","data-sentry-component":"DialogContent","data-sentry-source-file":"dialog.tsx",children:[(0,t.jsx)(o,{"data-sentry-element":"DialogOverlay","data-sentry-source-file":"dialog.tsx"}),(0,t.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...i,"data-sentry-element":"DialogPrimitive.Content","data-sentry-source-file":"dialog.tsx",children:[s,(0,t.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","data-sentry-element":"DialogPrimitive.Close","data-sentry-source-file":"dialog.tsx",children:[(0,t.jsx)(n.A,{"data-sentry-element":"XIcon","data-sentry-source-file":"dialog.tsx"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",a),...s,"data-sentry-component":"DialogHeader","data-sentry-source-file":"dialog.tsx"})}function u(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...s,"data-sentry-component":"DialogFooter","data-sentry-source-file":"dialog.tsx"})}function h(e){let{className:a,...s}=e;return(0,t.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",a),...s,"data-sentry-element":"DialogPrimitive.Title","data-sentry-component":"DialogTitle","data-sentry-source-file":"dialog.tsx"})}function p(e){let{className:a,...s}=e;return(0,t.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",a),...s,"data-sentry-element":"DialogPrimitive.Description","data-sentry-component":"DialogDescription","data-sentry-source-file":"dialog.tsx"})}},42526:(e,a,s)=>{"use strict";s.d(a,{J:()=>l});var t=s(95155);s(12115);var r=s(10489),n=s(64269);function l(e){let{className:a,...s}=e;return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...s,"data-sentry-element":"LabelPrimitive.Root","data-sentry-component":"Label","data-sentry-source-file":"label.tsx"})}},46201:(e,a,s)=>{"use strict";s.d(a,{Separator:()=>i});var t=s(95155),r=s(12115),n=s(57268),l=s(64269);let i=r.forwardRef((e,a)=>{let{className:s,orientation:r="horizontal",decorative:i=!0,...c}=e;return(0,t.jsx)(n.b,{ref:a,decorative:i,orientation:r,className:(0,l.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",s),...c})});i.displayName=n.b.displayName},69560:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>Z});var t=s(95155),r=s(12115),n=s(66094),l=s(20764),i=s(31936),c=s(38004),d=s(43756),o=s(69264),m=(s(1775),s(54042)),x=s(48052),u=s(68465),h=s(52182),p=s(17649),y=s(49408),f=s(90800),g=s(9005),j=s(27048),b=s(97327),N=s(12800),v=s(88021),w=s(4449),k=s(93155),C=s(18613),A=s(29094),D=s(66063),I=s(5446),P=s(16928);let S=e=>{var a,s,r;let{course:l,activeTab:i,onTabChange:c}=e,d=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"IDR";return"IDR"===a?"Rp"+new Intl.NumberFormat("id-ID").format(e):new Intl.NumberFormat("id-ID",{style:"currency",currency:a}).format(e)};return(0,t.jsxs)(N.tU,{value:i,onValueChange:c,className:"w-full","data-sentry-element":"Tabs","data-sentry-component":"CourseDetailTabs","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsxs)(N.j7,{className:"grid w-full grid-cols-6 mb-6","data-sentry-element":"TabsList","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsxs)(N.Xi,{value:"overview",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(o.A,{className:"h-4 w-4","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Ringkasan"]}),(0,t.jsxs)(N.Xi,{value:"admissions",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(w.A,{className:"h-4 w-4","data-sentry-element":"GraduationCapIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Penerimaan"]}),(0,t.jsxs)(N.Xi,{value:"academics",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(k.A,{className:"h-4 w-4","data-sentry-element":"AwardIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Akademik"]}),(0,t.jsxs)(N.Xi,{value:"tuition",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(C.A,{className:"h-4 w-4","data-sentry-element":"DollarCircleIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Biaya"]}),(0,t.jsxs)(N.Xi,{value:"careers",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(A.A,{className:"h-4 w-4","data-sentry-element":"BriefcaseIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Karier"]}),(0,t.jsxs)(N.Xi,{value:"experience",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(D.A,{className:"h-4 w-4","data-sentry-element":"StarIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Pengalaman"]})]}),(0,t.jsx)(N.av,{value:"overview",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(o.A,{className:"h-5 w-5","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Ringkasan Kursus"]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold mb-2",children:"Deskripsi"}),(0,t.jsx)("p",{className:"text-gray-700",children:l.description})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Detail Kursus"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4 text-gray-500","data-sentry-element":"UsersIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),(0,t.jsxs)("span",{children:["Instruktur: ",l.instructor]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"mr-2 h-4 w-4 text-gray-500","data-sentry-element":"CalendarIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),(0,t.jsxs)("span",{children:["Durasi: ",new Date(l.startDate).toLocaleDateString("id-ID")," - "," ",new Date(l.endDate).toLocaleDateString("id-ID")]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(o.A,{className:"mr-2 h-4 w-4 text-gray-500","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),(0,t.jsxs)("span",{children:[l.modules.length," modul"]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(y.A,{className:"mr-2 h-4 w-4 text-gray-500","data-sentry-element":"CheckCircleIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),(0,t.jsxs)("span",{children:["Nilai kelulusan: ",l.minPassingScore,"%"]})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Apa yang Akan Anda Pelajari"}),(0,t.jsxs)("div",{className:"space-y-2",children:[l.modules.slice(0,3).map((e,a)=>(0,t.jsxs)("div",{className:"flex items-start gap-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-green-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e.title})]},e.id)),l.modules.length>3&&(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["+",l.modules.length-3," modul lagi"]})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Sertifikat"}),(0,t.jsx)("div",{className:"flex items-center gap-2",children:l.certificate.isEligible?(0,t.jsxs)(v.E,{variant:"default",className:"bg-green-100 text-green-800",children:[(0,t.jsx)(k.A,{className:"mr-1 h-3 w-3"}),"Sertifikat Tersedia"]}):(0,t.jsx)(v.E,{variant:"secondary",children:"Tidak Ada Sertifikat"})})]})]})]})}),(0,t.jsx)(N.av,{value:"admissions",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(w.A,{className:"h-5 w-5","data-sentry-element":"GraduationCapIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Informasi Penerimaan"]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"course-detail-tabs.tsx",children:[(null==(a=l.admissions)?void 0:a.requirements)&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Persyaratan"}),(0,t.jsx)("ul",{className:"space-y-1",children:l.admissions.requirements.map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e})]},a))})]}),(null==(s=l.admissions)?void 0:s.prerequisites)&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Prasyarat"}),(0,t.jsx)("ul",{className:"space-y-1",children:l.admissions.prerequisites.map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e})]},a))})]}),(null==(r=l.admissions)?void 0:r.applicationDeadline)&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Batas Waktu Pendaftaran"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("span",{className:"text-sm",children:new Date(l.admissions.applicationDeadline).toLocaleDateString("id-ID")})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Opsi Pendaftaran"}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:["code"===l.enrollmentType&&(0,t.jsx)(v.E,{variant:"outline",children:"Kode Pendaftaran Diperlukan"}),"invitation"===l.enrollmentType&&(0,t.jsx)(v.E,{variant:"outline",children:"Hanya Undangan"}),"purchase"===l.enrollmentType&&(0,t.jsx)(v.E,{variant:"outline",children:"Pembelian Langsung"}),"both"===l.enrollmentType&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.E,{variant:"outline",children:"Kode Pendaftaran"}),(0,t.jsx)(v.E,{variant:"outline",children:"Pembelian Langsung"})]})]})]})]})]})}),(0,t.jsx)(N.av,{value:"academics",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(k.A,{className:"h-5 w-5","data-sentry-element":"AwardIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Informasi Akademik"]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"course-detail-tabs.tsx",children:[l.academics&&(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Struktur Kursus"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Kredit:"}),(0,t.jsx)(v.E,{variant:"secondary",children:l.academics.credits})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Beban Kerja:"}),(0,t.jsx)("span",{className:"font-medium",children:l.academics.workload})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Metode Penilaian"}),(0,t.jsx)("ul",{className:"space-y-1",children:l.academics.assessment.map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e})]},a))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Modul Kursus"}),(0,t.jsx)("div",{className:"space-y-2",children:l.modules.map((e,a)=>(0,t.jsx)(n.Zp,{className:"p-3",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"font-medium",children:e.title}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),(0,t.jsxs)("div",{className:"mt-2 text-sm text-gray-500",children:[e.chapters.length," bab"]})]}),(0,t.jsxs)(v.E,{variant:"outline",children:["Modul ",a+1]})]})},e.id))})]})]})]})}),(0,t.jsx)(N.av,{value:"tuition",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(C.A,{className:"h-5 w-5","data-sentry-element":"DollarCircleIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Biaya & Pembiayaan"]})}),(0,t.jsx)(n.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"course-detail-tabs.tsx",children:l.tuitionAndFinancing?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Biaya Kursus"}),(0,t.jsx)("div",{className:"text-3xl font-bold text-green-600",children:d(l.tuitionAndFinancing.totalCost,l.currency)}),l.price&&l.price<l.tuitionAndFinancing.totalCost&&(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Penawaran khusus: ",d(l.price,l.currency),(0,t.jsxs)(v.E,{variant:"destructive",className:"ml-2",children:[Math.round((l.tuitionAndFinancing.totalCost-l.price)/l.tuitionAndFinancing.totalCost*100),"% OFF"]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Opsi Pembayaran"}),(0,t.jsx)("ul",{className:"space-y-1",children:l.tuitionAndFinancing.paymentOptions.map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-green-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e})]},a))})]}),l.tuitionAndFinancing.scholarships&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Beasiswa Tersedia"}),(0,t.jsx)("ul",{className:"space-y-1",children:l.tuitionAndFinancing.scholarships.map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(k.A,{className:"h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e})]},a))})]})]}):(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"text-4xl font-bold text-green-600 mb-2",children:"GRATIS"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Kursus ini tersedia tanpa biaya"})]})})]})}),(0,t.jsx)(N.av,{value:"careers",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(A.A,{className:"h-5 w-5","data-sentry-element":"BriefcaseIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Prospek Karier"]})}),(0,t.jsx)(n.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"course-detail-tabs.tsx",children:l.careers?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Prospek Karier"}),(0,t.jsx)("ul",{className:"space-y-1",children:l.careers.outcomes.map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e})]},a))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Industri"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:l.careers.industries.map((e,a)=>(0,t.jsx)(v.E,{variant:"outline",children:e},a))})]}),l.careers.averageSalary&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Gaji Rata-rata"}),(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:l.careers.averageSalary})]})]}):(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(A.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Informasi karier tidak tersedia"})]})})]})}),(0,t.jsx)(N.av,{value:"experience",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(D.A,{className:"h-5 w-5","data-sentry-element":"StarIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Pengalaman Mahasiswa"]})}),(0,t.jsx)(n.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"course-detail-tabs.tsx",children:l.studentExperience?(0,t.jsxs)(t.Fragment,{children:[l.studentExperience.testimonials.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-4",children:"Testimoni Mahasiswa"}),(0,t.jsx)("div",{className:"space-y-4",children:l.studentExperience.testimonials.map((e,a)=>(0,t.jsx)(n.Zp,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(u.A,{className:"h-5 w-5 text-blue-600"})})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"text-sm italic mb-2",children:['"',e.feedback,'"']}),(0,t.jsxs)("p",{className:"font-medium text-sm",children:["- ",e.name]})]})]})},a))})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[l.studentExperience.facilities.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-semibold mb-2 flex items-center gap-2",children:[(0,t.jsx)(I.A,{className:"h-4 w-4"}),"Fasilitas"]}),(0,t.jsx)("ul",{className:"space-y-1",children:l.studentExperience.facilities.map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-green-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e})]},a))})]}),l.studentExperience.support.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-semibold mb-2 flex items-center gap-2",children:[(0,t.jsx)(P.A,{className:"h-4 w-4"}),"Dukungan Mahasiswa"]}),(0,t.jsx)("ul",{className:"space-y-1",children:l.studentExperience.support.map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e})]},a))})]})]})]}):(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(D.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Informasi pengalaman mahasiswa tidak tersedia"})]})})]})})]})};var T=s(42526),B=s(46201),E=s(61172),F=s(23277),R=s(26345);let K=e=>{let{course:a,isOpen:s,onClose:n,onPaymentSuccess:d}=e,[o,m]=(0,r.useState)("card"),[x,u]=(0,r.useState)(!1),[h,p]=(0,r.useState)("details"),[g,j]=(0,r.useState)({cardNumber:"",expiryDate:"",cvv:"",cardholderName:"",email:"",billingAddress:""}),b=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"IDR";return"IDR"===a?"Rp"+new Intl.NumberFormat("id-ID").format(e):new Intl.NumberFormat("id-ID",{style:"currency",currency:a}).format(e)},N=(e,a)=>{j(s=>({...s,[e]:a}))},w=()=>"card"===o?g.cardNumber&&g.expiryDate&&g.cvv&&g.cardholderName&&g.email:g.email,k=async()=>{if(w()){u(!0),p("processing");try{await new Promise(e=>setTimeout(e,3e3)),Math.random()>.1?(p("success"),setTimeout(()=>{d(),n(),A()},2e3)):p("error")}catch(e){p("error")}finally{u(!1)}}},A=()=>{p("details"),j({cardNumber:"",expiryDate:"",cvv:"",cardholderName:"",email:"",billingAddress:""}),u(!1)},D=()=>{x||"success"===h||(n(),A())},I=()=>(0,t.jsxs)("div",{className:"space-y-6","data-sentry-component":"renderPaymentDetails","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Ringkasan Kursus"}),(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:a.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["by ",a.instructor]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsx)("p",{className:"text-xl font-bold text-green-600",children:a.price?b(a.price,a.currency):"Free"})})]}),(0,t.jsxs)("div",{className:"flex gap-2 text-sm text-gray-600",children:[(0,t.jsxs)(v.E,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"payment-modal.tsx",children:[a.modules.length," modul"]}),a.certificate.isEligible&&(0,t.jsx)(v.E,{variant:"outline",children:"Sertifikat disertakan"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(T.J,{className:"text-base font-semibold mb-3 block","data-sentry-element":"Label","data-sentry-source-file":"payment-modal.tsx",children:"Metode Pembayaran"}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[(0,t.jsxs)(l.$,{variant:"card"===o?"default":"outline",onClick:()=>m("card"),className:"h-16 flex-col gap-1","data-sentry-element":"Button","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsx)(E.A,{className:"h-5 w-5","data-sentry-element":"CreditCardIcon","data-sentry-source-file":"payment-modal.tsx"}),(0,t.jsx)("span",{className:"text-xs",children:"Kartu Kredit"})]}),(0,t.jsxs)(l.$,{variant:"paypal"===o?"default":"outline",onClick:()=>m("paypal"),className:"h-16 flex-col gap-1","data-sentry-element":"Button","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsx)(C.A,{className:"h-5 w-5","data-sentry-element":"DollarCircleIcon","data-sentry-source-file":"payment-modal.tsx"}),(0,t.jsx)("span",{className:"text-xs",children:"PayPal"})]}),(0,t.jsxs)(l.$,{variant:"bank"===o?"default":"outline",onClick:()=>m("bank"),className:"h-16 flex-col gap-1","data-sentry-element":"Button","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsx)(F.A,{className:"h-5 w-5","data-sentry-element":"ShoppingCartIcon","data-sentry-source-file":"payment-modal.tsx"}),(0,t.jsx)("span",{className:"text-xs",children:"Transfer Bank"})]})]})]}),"card"===o&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(T.J,{htmlFor:"email",children:"Alamat Email"}),(0,t.jsx)(i.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:g.email,onChange:e=>N("email",e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(T.J,{htmlFor:"cardholderName",children:"Nama Pemegang Kartu"}),(0,t.jsx)(i.p,{id:"cardholderName",placeholder:"Nama lengkap pada kartu",value:g.cardholderName,onChange:e=>N("cardholderName",e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(T.J,{htmlFor:"cardNumber",children:"Nomor Kartu"}),(0,t.jsx)(i.p,{id:"cardNumber",placeholder:"1234 5678 9012 3456",value:g.cardNumber,onChange:e=>N("cardNumber",e.target.value)})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(T.J,{htmlFor:"expiryDate",children:"Tanggal Kedaluwarsa"}),(0,t.jsx)(i.p,{id:"expiryDate",placeholder:"BB/TT",value:g.expiryDate,onChange:e=>N("expiryDate",e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(T.J,{htmlFor:"cvv",children:"CVV"}),(0,t.jsx)(i.p,{id:"cvv",placeholder:"123",value:g.cvv,onChange:e=>N("cvv",e.target.value)})]})]})]}),"paypal"===o&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(T.J,{htmlFor:"email",children:"Alamat Email"}),(0,t.jsx)(i.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:g.email,onChange:e=>N("email",e.target.value)})]}),(0,t.jsx)("div",{className:"text-center py-4",children:(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Anda akan dialihkan ke PayPal untuk menyelesaikan pembayaran"})})]}),"bank"===o&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(T.J,{htmlFor:"email",children:"Alamat Email"}),(0,t.jsx)(i.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:g.email,onChange:e=>N("email",e.target.value)})]}),(0,t.jsx)("div",{className:"text-center py-4",children:(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Instruksi transfer bank akan dikirim ke email Anda"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 bg-blue-50 p-3 rounded-lg",children:[(0,t.jsx)(R.A,{className:"h-4 w-4","data-sentry-element":"LockIcon","data-sentry-source-file":"payment-modal.tsx"}),(0,t.jsx)("span",{children:"Informasi pembayaran Anda dienkripsi dan aman"})]}),(0,t.jsx)(B.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"payment-modal.tsx"}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsx)(l.$,{variant:"outline",onClick:D,className:"flex-1",disabled:x,"data-sentry-element":"Button","data-sentry-source-file":"payment-modal.tsx",children:"Batal"}),(0,t.jsxs)(l.$,{onClick:k,className:"flex-1 bg-green-600 hover:bg-green-700",disabled:!w()||x,"data-sentry-element":"Button","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsx)(F.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ShoppingCartIcon","data-sentry-source-file":"payment-modal.tsx"}),"Selesaikan Pembelian"]})]})]}),P=()=>(0,t.jsxs)("div",{className:"text-center py-8","data-sentry-component":"renderProcessing","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsx)("div",{className:"animate-spin h-12 w-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"}),(0,t.jsx)("h4",{className:"text-lg font-semibold mb-2",children:"Memproses Pembayaran"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Harap tunggu sementara kami memproses pembayaran Anda..."})]}),S=()=>(0,t.jsxs)("div",{className:"text-center py-8","data-sentry-component":"renderSuccess","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsx)(y.A,{className:"h-16 w-16 text-green-600 mx-auto mb-4","data-sentry-element":"CheckCircleIcon","data-sentry-source-file":"payment-modal.tsx"}),(0,t.jsx)("h4",{className:"text-xl font-semibold mb-2",children:"Pembayaran Berhasil!"}),(0,t.jsxs)("p",{className:"text-gray-600 mb-4",children:["Anda telah berhasil terdaftar di ",a.name]}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Mengalihkan ke konten kursus..."})]}),K=()=>(0,t.jsxs)("div",{className:"text-center py-8","data-sentry-component":"renderError","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsx)(f.A,{className:"h-16 w-16 text-red-600 mx-auto mb-4","data-sentry-element":"XCircleIcon","data-sentry-source-file":"payment-modal.tsx"}),(0,t.jsx)("h4",{className:"text-xl font-semibold mb-2",children:"Pembayaran Gagal"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Terjadi masalah saat memproses pembayaran Anda. Silakan coba lagi."}),(0,t.jsxs)("div",{className:"flex gap-3 justify-center",children:[(0,t.jsx)(l.$,{variant:"outline",onClick:D,"data-sentry-element":"Button","data-sentry-source-file":"payment-modal.tsx",children:"Batal"}),(0,t.jsx)(l.$,{onClick:()=>p("details"),"data-sentry-element":"Button","data-sentry-source-file":"payment-modal.tsx",children:"Coba Lagi"})]})]});return(0,t.jsx)(c.lG,{open:s,onOpenChange:D,"data-sentry-element":"Dialog","data-sentry-component":"PaymentModal","data-sentry-source-file":"payment-modal.tsx",children:(0,t.jsxs)(c.Cf,{className:"sm:max-w-md max-h-[90vh] overflow-y-auto","data-sentry-element":"DialogContent","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsxs)(c.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsxs)(c.L3,{className:"flex items-center gap-2","data-sentry-element":"DialogTitle","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsx)(F.A,{className:"h-5 w-5","data-sentry-element":"ShoppingCartIcon","data-sentry-source-file":"payment-modal.tsx"}),"success"===h?"Pembelian Selesai":"Beli Kursus"]}),"details"===h&&(0,t.jsx)(c.rr,{children:"Selesaikan pembelian Anda untuk mendapatkan akses instan ke kursus"})]}),(()=>{switch(h){case"processing":return P();case"success":return S();case"error":return K();default:return I()}})()]})})};var L=s(37450),O=s(15239),z=s(52619),M=s.n(z);let $=e=>{var a,s;let{course:r,isOpen:i,onClose:d,actionType:x}=e;return(0,t.jsx)(c.lG,{open:i,onOpenChange:()=>{},"data-sentry-element":"Dialog","data-sentry-component":"CourseSuccessModal","data-sentry-source-file":"course-success-modal.tsx",children:(0,t.jsxs)(c.Cf,{className:"sm:max-w-2xl w-[95vw] max-h-[85vh] flex flex-col p-0 gap-0","data-sentry-element":"DialogContent","data-sentry-source-file":"course-success-modal.tsx",children:[(0,t.jsx)("div",{className:"flex-shrink-0 p-6 pb-4",children:(0,t.jsxs)(c.c7,{className:"text-center","data-sentry-element":"DialogHeader","data-sentry-source-file":"course-success-modal.tsx",children:[(0,t.jsx)("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4",children:(0,t.jsx)(y.A,{className:"h-8 w-8 text-green-600","data-sentry-element":"CheckCircleIcon","data-sentry-source-file":"course-success-modal.tsx"})}),(0,t.jsx)(c.L3,{className:"text-2xl font-bold text-green-900 text-center","data-sentry-element":"DialogTitle","data-sentry-source-file":"course-success-modal.tsx",children:"purchase"===x?"Pembelian Berhasil!":"Pendaftaran Berhasil!"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"purchase"===x?"Selamat! Anda telah berhasil membeli dan terdaftar di kursus ini. Anda sekarang dapat mengakses semua materi pembelajaran.":"Selamat! Anda telah berhasil terdaftar di kursus ini. Anda sekarang dapat mengakses semua materi pembelajaran."})]})}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto px-6 min-h-0",children:(0,t.jsx)(n.Zp,{className:"border-2 border-green-200 bg-green-50/30","data-sentry-element":"Card","data-sentry-source-file":"course-success-modal.tsx",children:(0,t.jsxs)(n.Wu,{className:"p-0","data-sentry-element":"CardContent","data-sentry-source-file":"course-success-modal.tsx",children:[(0,t.jsxs)("div",{className:"h-32 rounded-t-lg flex items-center justify-center relative overflow-hidden",children:[r.thumbnail?(0,t.jsx)(O.default,{src:r.thumbnail,alt:r.name,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}):(0,t.jsxs)("div",{className:"h-32 bg-gradient-to-br from-blue-500 to-purple-600 w-full flex items-center justify-center",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,t.jsxs)("div",{className:"relative z-10 text-center text-white",children:[(0,t.jsx)(o.A,{className:"h-12 w-12 mx-auto mb-2 opacity-80"}),(0,t.jsx)("p",{className:"text-sm font-medium",children:r.code})]})]}),"purchase"===x&&r.price&&(0,t.jsx)(v.E,{className:"absolute top-3 right-3 bg-green-600 hover:bg-green-600",variant:"default",children:function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"IDR";return"IDR"===a?"Rp"+new Intl.NumberFormat("id-ID").format(e):new Intl.NumberFormat("id-ID",{style:"currency",currency:a}).format(e)}(r.price,r.currency)})]}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-bold text-gray-900 line-clamp-2",children:r.name}),(0,t.jsx)("p",{className:"text-gray-600 text-sm line-clamp-2",children:r.description})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4","data-sentry-element":"UsersIcon","data-sentry-source-file":"course-success-modal.tsx"}),(0,t.jsxs)("span",{children:["Instruktur: ",r.instructor]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"mr-2 h-4 w-4","data-sentry-element":"CalendarIcon","data-sentry-source-file":"course-success-modal.tsx"}),(0,t.jsxs)("span",{children:[new Date(r.startDate).toLocaleDateString("id-ID")," - "," ",new Date(r.endDate).toLocaleDateString("id-ID")]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(o.A,{className:"mr-2 h-4 w-4","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"course-success-modal.tsx"}),(0,t.jsxs)("span",{children:[r.modules.length," modul"]})]}),r.certificate.isEligible&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(k.A,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Sertifikat tersedia"})]})]}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[r.certificate.isEligible&&(0,t.jsxs)(v.E,{variant:"secondary",children:[(0,t.jsx)(k.A,{className:"mr-1 h-3 w-3"}),"Sertifikat"]}),(null==(a=r.academics)?void 0:a.workload)&&(0,t.jsx)(v.E,{variant:"outline",children:r.academics.workload}),(null==(s=r.academics)?void 0:s.credits)&&(0,t.jsxs)(v.E,{variant:"outline",children:[r.academics.credits," kredit"]})]})]})]})})}),(0,t.jsxs)("div",{className:"flex-shrink-0 border-t bg-white p-6 space-y-4",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,t.jsx)(M(),{href:"/my-courses/".concat(r.id),className:"flex-1","data-sentry-element":"Link","data-sentry-source-file":"course-success-modal.tsx",children:(0,t.jsxs)(l.$,{className:"w-full bg-green-600 hover:bg-green-700",size:"lg","data-sentry-element":"Button","data-sentry-source-file":"course-success-modal.tsx",children:[(0,t.jsx)(L.A,{className:"mr-2 h-4 w-4","data-sentry-element":"PlayIcon","data-sentry-source-file":"course-success-modal.tsx"}),"Mulai Belajar Sekarang",(0,t.jsx)(m.A,{className:"ml-2 h-4 w-4","data-sentry-element":"ArrowRightIcon","data-sentry-source-file":"course-success-modal.tsx"})]})}),(0,t.jsx)(M(),{href:"/my-courses",className:"flex-1","data-sentry-element":"Link","data-sentry-source-file":"course-success-modal.tsx",children:(0,t.jsxs)(l.$,{variant:"outline",className:"w-full",size:"lg","data-sentry-element":"Button","data-sentry-source-file":"course-success-modal.tsx",children:[(0,t.jsx)(o.A,{className:"mr-2 h-4 w-4","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"course-success-modal.tsx"}),"Lihat Semua Kursus Saya"]})})]}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)(l.$,{variant:"ghost",onClick:d,className:"text-gray-500 hover:text-gray-700","data-sentry-element":"Button","data-sentry-source-file":"course-success-modal.tsx",children:"Tutup dan kembali ke katalog"})})]})]})})},Z=()=>{let{isEnrolled:e,enrollInCourseWithPurchase:a,isEnrolledInCourse:s}=(0,g.q)(),{flags:N,setFlag:v}=(0,j.h)(),[w,k]=(0,r.useState)(null),[C,A]=(0,r.useState)(""),[D,I]=(0,r.useState)(!1),[P,T]=(0,r.useState)(!1),[B,E]=(0,r.useState)(!1),[F,R]=(0,r.useState)(!1),[L,O]=(0,r.useState)(!1),[z,Z]=(0,r.useState)("purchase"),[G,_]=(0,r.useState)("overview"),[H,J]=(0,r.useState)(""),[X,U]=(0,r.useState)({show:!1,message:"",type:"success"}),[W,q]=(0,r.useState)(!0),[Y,V]=(0,r.useState)(!1),[Q,ee]=(0,r.useState)(!1),ea=(0,r.useRef)(null),[es,et]=(0,r.useState)([]),[er,en]=(0,r.useState)(!0),el=()=>{let e=ea.current;if(!e)return;let{scrollTop:a,scrollHeight:s,clientHeight:t}=e;V(a>5),ee(s-t>100&&a<s-t-5),a>0&&q(!1)};(0,r.useEffect)(()=>{let e=ea.current;if(!e)return;el();let a=setTimeout(()=>el(),100),s=setTimeout(()=>el(),500),t=setTimeout(()=>el(),1e3),r=new ResizeObserver(()=>{el()});return r.observe(e),()=>{clearTimeout(a),clearTimeout(s),clearTimeout(t),r.disconnect()}},[P,w]),(0,r.useEffect)(()=>{if(P&&w){let e=setTimeout(()=>el(),200);return()=>clearTimeout(e)}},[G,P,w]),(0,r.useEffect)(()=>{ei()},[]);let ei=async()=>{try{en(!0);let e=await fetch("/api/courses?public=true"),a=await e.json();a.success?et(a.courses||[]):ec("Failed to fetch courses","error")}catch(e){console.error("Error fetching courses:",e),ec("Failed to fetch courses","error")}finally{en(!1)}},ec=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success";U({show:!0,message:e,type:a}),setTimeout(()=>{U(e=>({...e,show:!1}))},3e3)},ed=e=>{k(e),_("overview"),T(!0)},eo=e=>{k(e),I(!0)},em=e=>{k(e),E(!0)};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 p-8","data-sentry-component":"AvailableCoursesPage","data-sentry-source-file":"page.tsx",children:(0,t.jsxs)("div",{className:"mx-auto max-w-7xl space-y-6 pb-8",children:[(0,t.jsx)(d.B,{"data-sentry-element":"Breadcrumbs","data-sentry-source-file":"page.tsx"}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(o.A,{className:"h-8 w-8 text-[var(--iai-primary)]","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"page.tsx"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Kursus Tersedia"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Jelajahi dan daftar kursus profesional"})]})]}),!1]}),!1,e&&(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-green-900",children:"Anda sudah terdaftar!"}),(0,t.jsx)("p",{className:"text-sm text-green-700",children:"Akses kursus yang Anda daftari dan lanjutkan belajar"})]}),(0,t.jsx)(M(),{href:"/my-courses",children:(0,t.jsxs)(l.$,{variant:"iai",children:["Ke Kursus Saya",(0,t.jsx)(m.A,{className:"ml-2 h-4 w-4"})]})})]})}),er?(0,t.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[...Array(6)].map((e,a)=>(0,t.jsxs)(n.Zp,{className:"overflow-hidden animate-pulse",children:[(0,t.jsx)("div",{className:"aspect-video bg-gray-200"}),(0,t.jsxs)(n.Wu,{className:"p-6",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3 mb-4"}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16"})]})]})]},a))}):(0,t.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:es.map(e=>(0,t.jsx)(b.A,{course:e,onClick:()=>ed(e),onEnroll:()=>eo(e),onPurchase:()=>em(e),isEnrolled:s(e.id)},e.id))}),!er&&0===es.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(o.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Tidak ada kursus ditemukan"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Tidak ada kursus yang tersedia saat ini"})]}),(0,t.jsx)(c.lG,{open:P,onOpenChange:e=>{T(e),e||k(null)},"data-sentry-element":"Dialog","data-sentry-source-file":"page.tsx",children:(0,t.jsxs)(c.Cf,{className:"sm:max-w-4xl p-0 gap-0 h-[90vh] flex flex-col","data-sentry-element":"DialogContent","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)("div",{className:"p-6 pb-0 flex-shrink-0",children:(0,t.jsx)(c.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"page.tsx",children:(0,t.jsx)(c.L3,{className:"text-2xl","data-sentry-element":"DialogTitle","data-sentry-source-file":"page.tsx",children:null==w?void 0:w.name})})}),(0,t.jsxs)("div",{ref:ea,onScroll:el,className:"flex-1 overflow-y-auto scrollbar-visible min-h-0 relative scroll-container ".concat(W?"scroll-hint":""," ").concat(Y?"can-scroll-up":""," ").concat(Q?"can-scroll-down":""),children:[(0,t.jsx)("style",{dangerouslySetInnerHTML:{__html:'\n                  .scrollbar-visible::-webkit-scrollbar {\n                    width: 12px;\n                  }\n                  .scrollbar-visible::-webkit-scrollbar-track {\n                    background: rgb(243 244 246); /* gray-100 */\n                    border-radius: 6px;\n                  }\n                  .scrollbar-visible::-webkit-scrollbar-thumb {\n                    background: rgb(209 213 219); /* gray-300 */\n                    border-radius: 6px;\n                  }\n                  .scrollbar-visible::-webkit-scrollbar-thumb:hover {\n                    background: rgb(156 163 175); /* gray-400 */\n                  }\n                  /* For Firefox */\n                  .scrollbar-visible {\n                    scrollbar-width: thin;\n                    scrollbar-color: rgb(209 213 219) rgb(243 244 246);\n                  }\n                  \n                  /* Scroll Indicators */\n                  .scroll-container::before {\n                    content: "";\n                    position: absolute;\n                    top: 0;\n                    left: 0;\n                    right: 0;\n                    height: 20px;\n                    background: linear-gradient(to bottom, rgba(255,255,255,0.9), transparent);\n                    pointer-events: none;\n                    z-index: 10;\n                    opacity: 0;\n                    transition: opacity 0.3s ease;\n                  }\n                  \n                  .scroll-container::after {\n                    content: "";\n                    position: absolute;\n                    bottom: 0;\n                    left: 0;\n                    right: 0;\n                    height: 20px;\n                    background: linear-gradient(to top, rgba(255,255,255,0.9), transparent);\n                    pointer-events: none;\n                    z-index: 10;\n                    opacity: 0;\n                    transition: opacity 0.3s ease;\n                  }\n                  \n                  .scroll-container.can-scroll-up::before {\n                    opacity: 1;\n                  }\n                  \n                  .scroll-container.can-scroll-down::after {\n                    opacity: 1;\n                  }\n                  \n                  /* Pulsing scrollbar animation for initial hint */\n                  @keyframes pulse-scrollbar {\n                    0%, 100% { opacity: 0.6; }\n                    50% { opacity: 1; }\n                  }\n                  \n                  .scroll-hint .scrollbar-visible::-webkit-scrollbar-thumb {\n                    animation: pulse-scrollbar 2s infinite;\n                  }\n                  \n                  /* Scroll instruction overlay */\n                  .scroll-instruction {\n                    position: absolute;\n                    top: 50%;\n                    right: 20px;\n                    transform: translateY(-50%);\n                    background: rgba(59, 130, 246, 0.9);\n                    color: white;\n                    padding: 8px 12px;\n                    border-radius: 8px;\n                    font-size: 12px;\n                    z-index: 20;\n                    animation: fadeInOut 4s ease-in-out;\n                    pointer-events: none;\n                  }\n                  \n                  @keyframes fadeInOut {\n                    0%, 100% { opacity: 0; transform: translateY(-50%) translateX(10px); }\n                    10%, 90% { opacity: 1; transform: translateY(-50%) translateX(0); }\n                  }\n                '}}),(0,t.jsx)("div",{className:"p-6 pt-4",children:w&&(0,t.jsx)(S,{course:w,activeTab:G,onTabChange:_})})]}),Q&&(0,t.jsx)("div",{className:"flex justify-center py-2 bg-gray-50 border-t border-gray-100",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 text-gray-500 text-sm animate-bounce",children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Gulir ke bawah untuk melihat semua detail"}),(0,t.jsx)(x.A,{className:"h-4 w-4"})]})}),w&&(0,t.jsx)("div",{className:"p-6 pt-4 border-t bg-white flex-shrink-0",children:(0,t.jsxs)("div",{className:"flex gap-4",children:[w.isPurchasable&&N.enableCoursePurchase&&(0,t.jsxs)(l.$,{onClick:()=>em(w),variant:"iai",className:"flex-1",children:["Beli seharga ",w.price?"IDR"===w.currency?"Rp"+new Intl.NumberFormat("id-ID").format(w.price):new Intl.NumberFormat("id-ID",{style:"currency",currency:w.currency||"IDR"}).format(w.price):"Gratis"]}),N.enableEnrollmentCode&&w.enrollmentCode&&(0,t.jsx)(l.$,{onClick:()=>eo(w),variant:"outline",className:"flex-1",children:"Gunakan Kode Pendaftaran"})]})})]})}),w&&(0,t.jsx)(K,{course:w,isOpen:B,onClose:()=>E(!1),onPaymentSuccess:()=>{w&&(a(w),E(!1),T(!1),Z("purchase"),O(!0))}}),w&&(0,t.jsx)($,{course:w,isOpen:L,onClose:()=>{O(!1),k(null)},actionType:z}),(0,t.jsx)(c.lG,{open:D,onOpenChange:e=>{I(e),e||(J(""),A(""),k(null))},"data-sentry-element":"Dialog","data-sentry-source-file":"page.tsx",children:(0,t.jsxs)(c.Cf,{className:"sm:max-w-md","data-sentry-element":"DialogContent","data-sentry-source-file":"page.tsx",children:[(0,t.jsxs)(c.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"page.tsx",children:[(0,t.jsxs)(c.L3,{className:"text-xl","data-sentry-element":"DialogTitle","data-sentry-source-file":"page.tsx",children:["Daftar di ",null==w?void 0:w.name]}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Masukkan kode pendaftaran yang diberikan oleh instruktur Anda"})]}),(0,t.jsxs)("div",{className:"mt-4 space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2 rounded-lg bg-gray-50 p-4",children:[(0,t.jsxs)("p",{className:"flex items-center text-gray-700",children:[(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4","data-sentry-element":"UsersIcon","data-sentry-source-file":"page.tsx"}),"Instruktur: ",null==w?void 0:w.instructor]}),w&&(0,t.jsxs)("p",{className:"flex items-center text-gray-700",children:[(0,t.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Durasi: ",new Date(w.startDate).toLocaleDateString("id-ID")," - ",new Date(w.endDate).toLocaleDateString("id-ID")]})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(p.A,{className:"absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform ".concat(H?"text-red-500":"text-gray-400"),"data-sentry-element":"KeyIcon","data-sentry-source-file":"page.tsx"}),(0,t.jsx)(i.p,{placeholder:w?"Masukkan kode (contoh: ".concat(w.enrollmentCode,")"):"Masukkan kode pendaftaran",value:C,onChange:e=>A(e.target.value),className:"pl-10 ".concat(H?"border-red-500 focus:border-red-500 focus:ring-red-500":""),"data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),H&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:H})]}),(0,t.jsx)(l.$,{onClick:()=>{if(!w)return;let e=w.enrollmentCode;if(s(w.id))return void J("You are already enrolled in this course.");e===C?(a(w),I(!1),T(!1),J(""),A(""),Z("enrollment"),O(!0)):J("Invalid enrollment code. Please try again.")},className:"w-full",size:"lg","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Selesaikan Pendaftaran"})]})]})}),!1,X.show&&(0,t.jsx)("div",{className:"animate-in slide-in-from-bottom-2 fixed right-4 bottom-4 z-50",children:(0,t.jsxs)("div",{className:"flex min-w-[300px] items-center space-x-3 rounded-lg px-6 py-4 shadow-lg ".concat("success"===X.type?"bg-[var(--iai-primary)] text-white":"loading"===X.type?"bg-blue-600 text-white":"bg-red-600 text-white"," "),children:["success"===X.type?(0,t.jsx)(y.A,{className:"h-5 w-5 flex-shrink-0"}):"loading"===X.type?(0,t.jsx)("div",{className:"h-5 w-5 flex-shrink-0 animate-spin border-2 border-white border-t-transparent rounded-full"}):(0,t.jsx)(f.A,{className:"h-5 w-5 flex-shrink-0"}),(0,t.jsx)("p",{className:"font-medium",children:X.message})]})})]})})}},97327:(e,a,s)=>{"use strict";s.d(a,{A:()=>h});var t=s(95155);s(12115);var r=s(66094),n=s(20764),l=s(88021),i=s(69264),c=s(54042),d=s(23277),o=s(73370),m=s(68465),x=s(15239),u=s(27048);let h=e=>{let{course:a,onEnroll:s,onPurchase:h,onClick:p,isEnrolled:y=!1}=e,{canPurchase:f,canEnrollWithCode:g}=(0,u.h)(),j=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"IDR";return"IDR"===a?"Rp"+new Intl.NumberFormat("id-ID").format(e):new Intl.NumberFormat("id-ID",{style:"currency",currency:a}).format(e)};return(0,t.jsx)(r.Zp,{className:"group cursor-pointer transition-all hover:shadow-lg hover:scale-[1.02] overflow-hidden p-0 h-full",onClick:p,"data-sentry-element":"Card","data-sentry-component":"CoursePreviewCard","data-sentry-source-file":"course-preview-card.tsx",children:(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"aspect-[4/3] relative overflow-hidden bg-gray-100 flex-shrink-0",children:[a.thumbnail?(0,t.jsx)(x.default,{src:a.thumbnail,alt:a.name,fill:!0,className:"object-cover w-full h-full",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:!1}):(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)("div",{className:"w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,t.jsxs)("div",{className:"relative z-10 text-center text-white",children:[(0,t.jsx)(i.A,{className:"h-12 w-12 mx-auto mb-1 opacity-80"}),(0,t.jsx)("p",{className:"text-xs font-medium",children:a.code})]})]})}),a.isPurchasable&&(0,t.jsx)(l.E,{className:"absolute top-3 right-3 bg-[var(--iai-primary)] hover:bg-[var(--iai-primary)]",variant:"default",children:a.price?j(a.price,a.currency):"Gratis"})]}),(0,t.jsxs)("div",{className:"flex flex-col flex-grow p-4",children:[(0,t.jsxs)("div",{className:"space-y-1 mb-3 min-h-[60px]",children:[(0,t.jsx)("h3",{className:"text-lg font-bold group-hover:text-blue-600 transition-colors line-clamp-2",children:a.name}),(0,t.jsx)("p",{className:"text-gray-600 text-xs line-clamp-2",children:a.description})]}),(0,t.jsxs)("div",{className:"space-y-1 mb-4 text-xs text-gray-600 min-h-[32px]",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(m.A,{className:"mr-1 h-3 w-3 flex-shrink-0","data-sentry-element":"UsersIcon","data-sentry-source-file":"course-preview-card.tsx"}),(0,t.jsx)("span",{className:"line-clamp-1",children:a.instructor})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(i.A,{className:"mr-1 h-3 w-3 flex-shrink-0","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"course-preview-card.tsx"}),(0,t.jsxs)("span",{children:[a.modules.length," modul"]})]})]}),(0,t.jsx)("div",{className:"flex-grow"}),(0,t.jsx)("div",{className:"mt-auto",children:y?(0,t.jsxs)(n.$,{onClick:e=>{e.stopPropagation(),null==p||p()},variant:"outline",className:"w-full bg-green-50 border-green-200 text-green-700 hover:bg-green-100",disabled:!0,children:[(0,t.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Sudah Terdaftar"]}):a.previewMode?(0,t.jsxs)(n.$,{onClick:e=>{e.stopPropagation(),null==p||p()},variant:"outline",className:"w-full",children:[(0,t.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Lihat Detail",(0,t.jsx)(c.A,{className:"ml-2 h-4 w-4"})]}):a.isPurchasable&&f?(0,t.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsx)("span",{className:"text-2xl font-bold text-[var(--iai-primary)]",children:a.price?j(a.price,a.currency):"Gratis"})}),(0,t.jsxs)(n.$,{onClick:e=>{e.stopPropagation(),null==h||h()},variant:"iai",className:"w-full",children:[(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Beli Kursus"]}),g&&a.enrollmentCode&&(0,t.jsxs)(n.$,{onClick:e=>{e.stopPropagation(),null==s||s()},variant:"outline",className:"w-full",children:[(0,t.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Gunakan Kode Pendaftaran"]})]}):g&&a.enrollmentCode?(0,t.jsxs)(n.$,{onClick:e=>{e.stopPropagation(),null==s||s()},className:"w-full",children:[(0,t.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Daftar Sekarang"]}):(0,t.jsxs)(n.$,{onClick:e=>{e.stopPropagation(),null==p||p()},variant:"outline",className:"w-full","data-sentry-element":"Button","data-sentry-component":"renderActionButton","data-sentry-source-file":"course-preview-card.tsx",children:[(0,t.jsx)(i.A,{className:"mr-2 h-4 w-4","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"course-preview-card.tsx"}),"Lihat Kursus"]})})]})]})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[4909,7055,4736,660,5239,5667,892,5542,3324,4850,8441,3840,7358],()=>a(15959)),_N_E=e.O()}]);