{"version": 3, "middleware": {"/": {"files": ["server/edge-instrumentation.js", "server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|static|.*\\..*|_static|_vercel).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|static|.*\\..*|_static|_vercel).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "lKFEL7Aa-BL5CpumS-1uX", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oQU80BEb9Ysp0y5Cc6KvygFOTlbLolBab1Eeg0Ic8No=", "__NEXT_PREVIEW_MODE_ID": "39d52e1b45a4d993f11739df0364378d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b8a28d5fade8950e7692b1061a8aa49222d447e2dc466c88466db9dd987a1936", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cb84baaa6d5919f9917bb0da52c49d68ec4eedbbafc77ced96e2012aed05036a"}}}, "functions": {}, "sortedMiddleware": ["/"]}