{"version": 1, "files": ["../../node_modules/debug/package.json", "../../node_modules/debug/src/browser.js", "../../node_modules/debug/src/common.js", "../../node_modules/debug/src/index.js", "../../node_modules/debug/src/node.js", "../../node_modules/function-bind/implementation.js", "../../node_modules/function-bind/index.js", "../../node_modules/function-bind/package.json", "../../node_modules/has-flag/index.js", "../../node_modules/has-flag/package.json", "../../node_modules/hasown/index.js", "../../node_modules/hasown/package.json", "../../node_modules/import-in-the-middle/index.js", "../../node_modules/import-in-the-middle/lib/register.js", "../../node_modules/import-in-the-middle/package.json", "../../node_modules/is-core-module/core.json", "../../node_modules/is-core-module/index.js", "../../node_modules/is-core-module/package.json", "../../node_modules/module-details-from-path/index.js", "../../node_modules/module-details-from-path/package.json", "../../node_modules/ms/index.js", "../../node_modules/ms/package.json", "../../node_modules/path-parse/index.js", "../../node_modules/path-parse/package.json", "../../node_modules/require-in-the-middle/index.js", "../../node_modules/require-in-the-middle/package.json", "../../node_modules/resolve/index.js", "../../node_modules/resolve/lib/async.js", "../../node_modules/resolve/lib/caller.js", "../../node_modules/resolve/lib/core.js", "../../node_modules/resolve/lib/core.json", "../../node_modules/resolve/lib/homedir.js", "../../node_modules/resolve/lib/is-core.js", "../../node_modules/resolve/lib/node-modules-paths.js", "../../node_modules/resolve/lib/normalize-options.js", "../../node_modules/resolve/lib/sync.js", "../../node_modules/resolve/package.json", "../../node_modules/supports-color/index.js", "../../node_modules/supports-color/package.json", "../package.json", "instrumentation.js", "instrumentation.js.map", "webpack-runtime.js", "webpack-runtime.js.map"]}