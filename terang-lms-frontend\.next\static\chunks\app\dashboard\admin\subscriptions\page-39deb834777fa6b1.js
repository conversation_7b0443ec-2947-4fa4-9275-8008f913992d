try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="670392c8-a809-4ee0-b2ad-83e5c1d3e5f8",e._sentryDebugIdIdentifier="sentry-dbid-670392c8-a809-4ee0-b2ad-83e5c1d3e5f8")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2096],{12833:(e,t,a)=>{"use strict";a.d(t,{I:()=>c,SQ:()=>o,_2:()=>u,hO:()=>x,lp:()=>m,mB:()=>p,rI:()=>l,ty:()=>i});var s=a(95155);a(12115);var n=a(47971),r=a(5917),d=a(64269);function l(e){let{...t}=e;return(0,s.jsx)(n.bL,{"data-slot":"dropdown-menu",...t,"data-sentry-element":"DropdownMenuPrimitive.Root","data-sentry-component":"DropdownMenu","data-sentry-source-file":"dropdown-menu.tsx"})}function i(e){let{...t}=e;return(0,s.jsx)(n.l9,{"data-slot":"dropdown-menu-trigger",...t,"data-sentry-element":"DropdownMenuPrimitive.Trigger","data-sentry-component":"DropdownMenuTrigger","data-sentry-source-file":"dropdown-menu.tsx"})}function o(e){let{className:t,sideOffset:a=4,...r}=e;return(0,s.jsx)(n.ZL,{"data-sentry-element":"DropdownMenuPrimitive.Portal","data-sentry-component":"DropdownMenuContent","data-sentry-source-file":"dropdown-menu.tsx",children:(0,s.jsx)(n.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...r,"data-sentry-element":"DropdownMenuPrimitive.Content","data-sentry-source-file":"dropdown-menu.tsx"})})}function c(e){let{...t}=e;return(0,s.jsx)(n.YJ,{"data-slot":"dropdown-menu-group",...t,"data-sentry-element":"DropdownMenuPrimitive.Group","data-sentry-component":"DropdownMenuGroup","data-sentry-source-file":"dropdown-menu.tsx"})}function u(e){let{className:t,inset:a,variant:r="default",...l}=e;return(0,s.jsx)(n.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":r,className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground focus:[&_svg:not([class*='text-'])]:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,"data-sentry-element":"DropdownMenuPrimitive.Item","data-sentry-component":"DropdownMenuItem","data-sentry-source-file":"dropdown-menu.tsx"})}function x(e){let{className:t,children:a,checked:l,...i}=e;return(0,s.jsxs)(n.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:l,...i,"data-sentry-element":"DropdownMenuPrimitive.CheckboxItem","data-sentry-component":"DropdownMenuCheckboxItem","data-sentry-source-file":"dropdown-menu.tsx",children:[(0,s.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(n.VF,{"data-sentry-element":"DropdownMenuPrimitive.ItemIndicator","data-sentry-source-file":"dropdown-menu.tsx",children:(0,s.jsx)(r.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"dropdown-menu.tsx"})})}),a]})}function m(e){let{className:t,inset:a,...r}=e;return(0,s.jsx)(n.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,d.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...r,"data-sentry-element":"DropdownMenuPrimitive.Label","data-sentry-component":"DropdownMenuLabel","data-sentry-source-file":"dropdown-menu.tsx"})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(n.wv,{"data-slot":"dropdown-menu-separator",className:(0,d.cn)("bg-border -mx-1 my-1 h-px",t),...a,"data-sentry-element":"DropdownMenuPrimitive.Separator","data-sentry-component":"DropdownMenuSeparator","data-sentry-source-file":"dropdown-menu.tsx"})}},59191:(e,t,a)=>{Promise.resolve().then(a.bind(a,72107))},66094:(e,t,a)=>{"use strict";a.d(t,{BT:()=>i,Wu:()=>o,ZB:()=>l,Zp:()=>r,aR:()=>d,wL:()=>c});var s=a(95155);a(12115);var n=a(64269);function r(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...a,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...a,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},72107:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>N});var s=a(95155),n=a(12115),r=a(66094),d=a(20764),l=a(31936),i=a(88021),o=a(88864),c=a(12833),u=a(25532),x=a(97655),m=a(44466),p=a(6387),y=a(18720),f=a(71871),h=a(42529),g=a(78874),b=a(52056),j=a(86651),v=a(16485),w=a(37772),C=a(71163);function N(){let[e,t]=(0,n.useState)([]),[a,N]=(0,n.useState)({totalInstitutions:0,paidInstitutions:0,unpaidInstitutions:0,overdueInstitutions:0,totalStudents:0,totalTeachers:0}),[T,S]=(0,n.useState)(!0),[k,I]=(0,n.useState)(""),[_,D]=(0,n.useState)("all"),[M,P]=(0,n.useState)("all"),[H,R]=(0,n.useState)([]),[B,z]=(0,n.useState)(!1),[A,Z]=(0,n.useState)(void 0),[E,U]=(0,n.useState)(!1),L=(0,n.useRef)(null),O=(0,n.useCallback)(async()=>{try{S(!0);let e=new URLSearchParams({search:k,status:_,plan:M,limit:"100"}),a=await fetch("/api/subscriptions?".concat(e)),s=await a.json();s.success&&s.data?(t(s.data.institutions),N(s.data.summary)):y.oR.error(s.error||"Failed to fetch subscription data")}catch(e){console.error("Error fetching institutions:",e),y.oR.error("Failed to fetch subscription data")}finally{S(!1)}},[k,_,M]);(0,n.useEffect)(()=>(L.current&&clearTimeout(L.current),L.current=setTimeout(()=>{O()},300),()=>{L.current&&clearTimeout(L.current)}),[O]);let F=async e=>{if(0===H.length)return void y.oR.error("Please select institutions to update");try{z(!0);let t=await fetch("/api/subscriptions",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({institutionIds:H,paymentStatus:e,paymentDueDate:A?(0,C.GP)(A,"yyyy-MM-dd"):null})}),a=await t.json();a.success?(y.oR.success("Successfully updated ".concat(H.length," institution(s)")),R([]),Z(void 0),O()):y.oR.error(a.error||"Failed to update payment status")}catch(e){console.error("Error updating payment status:",e),y.oR.error("Failed to update payment status")}finally{z(!1)}},V=e=>{R(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},W=e=>{switch(e){case"paid":return(0,s.jsx)(i.E,{variant:"default",className:"bg-green-100 text-green-800",children:"Paid"});case"unpaid":return(0,s.jsx)(i.E,{variant:"destructive",children:"Unpaid"});default:return(0,s.jsx)(i.E,{variant:"secondary",children:e})}},G=e=>(0,s.jsx)(i.E,{variant:"outline",className:{basic:"bg-blue-100 text-blue-800",standard:"bg-purple-100 text-purple-800",premium:"bg-orange-100 text-orange-800"}[e]||"bg-gray-100 text-gray-800","data-sentry-element":"Badge","data-sentry-component":"getPlanBadge","data-sentry-source-file":"page.tsx",children:e.charAt(0).toUpperCase()+e.slice(1)}),$=e=>!!e&&new Date(e)<new Date,q=e=>{if(!e)return"No due date";let t=new Date(e),a=$(e);return(0,s.jsxs)("span",{className:a?"text-red-600 font-medium":"text-gray-600","data-sentry-component":"formatDueDate","data-sentry-source-file":"page.tsx",children:[(0,C.GP)(t,"MMM dd, yyyy"),a&&" (Overdue)"]})};return(0,s.jsxs)("div",{className:"space-y-6","data-sentry-component":"SubscriptionsPage","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"flex justify-between items-center",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Billing & Subscriptions"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Manage institution billing, payment status, and subscription details"})]})}),(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,s.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(r.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Total Institutions"}),(0,s.jsx)(f.A,{className:"h-4 w-4 text-muted-foreground","data-sentry-element":"Building","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:a.totalInstitutions}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:[a.totalStudents," students, ",a.totalTeachers," teachers"]})]})]}),(0,s.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(r.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Paid Institutions"}),(0,s.jsx)(h.A,{className:"h-4 w-4 text-green-600","data-sentry-element":"CheckCircle","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:a.paidInstitutions}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:[a.totalInstitutions>0?Math.round(a.paidInstitutions/a.totalInstitutions*100):0,"% of total"]})]})]}),(0,s.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(r.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Unpaid Institutions"}),(0,s.jsx)(g.A,{className:"h-4 w-4 text-red-600","data-sentry-element":"XCircle","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-red-600",children:a.unpaidInstitutions}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:[a.totalInstitutions>0?Math.round(a.unpaidInstitutions/a.totalInstitutions*100):0,"% of total"]})]})]}),(0,s.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(r.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Overdue Payments"}),(0,s.jsx)(b.A,{className:"h-4 w-4 text-orange-600","data-sentry-element":"AlertTriangle","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:a.overdueInstitutions}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Require immediate attention"})]})]})]}),(0,s.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(r.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(r.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Institution Billing"}),(0,s.jsx)(r.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"View and manage payment status for all institutions"})]}),(0,s.jsxs)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(j.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground","data-sentry-element":"Search","data-sentry-source-file":"page.tsx"}),(0,s.jsx)(l.p,{placeholder:"Search institutions...",value:k,onChange:e=>I(e.target.value),className:"pl-8","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(u.l6,{value:_,onValueChange:D,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(u.bq,{className:"w-[180px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(u.yv,{placeholder:"Payment Status","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,s.jsxs)(u.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(u.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"All Status"}),(0,s.jsx)(u.eb,{value:"paid","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Paid"}),(0,s.jsx)(u.eb,{value:"unpaid","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Unpaid"})]})]}),(0,s.jsxs)(u.l6,{value:M,onValueChange:P,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(u.bq,{className:"w-[180px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(u.yv,{placeholder:"Subscription Plan","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,s.jsxs)(u.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(u.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"All Plans"}),(0,s.jsx)(u.eb,{value:"basic","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Basic"}),(0,s.jsx)(u.eb,{value:"standard","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Standard"}),(0,s.jsx)(u.eb,{value:"premium","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Premium"})]})]})]}),H.length>0&&(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-4 mb-4 p-4 bg-blue-50 rounded-lg",children:[(0,s.jsxs)("span",{className:"text-sm font-medium",children:[H.length," institution(s) selected"]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(p.AM,{open:E,onOpenChange:U,children:[(0,s.jsx)(p.Wv,{asChild:!0,children:(0,s.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4"}),A?(0,C.GP)(A,"MMM dd, yyyy"):"Set Due Date"]})}),(0,s.jsx)(p.hl,{className:"w-auto p-0",children:(0,s.jsx)(m.V,{mode:"single",selected:A,onSelect:e=>{Z(e),U(!1)},initialFocus:!0})})]}),(0,s.jsx)(d.$,{size:"sm",onClick:()=>F("paid"),disabled:B,className:"bg-green-600 hover:bg-green-700",children:"Mark as Paid"}),(0,s.jsx)(d.$,{size:"sm",variant:"destructive",onClick:()=>F("unpaid"),disabled:B,children:"Mark as Unpaid"}),(0,s.jsx)(d.$,{size:"sm",variant:"outline",onClick:()=>R([]),children:"Clear Selection"})]})]}),(0,s.jsx)("div",{className:"rounded-md border",children:(0,s.jsxs)(o.Table,{"data-sentry-element":"Table","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(o.TableHeader,{"data-sentry-element":"TableHeader","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(o.TableRow,{"data-sentry-element":"TableRow","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(o.TableHead,{className:"w-12","data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(x.S,{checked:H.length===e.length&&e.length>0,onCheckedChange:()=>{H.length===e.length?R([]):R(e.map(e=>e.id))},"data-sentry-element":"Checkbox","data-sentry-source-file":"page.tsx"})}),(0,s.jsx)(o.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Institution"}),(0,s.jsx)(o.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Plan"}),(0,s.jsx)(o.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Billing Cycle"}),(0,s.jsx)(o.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Payment Status"}),(0,s.jsx)(o.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Due Date"}),(0,s.jsx)(o.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Students/Teachers"}),(0,s.jsx)(o.TableHead,{className:"w-12","data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx"})]})}),(0,s.jsx)(o.TableBody,{"data-sentry-element":"TableBody","data-sentry-source-file":"page.tsx",children:T?(0,s.jsx)(o.TableRow,{children:(0,s.jsx)(o.TableCell,{colSpan:8,className:"text-center py-8",children:"Loading institutions..."})}):0===e.length?(0,s.jsx)(o.TableRow,{children:(0,s.jsx)(o.TableCell,{colSpan:8,className:"text-center py-8",children:"No institutions found"})}):e.map(e=>(0,s.jsxs)(o.TableRow,{children:[(0,s.jsx)(o.TableCell,{children:(0,s.jsx)(x.S,{checked:H.includes(e.id),onCheckedChange:()=>V(e.id)})}),(0,s.jsx)(o.TableCell,{children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.name}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:e.type})]})}),(0,s.jsx)(o.TableCell,{children:G(e.subscription_plan)}),(0,s.jsx)(o.TableCell,{className:"capitalize",children:e.billing_cycle}),(0,s.jsx)(o.TableCell,{children:W(e.payment_status)}),(0,s.jsx)(o.TableCell,{children:q(e.payment_due_date)}),(0,s.jsx)(o.TableCell,{children:(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsxs)("div",{children:[e.actual_student_count,"/",e.student_count," students"]}),(0,s.jsxs)("div",{className:"text-muted-foreground",children:[e.actual_teacher_count,"/",e.teacher_count," teachers"]})]})}),(0,s.jsx)(o.TableCell,{children:(0,s.jsxs)(c.rI,{children:[(0,s.jsx)(c.ty,{asChild:!0,children:(0,s.jsx)(d.$,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,s.jsx)(w.A,{className:"h-4 w-4"})})}),(0,s.jsxs)(c.SQ,{align:"end",children:[(0,s.jsx)(c._2,{onClick:()=>{R([e.id]),F("paid")},className:"text-green-600",children:"Mark as Paid"}),(0,s.jsx)(c._2,{onClick:()=>{R([e.id]),F("unpaid")},className:"text-red-600",children:"Mark as Unpaid"})]})]})})]},e.id))})]})})]})]})]})}},88864:(e,t,a)=>{"use strict";a.d(t,{Table:()=>r,TableBody:()=>l,TableCell:()=>c,TableHead:()=>o,TableHeader:()=>d,TableRow:()=>i});var s=a(95155);a(12115);var n=a(64269);function r(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto","data-sentry-component":"Table","data-sentry-source-file":"table.tsx",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,n.cn)("w-full caption-bottom text-sm",t),...a})})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,n.cn)("[&_tr]:border-b",t),...a,"data-sentry-component":"TableHeader","data-sentry-source-file":"table.tsx"})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,n.cn)("[&_tr:last-child]:border-0",t),...a,"data-sentry-component":"TableBody","data-sentry-source-file":"table.tsx"})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,n.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a,"data-sentry-component":"TableRow","data-sentry-source-file":"table.tsx"})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,n.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a,"data-sentry-component":"TableHead","data-sentry-source-file":"table.tsx"})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,n.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a,"data-sentry-component":"TableCell","data-sentry-source-file":"table.tsx"})}},97655:(e,t,a)=>{"use strict";a.d(t,{S:()=>l});var s=a(95155);a(12115);var n=a(38162),r=a(5917),d=a(64269);function l(e){let{className:t,...a}=e;return(0,s.jsx)(n.bL,{"data-slot":"checkbox",className:(0,d.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,"data-sentry-element":"CheckboxPrimitive.Root","data-sentry-component":"Checkbox","data-sentry-source-file":"checkbox.tsx",children:(0,s.jsx)(n.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none","data-sentry-element":"CheckboxPrimitive.Indicator","data-sentry-source-file":"checkbox.tsx",children:(0,s.jsx)(r.A,{className:"size-3.5","data-sentry-element":"CheckIcon","data-sentry-source-file":"checkbox.tsx"})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[5105,4909,4736,660,8720,6093,9568,7971,5439,301,3500,4850,8441,3840,7358],()=>t(59191)),_N_E=e.O()}]);