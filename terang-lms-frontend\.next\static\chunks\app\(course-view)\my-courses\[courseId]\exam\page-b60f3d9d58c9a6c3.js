try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},s=(new e.Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="fc8b8c7f-3c17-49c8-a7ed-c7ce6a3045cd",e._sentryDebugIdIdentifier="sentry-dbid-fc8b8c7f-3c17-49c8-a7ed-c7ce6a3045cd")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7190],{48383:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N});var t=a(95155),i=a(12115),n=a(20063),l=a(20764),r=a(26737),c=a(66094),m=a(90800),d=a(33513),x=a(15215),o=a(11380),u=a(67965),h=a(86032),f=a(49408),j=a(45097),g=a(49716),p=a(9005),b=a(25647);let N=()=>{let e=(0,n.useParams)(),s=(0,n.useRouter)(),a=(0,n.useSearchParams)(),N=e.courseId,y=a.get("type")||"final",w=a.get("examId"),{courseData:v,updateCourseProgress:S}=(0,p.q)(),[k,A]=(0,i.useState)(0),[q,z]=(0,i.useState)({}),[E,I]=(0,i.useState)(null),[P,M]=(0,i.useState)(!1),[L,C]=(0,i.useState)(!1),[_,Q]=(0,i.useState)(new Set),[T,$]=(0,i.useState)(!1),[D,K]=(0,i.useState)(null),[B,O]=(0,i.useState)(!1),[R,U]=(0,i.useState)(!0),[Z,J]=(0,i.useState)({fifteenMin:!1,fiveMin:!1,oneMin:!1}),[W,F]=(0,i.useState)({show:!1,message:"",type:"warning"}),G=(()=>{if("final"===y)return v.finalExam;for(let e of v.modules){if(e.moduleQuiz.id===w)return e.moduleQuiz;for(let s of e.chapters)if(s.quiz.id===w)return s.quiz}return null})();(0,i.useEffect)(()=>{B&&(null==G?void 0:G.timeLimit)&&null===E&&I(60*G.timeLimit)},[B,G,E]),(0,i.useEffect)(()=>{if(B&&null!==E&&E>0&&!T){let e=setInterval(()=>{I(e=>{if(null===e||e<=1)return Y(),0;let s=e-1;return 900!==s||Z.fifteenMin||(J(e=>({...e,fifteenMin:!0})),F({show:!0,message:"Peringatan: Sisa waktu 15 menit!",type:"warning"}),setTimeout(()=>F(e=>({...e,show:!1})),5e3)),300!==s||Z.fiveMin||(J(e=>({...e,fiveMin:!0})),F({show:!0,message:"Peringatan: Sisa waktu 5 menit!",type:"warning"}),setTimeout(()=>F(e=>({...e,show:!1})),5e3)),60!==s||Z.oneMin||(J(e=>({...e,oneMin:!0})),F({show:!0,message:"PERINGATAN KRITIS: Sisa waktu 1 menit!",type:"critical"}),setTimeout(()=>F(e=>({...e,show:!1})),8e3)),s})},1e3);return()=>clearInterval(e)}},[B,E,T,Z]),(0,i.useEffect)(()=>{if(B&&!T){let e=e=>(e.preventDefault(),e.returnValue="","");return window.addEventListener("beforeunload",e),()=>window.removeEventListener("beforeunload",e)}},[B,T]);let H=e=>{if(!(null==G?void 0:G.timeLimit))return"normal";let s=e/(60*G.timeLimit);return s<=.1?"critical":s<=.25?"warning":"normal"},V=e=>{Q(s=>{let a=new Set(s);return a.has(e)?a.delete(e):a.add(e),a})},Y=()=>{X()},X=(0,i.useCallback)(()=>{if(P||!G)return;M(!0),C(!1);let e=0,a={};G.questions.forEach(s=>{let t=q[s.id]===s.correctAnswer;a[s.id]=t,t&&e++});let t=Math.round(e/G.questions.length*100);sessionStorage.setItem("exam_answers_".concat(w||"final"),JSON.stringify(q)),sessionStorage.setItem("exam_results_".concat(w||"final"),JSON.stringify(a)),sessionStorage.setItem("exam_flags_".concat(w||"final"),JSON.stringify(Array.from(_))),K({score:t,correctAnswers:e,totalQuestions:G.questions.length,results:a}),ee(t);let i="/my-courses/".concat(N,"/exam/results?type=").concat(y,"&examId=").concat(w||"final","&score=").concat(t,"&correct=").concat(e,"&total=").concat(G.questions.length);s.push(i)},[P,G,q]),ee=e=>{if(!G)return;let s=JSON.parse(JSON.stringify(v));if("final"===y)s.finalExam.attempts+=1,s.finalExam.lastScore=e,s.finalExam.isPassed=e>=s.finalExam.minimumScore,s.finalExam.isPassed&&s.modules.every(e=>e.chapters.every(e=>e.contents.every(e=>e.isCompleted)&&e.quiz.isPassed)&&e.moduleQuiz.isPassed)&&(s.certificate.isEligible=!0,s.certificate.completionDate=new Date().toISOString().split("T")[0],s.status="completed");else for(let a of s.modules){if(a.moduleQuiz.id===w){a.moduleQuiz.attempts+=1,a.moduleQuiz.lastScore=e,a.moduleQuiz.isPassed=e>=a.moduleQuiz.minimumScore;break}for(let s of a.chapters)if(s.quiz.id===w){s.quiz.attempts+=1,s.quiz.lastScore=e,s.quiz.isPassed=e>=s.quiz.minimumScore;break}}S(s)},es=()=>{s.push("/my-courses/".concat(N))};if(!G)return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsx)(c.Zp,{className:"w-full max-w-md",children:(0,t.jsxs)(c.Wu,{className:"p-6 text-center",children:[(0,t.jsx)(m.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Ujian Tidak Ditemukan"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Ujian yang diminta tidak dapat ditemukan."}),(0,t.jsxs)(l.$,{onClick:es,children:[(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Kembali ke Kursus"]})]})})});let ea=(()=>{let e=new Set;return null==G||G.questions.forEach((s,a)=>{void 0!==q[s.id]&&""!==q[s.id]&&e.add(a)}),e})(),et=ea.size/G.questions.length*100,ei=ea.size>0;return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50","data-sentry-component":"ExamPage","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)("div",{className:"bg-white border-b shadow-sm sticky top-0 z-10",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(l.$,{variant:"outline",size:"sm",onClick:es,className:"flex items-center space-x-2","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)(d.A,{className:"h-4 w-4","data-sentry-element":"ArrowLeftIcon","data-sentry-source-file":"page.tsx"}),(0,t.jsx)("span",{children:"Kembali"})]}),(0,t.jsx)(x.A,{className:"h-6 w-6 text-[var(--iai-primary)]","data-sentry-element":"TrophyIcon","data-sentry-source-file":"page.tsx"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:G.title}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:v.name})]})]}),B&&null!==E&&!T&&(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(null==G?void 0:G.timeLimit)&&(0,t.jsxs)("div",{className:"hidden sm:flex flex-col items-end min-w-[120px]",children:[(0,t.jsx)("div",{className:"text-xs text-gray-500 mb-1",children:"Sisa Waktu"}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-1000 ".concat("critical"===H(E)?"bg-red-500":"warning"===H(E)?"bg-amber-500":"bg-blue-500"),style:{width:"".concat(E/(60*G.timeLimit)*100,"%")}})})]}),(0,t.jsxs)("div",{className:"\n                  flex items-center gap-2 px-4 py-2 rounded-lg border-2 font-mono text-lg font-bold transition-all duration-300\n                  ".concat((e=>{switch(e){case"critical":return"border-red-500 text-red-600 bg-red-50";case"warning":return"border-amber-500 text-amber-600 bg-amber-50";default:return"border-blue-500 text-blue-600 bg-blue-50"}})(H(E)),"\n                  ").concat("critical"===H(E)?"animate-pulse shadow-lg":"","\n                "),children:[(0,t.jsx)(o.A,{className:"h-5 w-5"}),(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)("span",{className:"leading-tight",children:(e=>{let s=Math.floor(e/3600),a=Math.floor(e%3600/60),t=e%60;return s>0?"".concat(s.toString().padStart(2,"0"),":").concat(a.toString().padStart(2,"0"),":").concat(t.toString().padStart(2,"0")):"".concat(a.toString().padStart(2,"0"),":").concat(t.toString().padStart(2,"0"))})(E)}),(null==G?void 0:G.timeLimit)&&(0,t.jsx)("span",{className:"text-xs opacity-75 leading-tight",children:"critical"===H(E)?"SEGERA HABIS!":"warning"===H(E)?"Perhatian":"Tersisa"})]})]})]}),!B||T?(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(l.$,{variant:"outline",onClick:es,children:[(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Kembali ke Kursus"]}),(0,t.jsxs)(l.$,{variant:"outline",onClick:()=>{s.push("/my-courses")},children:[(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Dashboard"]})]}):null]})})}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[R&&(0,t.jsxs)(c.Zp,{className:"max-w-2xl mx-auto",children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{className:"text-center",children:"Instruksi Ujian"})}),(0,t.jsxs)(c.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-medium text-blue-600",children:"1"})}),(0,t.jsx)("p",{className:"text-gray-700",children:"Pastikan koneksi internet Anda stabil"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-medium text-blue-600",children:"2"})}),(0,t.jsxs)("p",{className:"text-gray-700",children:["Anda memiliki waktu ",G.timeLimit?"".concat(G.timeLimit," menit"):"tidak terbatas"," untuk menyelesaikan ujian"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-medium text-blue-600",children:"3"})}),(0,t.jsxs)("p",{className:"text-gray-700",children:["Nilai minimum untuk lulus: ",G.minimumScore,"%"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-medium text-blue-600",children:"4"})}),(0,t.jsxs)("p",{className:"text-gray-700",children:["Total soal: ",G.questions.length]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-medium text-blue-600",children:"5"})}),(0,t.jsxs)("p",{className:"text-gray-700",children:["Maksimal percobaan: ",G.maxAttempts]})]})]}),(0,t.jsxs)("div",{className:"bg-amber-50 border border-amber-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 text-amber-600"}),(0,t.jsx)("p",{className:"font-medium text-amber-800",children:"Peringatan"})]}),(0,t.jsx)("p",{className:"text-amber-700 text-sm mt-2",children:"Setelah ujian dimulai, jangan menutup browser atau meninggalkan halaman. Ujian akan otomatis diserahkan jika waktu habis."})]}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)(l.$,{size:"lg",variant:"iai",onClick:()=>{O(!0),U(!1),(null==G?void 0:G.timeLimit)&&I(60*G.timeLimit)},children:[(0,t.jsx)(x.A,{className:"mr-2 h-5 w-5"}),"Mulai Ujian"]})})]})]}),T&&D&&(0,t.jsx)("div",{className:"max-w-2xl mx-auto space-y-6",children:(0,t.jsxs)(c.Zp,{className:"border-2 ".concat(D.score>=G.minimumScore?"border-green-200 bg-green-50":"border-red-200 bg-red-50"),children:[(0,t.jsxs)(c.aR,{className:"text-center",children:[(0,t.jsx)("div",{className:"flex justify-center mb-4",children:D.score>=G.minimumScore?(0,t.jsx)(f.A,{className:"h-16 w-16 text-green-600"}):(0,t.jsx)(m.A,{className:"h-16 w-16 text-red-600"})}),(0,t.jsx)(c.ZB,{className:"text-2xl",children:D.score>=G.minimumScore?"Selamat! Anda Lulus":"Maaf, Anda Belum Lulus"})]}),(0,t.jsxs)(c.Wu,{className:"text-center space-y-4",children:[(0,t.jsxs)("div",{className:"text-4xl font-bold text-gray-900",children:[D.score,"%"]}),(0,t.jsxs)("div",{className:"text-gray-600",children:[D.correctAnswers," dari ",D.totalQuestions," soal dijawab benar"]}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["Nilai minimum untuk lulus: ",G.minimumScore,"%"]}),(0,t.jsxs)("div",{className:"flex justify-center space-x-4 mt-6",children:[D.score<G.minimumScore&&G.attempts<G.maxAttempts&&(0,t.jsxs)(l.$,{onClick:()=>{A(0),z({}),Q(new Set),$(!1),K(null),M(!1),C(!1),O(!0),(null==G?void 0:G.timeLimit)&&I(60*G.timeLimit)},children:[(0,t.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Ulangi Ujian"]}),(0,t.jsxs)(l.$,{variant:"outline",onClick:es,children:[(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Kembali ke Kursus"]})]})]})]})}),B&&!T&&(0,t.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-8",children:[(0,t.jsxs)("div",{className:"xl:col-span-3 space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,t.jsxs)("span",{children:["Soal ",k+1," dari ",G.questions.length]}),(0,t.jsxs)("span",{children:[Math.round(et),"% Selesai"]})]}),(0,t.jsx)(r.k,{value:et,className:"h-3"})]}),(0,t.jsx)(b.vN,{question:G.questions[k],questionNumber:k+1,totalQuestions:G.questions.length,selectedAnswer:q[G.questions[k].id],onAnswerChange:(e,s)=>{z(a=>({...a,[e]:s}))},disabled:P}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)(l.$,{variant:"outline",onClick:()=>A(e=>Math.max(0,e-1)),disabled:0===k||P,children:"Previous"}),(0,t.jsx)("div",{className:"flex items-center space-x-3",children:(0,t.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>V(k),disabled:P,className:_.has(k)?"bg-yellow-100 border-yellow-400":"",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2 ".concat(_.has(k)?"text-yellow-600":"")}),"Flag"]})}),(0,t.jsx)(l.$,{variant:"iai",onClick:()=>A(e=>Math.min(G.questions.length-1,e+1)),disabled:k===G.questions.length-1||P,children:"Next"})]})]}),(0,t.jsx)("div",{className:"xl:col-span-1",children:(0,t.jsx)(b.b2,{questions:G.questions,currentQuestion:k,answeredQuestions:ea,onQuestionSelect:e=>{A(e)},flaggedQuestions:_,onToggleFlag:V,onSubmit:()=>{C(!0)},canSubmit:ei,isSubmitting:P})})]})]}),L&&(0,t.jsx)("div",{className:"fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50",children:(0,t.jsxs)(c.Zp,{className:"w-full max-w-md mx-4 shadow-2xl border-2",children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)(c.ZB,{className:"flex items-center space-x-2 text-amber-700",children:[(0,t.jsx)(h.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Konfirmasi Penyerahan"})]})}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-gray-600",children:"Apakah Anda yakin ingin menyerahkan ujian? Pastikan semua jawaban sudah benar."}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:ea.size}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:"Terjawab"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-gray-400",children:G.questions.length-ea.size}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:"Belum"})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(l.$,{variant:"outline",className:"flex-1",onClick:()=>C(!1),children:"Batal"}),(0,t.jsx)(l.$,{variant:"iai",className:"flex-1",onClick:X,disabled:P,children:P?"Menyerahkan...":"Ya, Serahkan"})]})]})]})}),W.show&&(0,t.jsx)("div",{className:"fixed top-4 right-4 z-50 animate-in slide-in-from-top-2",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3 rounded-lg px-6 py-4 shadow-lg border-2 ".concat("critical"===W.type?"bg-red-50 text-red-800 border-red-200":"bg-amber-50 text-amber-800 border-amber-200"," min-w-[320px]"),children:[(0,t.jsx)("div",{className:"flex-shrink-0 ".concat("critical"===W.type?"animate-pulse":""),children:(0,t.jsx)(o.A,{className:"h-6 w-6 ".concat("critical"===W.type?"text-red-600":"text-amber-600")})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-semibold text-sm",children:W.message}),(0,t.jsx)("p",{className:"text-xs opacity-75 mt-1",children:"critical"===W.type?"Segera serahkan ujian Anda!":"Pastikan untuk menyerahkan ujian tepat waktu."})]})]})})]})}},77730:(e,s,a)=>{Promise.resolve().then(a.bind(a,48383))}},e=>{var s=s=>e(e.s=s);e.O(0,[4909,904,5542,5467,4850,8441,3840,7358],()=>s(77730)),_N_E=e.O()}]);