try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="2ad96ed3-843b-4329-9be5-8814b08bacc1",e._sentryDebugIdIdentifier="sentry-dbid-2ad96ed3-843b-4329-9be5-8814b08bacc1")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5521],{861:(e,t,r)=>{r.d(t,{Qg:()=>l,bL:()=>s});var n=r(12115),o=r(97602),i=r(95155),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=n.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{...l,...e.style}}));a.displayName="VisuallyHidden";var s=a},47520:(e,t,r)=>{r.d(t,{Kq:()=>q,UC:()=>Y,ZL:()=>X,bL:()=>G,i3:()=>Z,l9:()=>U});var n=r(12115),o=r(92556),i=r(94446),l=r(3468),a=r(44831),s=r(68946),u=r(66093),c=r(75433),d=r(76842),p=r(97602),f=r(32467),h=r(23558),y=r(861),g=r(95155),[x,v]=(0,l.A)("Tooltip",[u.Bk]),b=(0,u.Bk)(),w="TooltipProvider",m="tooltip.open",[C,k]=x(w),T=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,a=n.useRef(!0),s=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,g.jsx)(C,{scope:t,isOpenDelayedRef:a,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:i,children:l})};T.displayName=w;var E="Tooltip",[L,j]=x(E),R=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:i,onOpenChange:l,disableHoverableContent:a,delayDuration:c}=e,d=k(E,e.__scopeTooltip),p=b(t),[f,y]=n.useState(null),x=(0,s.B)(),v=n.useRef(0),w=null!=a?a:d.disableHoverableContent,C=null!=c?c:d.delayDuration,T=n.useRef(!1),[j,R]=(0,h.i)({prop:o,defaultProp:null!=i&&i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(m))):d.onClose(),null==l||l(e)},caller:E}),_=n.useMemo(()=>j?T.current?"delayed-open":"instant-open":"closed",[j]),M=n.useCallback(()=>{window.clearTimeout(v.current),v.current=0,T.current=!1,R(!0)},[R]),D=n.useCallback(()=>{window.clearTimeout(v.current),v.current=0,R(!1)},[R]),I=n.useCallback(()=>{window.clearTimeout(v.current),v.current=window.setTimeout(()=>{T.current=!0,R(!0),v.current=0},C)},[C,R]);return n.useEffect(()=>()=>{v.current&&(window.clearTimeout(v.current),v.current=0)},[]),(0,g.jsx)(u.bL,{...p,children:(0,g.jsx)(L,{scope:t,contentId:x,open:j,stateAttribute:_,trigger:f,onTriggerChange:y,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayedRef.current?I():M()},[d.isOpenDelayedRef,I,M]),onTriggerLeave:n.useCallback(()=>{w?D():(window.clearTimeout(v.current),v.current=0)},[D,w]),onOpen:M,onClose:D,disableHoverableContent:w,children:r})})};R.displayName=E;var _="TooltipTrigger",M=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...l}=e,a=j(_,r),s=k(_,r),c=b(r),d=n.useRef(null),f=(0,i.s)(t,d,a.onTriggerChange),h=n.useRef(!1),y=n.useRef(!1),x=n.useCallback(()=>h.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",x),[x]),(0,g.jsx)(u.Mz,{asChild:!0,...c,children:(0,g.jsx)(p.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(y.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),y.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),y.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),h.current=!0,document.addEventListener("pointerup",x,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{h.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});M.displayName=_;var D="TooltipPortal",[I,N]=x(D,{forceMount:void 0}),P=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,i=j(D,t);return(0,g.jsx)(I,{scope:t,forceMount:r,children:(0,g.jsx)(d.C,{present:r||i.open,children:(0,g.jsx)(c.Z,{asChild:!0,container:o,children:n})})})};P.displayName=D;var A="TooltipContent",O=n.forwardRef((e,t)=>{let r=N(A,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...i}=e,l=j(A,e.__scopeTooltip);return(0,g.jsx)(d.C,{present:n||l.open,children:l.disableHoverableContent?(0,g.jsx)(F,{side:o,...i,ref:t}):(0,g.jsx)(B,{side:o,...i,ref:t})})}),B=n.forwardRef((e,t)=>{let r=j(A,e.__scopeTooltip),o=k(A,e.__scopeTooltip),l=n.useRef(null),a=(0,i.s)(t,l),[s,u]=n.useState(null),{trigger:c,onClose:d}=r,p=l.current,{onPointerInTransitChange:f}=o,h=n.useCallback(()=>{u(null),f(!1)},[f]),y=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(r,n,o,i)){case i:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),f(!0)},[f]);return n.useEffect(()=>()=>h(),[h]),n.useEffect(()=>{if(c&&p){let e=e=>y(e,p),t=e=>y(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,y,h]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==c?void 0:c.contains(t))||(null==p?void 0:p.contains(t)),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],s=l.x,u=l.y,c=a.x,d=a.y;u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s&&(o=!o)}return o}(r,s);n?h():o&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,s,d,h]),(0,g.jsx)(F,{...e,ref:a})}),[W,z]=x(E,{isInside:!1}),H=(0,f.Dc)("TooltipContent"),F=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:s,...c}=e,d=j(A,r),p=b(r),{onClose:f}=d;return n.useEffect(()=>(document.addEventListener(m,f),()=>document.removeEventListener(m,f)),[f]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,f]),(0,g.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,g.jsxs)(u.UC,{"data-state":d.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,g.jsx)(H,{children:o}),(0,g.jsx)(W,{scope:r,isInside:!0,children:(0,g.jsx)(y.bL,{id:d.contentId,role:"tooltip",children:i||o})})]})})});O.displayName=A;var S="TooltipArrow",V=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=b(r);return z(S,r).isInside?null:(0,g.jsx)(u.i3,{...o,...n,ref:t})});V.displayName=S;var q=T,G=R,U=M,X=P,Y=O,Z=V},80534:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(71847).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},96063:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(12115),o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round"};let i=(e,t)=>{let r=(0,n.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:i=1.5,className:l="",children:a,...s},u)=>{let c={ref:u,...o,width:r,height:r,strokeWidth:i,color:e,className:l,...s};return(0,n.createElement)("svg",c,t?.map(([e,t])=>(0,n.createElement)(e,{key:t.id,...t}))??[],...Array.isArray(a)?a:[a])});return r.displayName=`${e}Icon`,r}}}]);