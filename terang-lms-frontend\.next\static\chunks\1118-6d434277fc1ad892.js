try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="4bb5e0b2-bc3f-4b88-9266-2564a638e9c9",e._sentryDebugIdIdentifier="sentry-dbid-4bb5e0b2-bc3f-4b88-9266-2564a638e9c9")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1118],{2030:(e,t,a)=>{a.d(t,{Cn:()=>s,iI:()=>r});let r=e=>[{title:"Available Courses",url:"/courses",icon:"searchList",isActive:e.startsWith("/courses"),shortcut:["a","c"],items:[]},{title:"My Courses",url:"/my-courses",icon:"graduationCap",isActive:e.startsWith("/my-courses"),shortcut:["m","c"],items:[]}],s=r("")},7648:(e,t,a)=>{a.d(t,{default:()=>eh});var r=a(95155),s=a(57259);function n(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"collapsible",...t,"data-sentry-element":"CollapsiblePrimitive.Root","data-sentry-component":"Collapsible","data-sentry-source-file":"collapsible.tsx"})}function i(e){let{...t}=e;return(0,r.jsx)(s.R6,{"data-slot":"collapsible-trigger",...t,"data-sentry-element":"CollapsiblePrimitive.CollapsibleTrigger","data-sentry-component":"CollapsibleTrigger","data-sentry-source-file":"collapsible.tsx"})}function o(e){let{...t}=e;return(0,r.jsx)(s.Ke,{"data-slot":"collapsible-content",...t,"data-sentry-element":"CollapsiblePrimitive.CollapsibleContent","data-sentry-component":"CollapsibleContent","data-sentry-source-file":"collapsible.tsx"})}var d=a(12833),l=a(28370),c=a(85188);let u=[{title:"Dashboard",url:"/dashboard/admin",icon:"dashboard",isActive:!1,shortcut:["d","d"],items:[]},{title:"Institutions",url:"/dashboard/admin/institutions",icon:"building",isActive:!1,shortcut:["i","i"],items:[{title:"All Institutions",url:"/dashboard/admin/institutions",icon:"building"},{title:"Add Institution",url:"/dashboard/admin/institutions/new",icon:"plus"}]},{title:"Users",url:"/dashboard/admin/users",icon:"users",isActive:!1,shortcut:["u","u"],items:[{title:"All Users",url:"/dashboard/admin/users",icon:"users"},{title:"Add User",url:"/dashboard/admin/users/new",icon:"userPlus"}]},{title:"Subscriptions",url:"/dashboard/admin/subscriptions",icon:"creditCard",isActive:!1,shortcut:["s","s"],items:[]},{title:"Analytics",url:"/dashboard/admin/analytics",icon:"barChart",isActive:!1,shortcut:["a","a"],items:[]},{title:"Account",url:"#",icon:"billing",isActive:!1,items:[{title:"Profile",url:"/dashboard/profile",icon:"userPen",shortcut:["p","p"]},{title:"Settings",url:"/dashboard/settings",icon:"settings"}]}],m=[{title:"Dashboard",url:"/dashboard/teacher",icon:"dashboard",isActive:!1,shortcut:["d","d"],items:[]},{title:"Courses",url:"/dashboard/teacher/courses",icon:"bookOpen",isActive:!1,shortcut:["o","o"],items:[{title:"My Courses",url:"/dashboard/teacher/courses",icon:"bookOpen"},{title:"Create Course",url:"/dashboard/teacher/courses/new",icon:"plus"}]}],p=[{title:"Dashboard",url:"/dashboard/student",icon:"dashboard",isActive:!1,shortcut:["d","d"],items:[]},{title:"My Courses",url:"/dashboard/student/courses",icon:"bookOpen",isActive:!1,shortcut:["c","c"],items:[]},{title:"Progress",url:"/dashboard/student/progress",icon:"trendingUp",isActive:!1,shortcut:["p","p"],items:[]},{title:"Certificates",url:"/dashboard/student/certificates",icon:"award",isActive:!1,shortcut:["e","e"],items:[]},{title:"Account",url:"#",icon:"billing",isActive:!1,items:[{title:"Profile",url:"/dashboard/profile",icon:"userPen",shortcut:["r","r"]},{title:"Settings",url:"/dashboard/settings",icon:"settings"}]}],f=e=>{switch(e){case"super_admin":return u;case"teacher":return m;case"student":return p;default:return[]}};var x=a(47886),h=a(2030),b=a(12115),g=a(19408),y=a(27937),v=a(84926),w=a(52619),j=a.n(w),A=a(15239),N=a(20063),S=a(59384),C=a(50700),k=a(59865),M=a(65229),I=a(60952),D=a(35299),P=a(59e3),z=a(90368),_=a(71360),T=a(80501),B=a(89715),L=a(92001),U=a(89470),R=a(50069),F=a(42196),O=a(15870),K=a(78519),G=a(37772),E=a(6191),q=a(52056),X=a(20508),Z=a(52987),H=a(80021),$=a(96047),V=a(59427),J=a(37494),Q=a(40887),W=a(94684),Y=a(97378),ee=a(5917),et=a(91169),ea=a(96583),er=a(12181),es=a(80534),en=a(47937),ei=a(28446),eo=a(78192),ed=a(1524),el=a(52472),ec=a(7202),eu=a(4449);let em={dashboard:S.A,logo:C.A,login:k.A,close:M.A,product:I.A,spinner:D.A,kanban:P.A,chevronLeft:z.A,chevronRight:y.A,trash:_.A,employee:T.A,post:B.A,page:L.A,userPen:U.A,user2:R.A,media:F.A,settings:O.A,billing:K.A,ellipsis:G.A,add:E.A,warning:q.A,user:X.A,arrowRight:Z.A,help:H.A,pizza:$.A,sun:V.A,moon:J.A,laptop:Q.A,github:W.A,twitter:Y.A,check:ee.A,building:g.A,users:et.A,userPlus:ea.A,userCheck:er.A,creditCard:K.A,barChart:es.A,bookOpen:en.A,book:ei.A,bot:eo.A,trendingUp:ed.A,award:el.A,plus:E.A,school:en.A,certificate:el.A,enrollment:ea.A,searchList:ec.A,graduationCap:eu.A};var ep=a(89442);let ef=e=>{switch(e){case"teacher":return{label:"Teacher",icon:ep.A,bgColor:"bg-blue-600"};case"student":return{label:"Student",icon:en.A,bgColor:"bg-green-600"};case"super_admin":return{label:"Institution Manager",icon:g.A,bgColor:"bg-purple-600"};default:return{label:"User",icon:en.A,bgColor:"bg-gray-600"}}};function ex(){let[e,t]=b.useState(x.qs.getUser());if(b.useEffect(()=>{t(x.qs.getUser())},[]),!e)return null;let a=ef(e.role),s=a.icon;return(0,r.jsx)(l.wZ,{"data-sentry-element":"SidebarMenu","data-sentry-component":"RoleIndicator","data-sentry-source-file":"role-indicator.tsx",children:(0,r.jsx)(l.FX,{"data-sentry-element":"SidebarMenuItem","data-sentry-source-file":"role-indicator.tsx",children:(0,r.jsxs)(l.Uj,{size:"lg",className:"cursor-default hover:bg-transparent","data-sentry-element":"SidebarMenuButton","data-sentry-source-file":"role-indicator.tsx",children:[(0,r.jsx)("div",{className:"".concat(a.bgColor," text-white flex aspect-square size-8 items-center justify-center rounded-lg"),children:(0,r.jsx)(s,{className:"size-4","data-sentry-element":"Icon","data-sentry-source-file":"role-indicator.tsx"})}),(0,r.jsxs)("div",{className:"flex flex-col gap-0.5 leading-none",children:[(0,r.jsx)("span",{className:"font-semibold",children:e.name}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Account Type: ",a.label]})]})]})})})}function eh(){let e=(0,N.usePathname)(),{isOpen:t}=function(){let[e,t]=(0,b.useState)(!1);return(0,b.useEffect)(()=>{let e=window.matchMedia("(max-width: 768px)");t(e.matches);let a=e=>{t(e.matches)};return e.addEventListener("change",a),()=>e.removeEventListener("change",a)},[]),{isOpen:e}}(),a=(0,N.useRouter)(),{state:s}=(0,l.cL)(),[u,m]=b.useState([]);b.useEffect(()=>{let t=x.qs.getUser();t?"student"===t.role?m((0,h.iI)(e)):m(f(t.role)):m((0,h.iI)(e))},[e]);let p=u.length>0?u:(0,h.iI)(e);return(0,r.jsxs)(l.Bx,{collapsible:"icon","data-sentry-element":"Sidebar","data-sentry-component":"AppSidebar","data-sentry-source-file":"app-sidebar.tsx",children:[(0,r.jsxs)(l.Gh,{className:"p-4","data-sentry-element":"SidebarHeader","data-sentry-source-file":"app-sidebar.tsx",children:[(0,r.jsx)("div",{className:"flex items-center justify-center",children:(0,r.jsx)(j(),{href:"/","data-sentry-element":"Link","data-sentry-source-file":"app-sidebar.tsx",children:(0,r.jsx)(A.default,{src:"/assets/logo-iai.png",alt:"IAI Logo",width:"collapsed"===s?48:160,height:"collapsed"===s?48:60,className:"object-contain transition-all duration-200 cursor-pointer hover:opacity-80",priority:!0,"data-sentry-element":"Image","data-sentry-source-file":"app-sidebar.tsx"})})}),(0,r.jsx)("div",{className:"mt-3 ".concat("collapsed"===s?"hidden":"block"),children:(0,r.jsx)(ex,{"data-sentry-element":"RoleIndicator","data-sentry-source-file":"app-sidebar.tsx"})})]}),(0,r.jsx)(l.Yv,{className:"overflow-x-hidden","data-sentry-element":"SidebarContent","data-sentry-source-file":"app-sidebar.tsx",children:(0,r.jsxs)(l.Cn,{"data-sentry-element":"SidebarGroup","data-sentry-source-file":"app-sidebar.tsx",children:[(0,r.jsx)(l.jj,{"data-sentry-element":"SidebarGroupLabel","data-sentry-source-file":"app-sidebar.tsx",children:"Overview"}),(0,r.jsx)(l.wZ,{className:"gap-0","data-sentry-element":"SidebarMenu","data-sentry-source-file":"app-sidebar.tsx",children:p.map(t=>{var a,s,d;let c=t.icon?em[t.icon]:em.logo;return(null==t?void 0:t.items)&&(null==t||null==(a=t.items)?void 0:a.length)>0?(0,r.jsx)(n,{asChild:!0,defaultOpen:t.isActive,className:"group/collapsible",children:(0,r.jsxs)(l.FX,{children:[(0,r.jsx)(i,{asChild:!0,children:(0,r.jsxs)(l.Uj,{tooltip:t.title,isActive:null==(s=t.items)?void 0:s.some(t=>e===t.url),className:"py-3 px-4 text-base font-medium h-auto",children:[t.icon&&(0,r.jsx)(c,{}),(0,r.jsx)("span",{children:t.title}),(0,r.jsx)(y.A,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),(0,r.jsx)(o,{children:(0,r.jsx)(l.q9,{children:null==(d=t.items)?void 0:d.map(t=>(0,r.jsx)(l.Fg,{children:(0,r.jsx)(l.Cp,{asChild:!0,isActive:e===t.url,className:"py-2 px-6 text-sm font-medium",children:(0,r.jsx)(j(),{href:t.url,children:(0,r.jsx)("span",{children:t.title})})})},t.title))})})]})},t.title):(0,r.jsx)(l.FX,{children:(0,r.jsx)(l.Uj,{asChild:!0,tooltip:t.title,isActive:e===t.url,className:"py-3 px-4 text-base font-medium h-auto",children:(0,r.jsxs)(j(),{href:t.url,children:[(0,r.jsx)(c,{}),(0,r.jsx)("span",{children:t.title})]})})},t.title)})})]})}),(0,r.jsx)(l.CG,{"data-sentry-element":"SidebarFooter","data-sentry-source-file":"app-sidebar.tsx",children:(0,r.jsx)(l.wZ,{"data-sentry-element":"SidebarMenu","data-sentry-source-file":"app-sidebar.tsx",children:(0,r.jsx)(l.FX,{"data-sentry-element":"SidebarMenuItem","data-sentry-source-file":"app-sidebar.tsx",children:(0,r.jsxs)(d.rI,{"data-sentry-element":"DropdownMenu","data-sentry-source-file":"app-sidebar.tsx",children:[(0,r.jsx)(d.ty,{asChild:!0,"data-sentry-element":"DropdownMenuTrigger","data-sentry-source-file":"app-sidebar.tsx",children:(0,r.jsxs)(l.Uj,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground py-3 px-4 text-base font-medium h-auto","data-sentry-element":"SidebarMenuButton","data-sentry-source-file":"app-sidebar.tsx",children:[(0,r.jsx)(c.M,{className:"h-8 w-8 rounded-lg",showInfo:!0,"data-sentry-element":"UserAvatarProfile","data-sentry-source-file":"app-sidebar.tsx"}),(0,r.jsx)(em.chevronRight,{className:"ml-auto size-4","data-sentry-element":"Icons.chevronRight","data-sentry-source-file":"app-sidebar.tsx"})]})}),(0,r.jsxs)(d.SQ,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:"bottom",align:"end",sideOffset:4,"data-sentry-element":"DropdownMenuContent","data-sentry-source-file":"app-sidebar.tsx",children:[(0,r.jsx)(d.lp,{className:"p-0 font-normal","data-sentry-element":"DropdownMenuLabel","data-sentry-source-file":"app-sidebar.tsx",children:(0,r.jsx)("div",{className:"px-1 py-1.5",children:(0,r.jsx)(c.M,{className:"h-8 w-8 rounded-lg",showInfo:!0,"data-sentry-element":"UserAvatarProfile","data-sentry-source-file":"app-sidebar.tsx"})})}),(0,r.jsx)(d.mB,{"data-sentry-element":"DropdownMenuSeparator","data-sentry-source-file":"app-sidebar.tsx"}),(0,r.jsxs)(d.I,{"data-sentry-element":"DropdownMenuGroup","data-sentry-source-file":"app-sidebar.tsx",children:[(0,r.jsxs)(d._2,{onClick:()=>a.push("/dashboard/profile"),"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"app-sidebar.tsx",children:[(0,r.jsx)(em.user,{className:"mr-2 h-4 w-4","data-sentry-element":"Icons.user","data-sentry-source-file":"app-sidebar.tsx"}),"Profile"]}),(0,r.jsxs)(d._2,{"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"app-sidebar.tsx",children:[(0,r.jsx)(em.billing,{className:"mr-2 h-4 w-4","data-sentry-element":"Icons.billing","data-sentry-source-file":"app-sidebar.tsx"}),"Billing"]}),(0,r.jsxs)(d._2,{"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"app-sidebar.tsx",children:[(0,r.jsx)(em.help,{className:"mr-2 h-4 w-4","data-sentry-element":"Icons.help","data-sentry-source-file":"app-sidebar.tsx"}),"Notifications"]})]}),(0,r.jsx)(d.mB,{"data-sentry-element":"DropdownMenuSeparator","data-sentry-source-file":"app-sidebar.tsx"}),(0,r.jsxs)(d._2,{onClick:()=>{x.qs.removeUser(),window.location.href="/auth/sign-in"},"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"app-sidebar.tsx",children:[(0,r.jsx)(v.A,{className:"mr-2 h-4 w-4","data-sentry-element":"LogOut","data-sentry-source-file":"app-sidebar.tsx"}),(0,r.jsx)("span",{children:"Logout"})]})]})]})})})}),(0,r.jsx)(l.jM,{"data-sentry-element":"SidebarRail","data-sentry-source-file":"app-sidebar.tsx"})]})}g.A},12547:(e,t,a)=>{a.d(t,{Avatar:()=>i,AvatarFallback:()=>d,AvatarImage:()=>o});var r=a(95155);a(12115);var s=a(46591),n=a(64269);function i(e){let{className:t,...a}=e;return(0,r.jsx)(s.bL,{"data-slot":"avatar",className:(0,n.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a,"data-sentry-element":"AvatarPrimitive.Root","data-sentry-component":"Avatar","data-sentry-source-file":"avatar.tsx"})}function o(e){let{className:t,...a}=e;return(0,r.jsx)(s._V,{"data-slot":"avatar-image",className:(0,n.cn)("aspect-square size-full",t),...a,"data-sentry-element":"AvatarPrimitive.Image","data-sentry-component":"AvatarImage","data-sentry-source-file":"avatar.tsx"})}function d(e){let{className:t,...a}=e;return(0,r.jsx)(s.H4,{"data-slot":"avatar-fallback",className:(0,n.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a,"data-sentry-element":"AvatarPrimitive.Fallback","data-sentry-component":"AvatarFallback","data-sentry-source-file":"avatar.tsx"})}},12833:(e,t,a)=>{a.d(t,{I:()=>c,SQ:()=>l,_2:()=>u,hO:()=>m,lp:()=>p,mB:()=>f,rI:()=>o,ty:()=>d});var r=a(95155);a(12115);var s=a(47971),n=a(5917),i=a(64269);function o(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"dropdown-menu",...t,"data-sentry-element":"DropdownMenuPrimitive.Root","data-sentry-component":"DropdownMenu","data-sentry-source-file":"dropdown-menu.tsx"})}function d(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"dropdown-menu-trigger",...t,"data-sentry-element":"DropdownMenuPrimitive.Trigger","data-sentry-component":"DropdownMenuTrigger","data-sentry-source-file":"dropdown-menu.tsx"})}function l(e){let{className:t,sideOffset:a=4,...n}=e;return(0,r.jsx)(s.ZL,{"data-sentry-element":"DropdownMenuPrimitive.Portal","data-sentry-component":"DropdownMenuContent","data-sentry-source-file":"dropdown-menu.tsx",children:(0,r.jsx)(s.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...n,"data-sentry-element":"DropdownMenuPrimitive.Content","data-sentry-source-file":"dropdown-menu.tsx"})})}function c(e){let{...t}=e;return(0,r.jsx)(s.YJ,{"data-slot":"dropdown-menu-group",...t,"data-sentry-element":"DropdownMenuPrimitive.Group","data-sentry-component":"DropdownMenuGroup","data-sentry-source-file":"dropdown-menu.tsx"})}function u(e){let{className:t,inset:a,variant:n="default",...o}=e;return(0,r.jsx)(s.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":n,className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground focus:[&_svg:not([class*='text-'])]:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,"data-sentry-element":"DropdownMenuPrimitive.Item","data-sentry-component":"DropdownMenuItem","data-sentry-source-file":"dropdown-menu.tsx"})}function m(e){let{className:t,children:a,checked:o,...d}=e;return(0,r.jsxs)(s.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:o,...d,"data-sentry-element":"DropdownMenuPrimitive.CheckboxItem","data-sentry-component":"DropdownMenuCheckboxItem","data-sentry-source-file":"dropdown-menu.tsx",children:[(0,r.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{"data-sentry-element":"DropdownMenuPrimitive.ItemIndicator","data-sentry-source-file":"dropdown-menu.tsx",children:(0,r.jsx)(n.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"dropdown-menu.tsx"})})}),a]})}function p(e){let{className:t,inset:a,...n}=e;return(0,r.jsx)(s.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,i.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...n,"data-sentry-element":"DropdownMenuPrimitive.Label","data-sentry-component":"DropdownMenuLabel","data-sentry-source-file":"dropdown-menu.tsx"})}function f(e){let{className:t,...a}=e;return(0,r.jsx)(s.wv,{"data-slot":"dropdown-menu-separator",className:(0,i.cn)("bg-border -mx-1 my-1 h-px",t),...a,"data-sentry-element":"DropdownMenuPrimitive.Separator","data-sentry-component":"DropdownMenuSeparator","data-sentry-source-file":"dropdown-menu.tsx"})}},16325:(e,t,a)=>{a.d(t,{Bc:()=>i,ZI:()=>l,k$:()=>d,m_:()=>o});var r=a(95155);a(12115);var s=a(47520),n=a(64269);function i(e){let{delayDuration:t=0,...a}=e;return(0,r.jsx)(s.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a,"data-sentry-element":"TooltipPrimitive.Provider","data-sentry-component":"TooltipProvider","data-sentry-source-file":"tooltip.tsx"})}function o(e){let{...t}=e;return(0,r.jsx)(i,{"data-sentry-element":"TooltipProvider","data-sentry-component":"Tooltip","data-sentry-source-file":"tooltip.tsx",children:(0,r.jsx)(s.bL,{"data-slot":"tooltip",...t,"data-sentry-element":"TooltipPrimitive.Root","data-sentry-source-file":"tooltip.tsx"})})}function d(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"tooltip-trigger",...t,"data-sentry-element":"TooltipPrimitive.Trigger","data-sentry-component":"TooltipTrigger","data-sentry-source-file":"tooltip.tsx"})}function l(e){let{className:t,sideOffset:a=0,children:i,...o}=e;return(0,r.jsx)(s.ZL,{"data-sentry-element":"TooltipPrimitive.Portal","data-sentry-component":"TooltipContent","data-sentry-source-file":"tooltip.tsx",children:(0,r.jsxs)(s.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,n.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...o,"data-sentry-element":"TooltipPrimitive.Content","data-sentry-source-file":"tooltip.tsx",children:[i,(0,r.jsx)(s.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]","data-sentry-element":"TooltipPrimitive.Arrow","data-sentry-source-file":"tooltip.tsx"})]})})}},20764:(e,t,a)=>{a.d(t,{$:()=>d,r:()=>o});var r=a(95155);a(12115);var s=a(32467),n=a(83101),i=a(64269);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:n,asChild:d=!1,...l}=e,c=d?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:a,size:n,className:t})),...l,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},28370:(e,t,a)=>{a.d(t,{Bx:()=>A,Yv:()=>I,CG:()=>M,Cn:()=>D,jj:()=>P,Gh:()=>k,SidebarInset:()=>C,wZ:()=>z,Uj:()=>B,FX:()=>_,q9:()=>L,Cp:()=>R,Fg:()=>U,SidebarProvider:()=>j,jM:()=>S,x2:()=>N,cL:()=>w});var r=a(95155),s=a(12115),n=a(32467),i=a(83101),o=a(77931),d=a(64269),l=a(20764);a(31936),a(46201);var c=a(89511),u=a(65229);function m(e){let{...t}=e;return(0,r.jsx)(c.bL,{"data-slot":"sheet",...t,"data-sentry-element":"SheetPrimitive.Root","data-sentry-component":"Sheet","data-sentry-source-file":"sheet.tsx"})}function p(e){let{...t}=e;return(0,r.jsx)(c.ZL,{"data-slot":"sheet-portal",...t,"data-sentry-element":"SheetPrimitive.Portal","data-sentry-component":"SheetPortal","data-sentry-source-file":"sheet.tsx"})}function f(e){let{className:t,...a}=e;return(0,r.jsx)(c.hJ,{"data-slot":"sheet-overlay",className:(0,d.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a,"data-sentry-element":"SheetPrimitive.Overlay","data-sentry-component":"SheetOverlay","data-sentry-source-file":"sheet.tsx"})}function x(e){let{className:t,children:a,side:s="right",...n}=e;return(0,r.jsxs)(p,{"data-sentry-element":"SheetPortal","data-sentry-component":"SheetContent","data-sentry-source-file":"sheet.tsx",children:[(0,r.jsx)(f,{"data-sentry-element":"SheetOverlay","data-sentry-source-file":"sheet.tsx"}),(0,r.jsxs)(c.UC,{"data-slot":"sheet-content",className:(0,d.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===s&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===s&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===s&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===s&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...n,"data-sentry-element":"SheetPrimitive.Content","data-sentry-source-file":"sheet.tsx",children:[a,(0,r.jsxs)(c.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none","data-sentry-element":"SheetPrimitive.Close","data-sentry-source-file":"sheet.tsx",children:[(0,r.jsx)(u.A,{className:"size-4","data-sentry-element":"XIcon","data-sentry-source-file":"sheet.tsx"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function h(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sheet-header",className:(0,d.cn)("flex flex-col gap-1.5 p-4",t),...a,"data-sentry-component":"SheetHeader","data-sentry-source-file":"sheet.tsx"})}function b(e){let{className:t,...a}=e;return(0,r.jsx)(c.hE,{"data-slot":"sheet-title",className:(0,d.cn)("text-foreground font-semibold",t),...a,"data-sentry-element":"SheetPrimitive.Title","data-sentry-component":"SheetTitle","data-sentry-source-file":"sheet.tsx"})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(c.VY,{"data-slot":"sheet-description",className:(0,d.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-element":"SheetPrimitive.Description","data-sentry-component":"SheetDescription","data-sentry-source-file":"sheet.tsx"})}a(88941);var y=a(16325);let v=s.createContext(null);function w(){let e=s.useContext(v);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function j(e){let{defaultOpen:t=!0,open:a,onOpenChange:n,className:i,style:o,children:l,...c}=e,u=function(){let[e,t]=s.useState(void 0);return s.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),a=()=>{t(window.innerWidth<768)};return e.addEventListener("change",a),t(window.innerWidth<768),()=>e.removeEventListener("change",a)},[]),!!e}(),[m,p]=s.useState(!1),[f,x]=s.useState(t),h=null!=a?a:f,b=s.useCallback(e=>{let t="function"==typeof e?e(h):e;n?n(t):x(t),document.cookie="".concat("sidebar_state","=").concat(t,"; path=/; max-age=").concat(604800)},[n,h]),g=s.useCallback(()=>u?p(e=>!e):b(e=>!e),[u,b,p]);s.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),g())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[g]);let w=h?"expanded":"collapsed",j=s.useMemo(()=>({state:w,open:h,setOpen:b,isMobile:u,openMobile:m,setOpenMobile:p,toggleSidebar:g}),[w,h,b,u,m,p,g]);return(0,r.jsx)(v.Provider,{value:j,"data-sentry-element":"SidebarContext.Provider","data-sentry-component":"SidebarProvider","data-sentry-source-file":"sidebar.tsx",children:(0,r.jsx)(y.Bc,{delayDuration:0,"data-sentry-element":"TooltipProvider","data-sentry-source-file":"sidebar.tsx",children:(0,r.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...o},className:(0,d.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",i),...c,children:l})})})}function A(e){let{side:t="left",variant:a="sidebar",collapsible:s="offcanvas",className:n,children:i,...o}=e,{isMobile:l,state:c,openMobile:u,setOpenMobile:p}=w();return"none"===s?(0,r.jsx)("div",{"data-slot":"sidebar",className:(0,d.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",n),...o,children:i}):l?(0,r.jsx)(m,{open:u,onOpenChange:p,...o,children:(0,r.jsxs)(x,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:t,children:[(0,r.jsxs)(h,{className:"sr-only",children:[(0,r.jsx)(b,{children:"Sidebar"}),(0,r.jsx)(g,{children:"Displays the mobile sidebar."})]}),(0,r.jsx)("div",{className:"flex h-full w-full flex-col",children:i})]})}):(0,r.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":c,"data-collapsible":"collapsed"===c?s:"","data-variant":a,"data-side":t,"data-slot":"sidebar","data-sentry-component":"Sidebar","data-sentry-source-file":"sidebar.tsx",children:[(0,r.jsx)("div",{"data-slot":"sidebar-gap",className:(0,d.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===a||"inset"===a?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,r.jsx)("div",{"data-slot":"sidebar-container",className:(0,d.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===t?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===a||"inset"===a?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",n),...o,children:(0,r.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:i})})]})}function N(e){let{className:t,onClick:a,...s}=e,{toggleSidebar:n}=w();return(0,r.jsxs)(l.$,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,d.cn)("size-7",t),onClick:e=>{null==a||a(e),n()},...s,"data-sentry-element":"Button","data-sentry-component":"SidebarTrigger","data-sentry-source-file":"sidebar.tsx",children:[(0,r.jsx)(o.A,{"data-sentry-element":"PanelLeftIcon","data-sentry-source-file":"sidebar.tsx"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function S(e){let{className:t,...a}=e,{toggleSidebar:s}=w();return(0,r.jsx)("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:s,title:"Toggle Sidebar",className:(0,d.cn)("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",t),...a,"data-sentry-component":"SidebarRail","data-sentry-source-file":"sidebar.tsx"})}function C(e){let{className:t,...a}=e;return(0,r.jsx)("main",{"data-slot":"sidebar-inset",className:(0,d.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",t),...a,"data-sentry-component":"SidebarInset","data-sentry-source-file":"sidebar.tsx"})}function k(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,d.cn)("flex flex-col gap-2 p-2",t),...a,"data-sentry-component":"SidebarHeader","data-sentry-source-file":"sidebar.tsx"})}function M(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,d.cn)("flex flex-col gap-2 p-2",t),...a,"data-sentry-component":"SidebarFooter","data-sentry-source-file":"sidebar.tsx"})}function I(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,d.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...a,"data-sentry-component":"SidebarContent","data-sentry-source-file":"sidebar.tsx"})}function D(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,d.cn)("relative flex w-full min-w-0 flex-col p-2",t),...a,"data-sentry-component":"SidebarGroup","data-sentry-source-file":"sidebar.tsx"})}function P(e){let{className:t,asChild:a=!1,...s}=e,i=a?n.DX:"div";return(0,r.jsx)(i,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,d.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t),...s,"data-sentry-element":"Comp","data-sentry-component":"SidebarGroupLabel","data-sentry-source-file":"sidebar.tsx"})}function z(e){let{className:t,...a}=e;return(0,r.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,d.cn)("flex w-full min-w-0 flex-col gap-1",t),...a,"data-sentry-component":"SidebarMenu","data-sentry-source-file":"sidebar.tsx"})}function _(e){let{className:t,...a}=e;return(0,r.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,d.cn)("group/menu-item relative",t),...a,"data-sentry-component":"SidebarMenuItem","data-sentry-source-file":"sidebar.tsx"})}let T=(0,i.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 cursor-pointer",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function B(e){let{asChild:t=!1,isActive:a=!1,variant:s="default",size:i="default",tooltip:o,className:l,...c}=e,u=t?n.DX:"button",{isMobile:m,state:p}=w(),f=(0,r.jsx)(u,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":i,"data-active":a,className:(0,d.cn)(T({variant:s,size:i}),l),...c});return o?("string"==typeof o&&(o={children:o}),(0,r.jsxs)(y.m_,{"data-sentry-element":"Tooltip","data-sentry-component":"SidebarMenuButton","data-sentry-source-file":"sidebar.tsx",children:[(0,r.jsx)(y.k$,{asChild:!0,"data-sentry-element":"TooltipTrigger","data-sentry-source-file":"sidebar.tsx",children:f}),(0,r.jsx)(y.ZI,{side:"right",align:"center",hidden:"collapsed"!==p||m,...o,"data-sentry-element":"TooltipContent","data-sentry-source-file":"sidebar.tsx"})]})):f}function L(e){let{className:t,...a}=e;return(0,r.jsx)("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:(0,d.cn)("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",t),...a,"data-sentry-component":"SidebarMenuSub","data-sentry-source-file":"sidebar.tsx"})}function U(e){let{className:t,...a}=e;return(0,r.jsx)("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:(0,d.cn)("group/menu-sub-item relative",t),...a,"data-sentry-component":"SidebarMenuSubItem","data-sentry-source-file":"sidebar.tsx"})}function R(e){let{asChild:t=!1,size:a="md",isActive:s=!1,className:i,...o}=e,l=t?n.DX:"a";return(0,r.jsx)(l,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":a,"data-active":s,className:(0,d.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===a&&"text-xs","md"===a&&"text-sm","group-data-[collapsible=icon]:hidden",i),...o,"data-sentry-element":"Comp","data-sentry-component":"SidebarMenuSubButton","data-sentry-source-file":"sidebar.tsx"})}},31936:(e,t,a)=>{a.d(t,{p:()=>n});var r=a(95155);a(12115);var s=a(64269);function n(e){let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},37287:(e,t,a)=>{a.d(t,{default:()=>x});var r=a(95155);a(12115);var s=a(28370),n=a(20764),i=a(12833),o=a(85188),d=a(20063),l=a(61289);function c(){let e=(0,d.useRouter)(),{user:t,signOut:a}=(0,l.A)();return t?(0,r.jsxs)(i.rI,{"data-sentry-element":"DropdownMenu","data-sentry-component":"UserNav","data-sentry-source-file":"user-nav.tsx",children:[(0,r.jsx)(i.ty,{asChild:!0,"data-sentry-element":"DropdownMenuTrigger","data-sentry-source-file":"user-nav.tsx",children:(0,r.jsx)(n.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full cursor-pointer","data-sentry-element":"Button","data-sentry-source-file":"user-nav.tsx",children:(0,r.jsx)(o.M,{"data-sentry-element":"UserAvatarProfile","data-sentry-source-file":"user-nav.tsx"})})}),(0,r.jsxs)(i.SQ,{className:"w-56",align:"end",sideOffset:10,forceMount:!0,"data-sentry-element":"DropdownMenuContent","data-sentry-source-file":"user-nav.tsx",children:[(0,r.jsx)(i.lp,{className:"font-normal","data-sentry-element":"DropdownMenuLabel","data-sentry-source-file":"user-nav.tsx",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,r.jsx)("p",{className:"text-sm leading-none font-medium",children:t.name}),(0,r.jsx)("p",{className:"text-muted-foreground text-xs leading-none",children:t.email})]})}),(0,r.jsx)(i.mB,{"data-sentry-element":"DropdownMenuSeparator","data-sentry-source-file":"user-nav.tsx"}),(0,r.jsxs)(i.I,{"data-sentry-element":"DropdownMenuGroup","data-sentry-source-file":"user-nav.tsx",children:[(0,r.jsx)(i._2,{onClick:()=>e.push("/dashboard/profile"),"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"user-nav.tsx",children:"Profile"}),(0,r.jsx)(i._2,{"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"user-nav.tsx",children:"Billing"})]}),(0,r.jsx)(i.mB,{"data-sentry-element":"DropdownMenuSeparator","data-sentry-source-file":"user-nav.tsx"}),(0,r.jsx)(i._2,{onClick:a,"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"user-nav.tsx",children:"Sign Out"})]})]}):null}var u=a(20508),m=a(15870),p=a(80021),f=a(89715);function x(){let e=(0,d.useRouter)();return(0,r.jsxs)("header",{className:"flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12","data-sentry-component":"Header","data-sentry-source-file":"header.tsx",children:[(0,r.jsx)("div",{className:"flex items-center gap-2 px-4",children:(0,r.jsx)(s.x2,{className:"-ml-1","data-sentry-element":"SidebarTrigger","data-sentry-source-file":"header.tsx"})}),(0,r.jsxs)("div",{className:"flex items-center gap-2 px-4",children:[(0,r.jsxs)(i.rI,{"data-sentry-element":"DropdownMenu","data-sentry-source-file":"header.tsx",children:[(0,r.jsx)(i.ty,{asChild:!0,"data-sentry-element":"DropdownMenuTrigger","data-sentry-source-file":"header.tsx",children:(0,r.jsxs)(n.$,{variant:"ghost",size:"icon",className:"relative h-8 w-8 rounded-full cursor-pointer","data-sentry-element":"Button","data-sentry-source-file":"header.tsx",children:[(0,r.jsx)(u.A,{className:"h-4 w-4","data-sentry-element":"User","data-sentry-source-file":"header.tsx"}),(0,r.jsx)("span",{className:"sr-only",children:"Profile Menu"})]})}),(0,r.jsxs)(i.SQ,{className:"w-56",align:"end",sideOffset:10,"data-sentry-element":"DropdownMenuContent","data-sentry-source-file":"header.tsx",children:[(0,r.jsx)(i.lp,{"data-sentry-element":"DropdownMenuLabel","data-sentry-source-file":"header.tsx",children:"Profile & Settings"}),(0,r.jsx)(i.mB,{"data-sentry-element":"DropdownMenuSeparator","data-sentry-source-file":"header.tsx"}),(0,r.jsxs)(i.I,{"data-sentry-element":"DropdownMenuGroup","data-sentry-source-file":"header.tsx",children:[(0,r.jsxs)(i._2,{onClick:()=>e.push("/dashboard/profile"),"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"header.tsx",children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4","data-sentry-element":"User","data-sentry-source-file":"header.tsx"}),"View Profile"]}),(0,r.jsxs)(i._2,{onClick:()=>e.push("/dashboard/settings"),"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"header.tsx",children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Settings","data-sentry-source-file":"header.tsx"}),"Settings"]}),(0,r.jsxs)(i._2,{onClick:()=>e.push("/dashboard/help"),"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"header.tsx",children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4","data-sentry-element":"HelpCircle","data-sentry-source-file":"header.tsx"}),"Help & Support"]}),(0,r.jsxs)(i._2,{onClick:()=>e.push("/dashboard/documentation"),"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"header.tsx",children:[(0,r.jsx)(f.A,{className:"mr-2 h-4 w-4","data-sentry-element":"FileText","data-sentry-source-file":"header.tsx"}),"Documentation"]})]})]})]}),(0,r.jsx)(c,{"data-sentry-element":"UserNav","data-sentry-source-file":"header.tsx"})]})]})}},46201:(e,t,a)=>{a.d(t,{Separator:()=>o});var r=a(95155),s=a(12115),n=a(57268),i=a(64269);let o=s.forwardRef((e,t)=>{let{className:a,orientation:s="horizontal",decorative:o=!0,...d}=e;return(0,r.jsx)(n.b,{ref:t,decorative:o,orientation:s,className:(0,i.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",a),...d})});o.displayName=n.b.displayName},47886:(e,t,a)=>{a.d(t,{WG:()=>s,cl:()=>i,qs:()=>r});let r={setUser:e=>{localStorage.setItem("auth_user",JSON.stringify(e))},getUser:()=>{{let e=localStorage.getItem("auth_user");return e?JSON.parse(e):null}},removeUser:()=>{localStorage.removeItem("auth_user")},isAuthenticated:()=>null!==r.getUser(),hasRole:e=>{let t=r.getUser();return(null==t?void 0:t.role)===e},isSuperAdmin:()=>r.hasRole("super_admin"),isTeacher:()=>r.hasRole("teacher"),isStudent:()=>r.hasRole("student")},s=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},n=()=>{let e=r.getUser();return e||(window.location.href="/auth/sign-in",null)},i=e=>{let t=n();return t?t.role!==e?(window.location.href=s(t),null):t:null}},61289:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(12115),s=a(47886),n=a(20063);function i(){let[e,t]=(0,r.useState)(null),[a,i]=(0,r.useState)(!0),o=(0,n.useRouter)();return(0,r.useEffect)(()=>{let e=s.qs.getUser();e&&t(e),i(!1)},[]),{user:e,loading:a,signOut:()=>{s.qs.removeUser(),t(null),o.push("/auth/sign-in")}}}},64269:(e,t,a)=>{a.d(t,{cn:()=>n,z:()=>i});var r=a(2821),s=a(75889);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}function i(e){var t,a;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:s=0,sizeType:n="normal"}=r;if(0===e)return"0 Byte";let i=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,i)).toFixed(s)," ").concat("accurate"===n?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][i])?t:"Bytest":null!=(a=["Bytes","KB","MB","GB","TB"][i])?a:"Bytes")}},85188:(e,t,a)=>{a.d(t,{M:()=>i});var r=a(95155),s=a(12547),n=a(61289);function i(e){var t,a;let{className:i,showInfo:o=!1}=e,{user:d}=(0,n.A)();return d?(0,r.jsxs)("div",{className:"flex items-center gap-2","data-sentry-component":"UserAvatarProfile","data-sentry-source-file":"user-avatar-profile.tsx",children:[(0,r.jsxs)(s.Avatar,{className:i,"data-sentry-element":"Avatar","data-sentry-source-file":"user-avatar-profile.tsx",children:[(0,r.jsx)(s.AvatarImage,{src:"https://ui-avatars.com/api/?name=".concat(d.name,"&background=random"),alt:d.name,"data-sentry-element":"AvatarImage","data-sentry-source-file":"user-avatar-profile.tsx"}),(0,r.jsx)(s.AvatarFallback,{className:"rounded-lg","data-sentry-element":"AvatarFallback","data-sentry-source-file":"user-avatar-profile.tsx",children:(null==(a=d.name)||null==(t=a.slice(0,2))?void 0:t.toUpperCase())||"U"})]}),o&&(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-semibold",children:d.name}),(0,r.jsx)("span",{className:"truncate text-xs",children:d.email})]})]}):(0,r.jsx)(s.Avatar,{className:i,children:(0,r.jsx)(s.AvatarFallback,{children:"G"})})}},88941:(e,t,a)=>{a.d(t,{E:()=>n});var r=a(95155),s=a(64269);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"skeleton",className:(0,s.cn)("bg-accent animate-pulse rounded-md",t),...a,"data-sentry-component":"Skeleton","data-sentry-source-file":"skeleton.tsx"})}},99813:(e,t,a)=>{a.d(t,{default:()=>m});var r=a(95155),s=a(2030),n=a(51497),i=a(20063),o=a(12115);let d=o.forwardRef((e,t)=>{var a;let{action:s,active:n,currentRootActionId:i}=e,d=o.useMemo(()=>{if(!i)return s.ancestors;let e=s.ancestors.findIndex(e=>e.id===i);return s.ancestors.slice(e+1)},[s.ancestors,i]);return(0,r.jsxs)("div",{ref:t,className:"relative z-10 flex cursor-pointer items-center justify-between px-4 py-3",children:[n&&(0,r.jsx)("div",{id:"kbar-result-item",className:"border-primary bg-accent/50 absolute inset-0 z-[-1]! border-l-4"}),(0,r.jsxs)("div",{className:"relative z-10 flex items-center gap-2",children:[s.icon&&s.icon,(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsxs)("div",{children:[d.length>0&&d.map(e=>(0,r.jsxs)(o.Fragment,{children:[(0,r.jsx)("span",{className:"text-muted-foreground mr-2",children:e.name}),(0,r.jsx)("span",{className:"mr-2",children:"›"})]},e.id)),(0,r.jsx)("span",{children:s.name})]}),s.subtitle&&(0,r.jsx)("span",{className:"text-muted-foreground text-sm",children:s.subtitle})]})]}),(null==(a=s.shortcut)?void 0:a.length)?(0,r.jsx)("div",{className:"relative z-10 grid grid-flow-col gap-1",children:s.shortcut.map((e,t)=>(0,r.jsx)("kbd",{className:"bg-muted flex h-5 items-center gap-1 rounded-md border px-1.5 text-[10px] font-medium",children:e},e+t))}):null]})});function l(){let{results:e,rootActionId:t}=(0,n.useMatches)();return(0,r.jsx)(n.KBarResults,{items:e,onRender:e=>{let{item:a,active:s}=e;return"string"==typeof a?(0,r.jsx)("div",{className:"text-primary-foreground px-4 py-2 text-sm uppercase opacity-50",children:a}):(0,r.jsx)(d,{action:a,active:s,currentRootActionId:null!=t?t:""})},"data-sentry-element":"KBarResults","data-sentry-component":"RenderResults","data-sentry-source-file":"render-result.tsx"})}d.displayName="KBarResultItem";var c=a(5379);let u=()=>{let{theme:e,setTheme:t}=(0,c.D)();(0,n.useRegisterActions)([{id:"toggleTheme",name:"Toggle Theme",shortcut:["t","t"],section:"Theme",perform:()=>{t("light"===e?"dark":"light")}},{id:"setLightTheme",name:"Set Light Theme",section:"Theme",perform:()=>t("light")},{id:"setDarkTheme",name:"Set Dark Theme",section:"Theme",perform:()=>t("dark")}],[e])};function m(e){let{children:t}=e,a=(0,i.useRouter)(),d=(0,o.useMemo)(()=>{let e=e=>{a.push(e)};return s.Cn.flatMap(t=>{var a,r;let s="#"!==t.url?{id:"".concat(t.title.toLowerCase(),"Action"),name:t.title,shortcut:t.shortcut,keywords:t.title.toLowerCase(),section:"Navigation",subtitle:"Go to ".concat(t.title),perform:()=>e(t.url)}:null,n=null!=(r=null==(a=t.items)?void 0:a.map(a=>({id:"".concat(a.title.toLowerCase(),"Action"),name:a.title,shortcut:a.shortcut,keywords:a.title.toLowerCase(),section:t.title,subtitle:"Go to ".concat(a.title),perform:()=>e(a.url)})))?r:[];return s?[s,...n]:n})},[a]);return(0,r.jsx)(n.KBarProvider,{actions:d,"data-sentry-element":"KBarProvider","data-sentry-component":"KBar","data-sentry-source-file":"index.tsx",children:(0,r.jsx)(p,{"data-sentry-element":"KBarComponent","data-sentry-source-file":"index.tsx",children:t})})}let p=e=>{let{children:t}=e;return u(),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.KBarPortal,{"data-sentry-element":"KBarPortal","data-sentry-source-file":"index.tsx",children:(0,r.jsx)(n.KBarPositioner,{className:"bg-background/80 fixed inset-0 z-99999 p-0! backdrop-blur-sm","data-sentry-element":"KBarPositioner","data-sentry-source-file":"index.tsx",children:(0,r.jsxs)(n.KBarAnimator,{className:"bg-card text-card-foreground relative mt-64! w-full max-w-[600px] -translate-y-12! overflow-hidden rounded-lg border shadow-lg","data-sentry-element":"KBarAnimator","data-sentry-source-file":"index.tsx",children:[(0,r.jsx)("div",{className:"bg-card border-border sticky top-0 z-10 border-b",children:(0,r.jsx)(n.KBarSearch,{className:"bg-card w-full border-none px-6 py-4 text-lg outline-hidden focus:ring-0 focus:ring-offset-0 focus:outline-hidden","data-sentry-element":"KBarSearch","data-sentry-source-file":"index.tsx"})}),(0,r.jsx)("div",{className:"max-h-[400px]",children:(0,r.jsx)(l,{"data-sentry-element":"RenderResults","data-sentry-source-file":"index.tsx"})})]})})}),t]})}}}]);