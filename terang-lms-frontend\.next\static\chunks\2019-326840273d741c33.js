try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="6074b11d-68ad-426c-9916-888c7d96ae76",e._sentryDebugIdIdentifier="sentry-dbid-6074b11d-68ad-426c-9916-888c7d96ae76")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2019],{1524:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71847).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},71847:(e,t,n)=>{n.d(t,{A:()=>s});var r=n(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:s,className:c="",children:u,iconNode:p,...y}=e;return(0,r.createElement)("svg",{ref:t,...o,width:a,height:a,stroke:n,strokeWidth:s?24*Number(l)/Number(a):l,className:i("lucide",c),...y},[...p.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(u)?u:[u]])}),s=(e,t)=>{let n=(0,r.forwardRef)((n,o)=>{let{className:s,...c}=n;return(0,r.createElement)(l,{ref:o,iconNode:t,className:i("lucide-".concat(a(e)),s),...c})});return n.displayName="".concat(e),n}},73850:(e,t,n)=>{n.d(t,{Q:()=>s});var r=n(83946),a=n(77568),i=n(47734),o=n(73697),l=n(36164),s=(0,r.gu)({chartName:"AreaChart",GraphicalChild:a.G,axisComponents:[{axisType:"xAxis",AxisComp:i.W},{axisType:"yAxis",AxisComp:o.h}],formatAxisMap:l.pr})},77568:(e,t,n)=>{n.d(t,{G:()=>R});var r=n(12115),a=n(2821),i=n(37510),o=n(71730),l=n.n(o),s=n(72092),c=n.n(s),u=n(80385),p=n.n(u),y=n(32323),f=n.n(y),d=n(35138),h=n.n(d),m=n(7050),v=n(36927),b=n(87095),g=n(14724),A=n(33692),x=n(49580),O=n(24719),w=n(70543),E=["layout","type","stroke","connectNulls","isRange","ref"],P=["key"];function k(e){return(k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function j(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function D(){return(D=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function S(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function N(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?S(Object(n),!0).forEach(function(t){_(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):S(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function I(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,B(r.key),r)}}function L(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(L=function(){return!!e})()}function M(e){return(M=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function C(e,t){return(C=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _(e,t,n){return(t=B(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function B(e){var t=function(e,t){if("object"!=k(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=k(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==k(t)?t:t+""}var R=function(e){var t,n;function o(){var e,t,n;if(!(this instanceof o))throw TypeError("Cannot call a class as a function");for(var r=arguments.length,a=Array(r),i=0;i<r;i++)a[i]=arguments[i];return t=o,n=[].concat(a),t=M(t),_(e=function(e,t){if(t&&("object"===k(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var n=e;if(void 0===n)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return n}(this,L()?Reflect.construct(t,n||[],M(this).constructor):t.apply(this,n)),"state",{isAnimationFinished:!0}),_(e,"id",(0,x.NF)("recharts-area-")),_(e,"handleAnimationEnd",function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),l()(t)&&t()}),_(e,"handleAnimationStart",function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),l()(t)&&t()}),e}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return o.prototype=Object.create(e&&e.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),e&&C(o,e),t=[{key:"renderDots",value:function(e,t,n){var a=this.props.isAnimationActive,i=this.state.isAnimationFinished;if(a&&!i)return null;var l=this.props,s=l.dot,c=l.points,u=l.dataKey,p=(0,w.J9)(this.props,!1),y=(0,w.J9)(s,!0),f=c.map(function(e,t){var n=N(N(N({key:"dot-".concat(t),r:3},p),y),{},{index:t,cx:e.x,cy:e.y,dataKey:u,value:e.value,payload:e.payload,points:c});return o.renderDotItem(s,n)}),d={clipPath:e?"url(#clipPath-".concat(t?"":"dots-").concat(n,")"):null};return r.createElement(b.W,D({className:"recharts-area-dots"},d),f)}},{key:"renderHorizontalRect",value:function(e){var t=this.props,n=t.baseLine,a=t.points,i=t.strokeWidth,o=a[0].x,l=a[a.length-1].x,s=e*Math.abs(o-l),u=c()(a.map(function(e){return e.y||0}));return((0,x.Et)(n)&&"number"==typeof n?u=Math.max(n,u):n&&Array.isArray(n)&&n.length&&(u=Math.max(c()(n.map(function(e){return e.y||0})),u)),(0,x.Et)(u))?r.createElement("rect",{x:o<l?o:o-s,y:0,width:s,height:Math.floor(u+(i?parseInt("".concat(i),10):1))}):null}},{key:"renderVerticalRect",value:function(e){var t=this.props,n=t.baseLine,a=t.points,i=t.strokeWidth,o=a[0].y,l=a[a.length-1].y,s=e*Math.abs(o-l),u=c()(a.map(function(e){return e.x||0}));return((0,x.Et)(n)&&"number"==typeof n?u=Math.max(n,u):n&&Array.isArray(n)&&n.length&&(u=Math.max(c()(n.map(function(e){return e.x||0})),u)),(0,x.Et)(u))?r.createElement("rect",{x:0,y:o<l?o:o-s,width:u+(i?parseInt("".concat(i),10):1),height:Math.floor(s)}):null}},{key:"renderClipRect",value:function(e){return"vertical"===this.props.layout?this.renderVerticalRect(e):this.renderHorizontalRect(e)}},{key:"renderAreaStatically",value:function(e,t,n,a){var i=this.props,o=i.layout,l=i.type,s=i.stroke,c=i.connectNulls,u=i.isRange,p=(i.ref,j(i,E));return r.createElement(b.W,{clipPath:n?"url(#clipPath-".concat(a,")"):null},r.createElement(m.I,D({},(0,w.J9)(p,!0),{points:e,connectNulls:c,type:l,baseLine:t,layout:o,stroke:"none",className:"recharts-area-area"})),"none"!==s&&r.createElement(m.I,D({},(0,w.J9)(this.props,!1),{className:"recharts-area-curve",layout:o,type:l,connectNulls:c,fill:"none",points:e})),"none"!==s&&u&&r.createElement(m.I,D({},(0,w.J9)(this.props,!1),{className:"recharts-area-curve",layout:o,type:l,connectNulls:c,fill:"none",points:t})))}},{key:"renderAreaWithAnimation",value:function(e,t){var n=this,a=this.props,o=a.points,l=a.baseLine,s=a.isAnimationActive,c=a.animationBegin,u=a.animationDuration,y=a.animationEasing,d=a.animationId,h=this.state,m=h.prevPoints,v=h.prevBaseLine;return r.createElement(i.Ay,{begin:c,duration:u,isActive:s,easing:y,from:{t:0},to:{t:1},key:"area-".concat(d),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(a){var i=a.t;if(m){var s,c=m.length/o.length,u=o.map(function(e,t){var n=Math.floor(t*c);if(m[n]){var r=m[n],a=(0,x.Dj)(r.x,e.x),o=(0,x.Dj)(r.y,e.y);return N(N({},e),{},{x:a(i),y:o(i)})}return e});return s=(0,x.Et)(l)&&"number"==typeof l?(0,x.Dj)(v,l)(i):p()(l)||f()(l)?(0,x.Dj)(v,0)(i):l.map(function(e,t){var n=Math.floor(t*c);if(v[n]){var r=v[n],a=(0,x.Dj)(r.x,e.x),o=(0,x.Dj)(r.y,e.y);return N(N({},e),{},{x:a(i),y:o(i)})}return e}),n.renderAreaStatically(u,s,e,t)}return r.createElement(b.W,null,r.createElement("defs",null,r.createElement("clipPath",{id:"animationClipPath-".concat(t)},n.renderClipRect(i))),r.createElement(b.W,{clipPath:"url(#animationClipPath-".concat(t,")")},n.renderAreaStatically(o,l,e,t)))})}},{key:"renderArea",value:function(e,t){var n=this.props,r=n.points,a=n.baseLine,i=n.isAnimationActive,o=this.state,l=o.prevPoints,s=o.prevBaseLine,c=o.totalLength;return i&&r&&r.length&&(!l&&c>0||!h()(l,r)||!h()(s,a))?this.renderAreaWithAnimation(e,t):this.renderAreaStatically(r,a,e,t)}},{key:"render",value:function(){var e,t=this.props,n=t.hide,i=t.dot,o=t.points,l=t.className,s=t.top,c=t.left,u=t.xAxis,y=t.yAxis,f=t.width,d=t.height,h=t.isAnimationActive,m=t.id;if(n||!o||!o.length)return null;var v=this.state.isAnimationFinished,A=1===o.length,x=(0,a.A)("recharts-area",l),O=u&&u.allowDataOverflow,E=y&&y.allowDataOverflow,P=O||E,k=p()(m)?this.id:m,j=null!=(e=(0,w.J9)(i,!1))?e:{r:3,strokeWidth:2},D=j.r,S=j.strokeWidth,N=((0,w.sT)(i)?i:{}).clipDot,I=void 0===N||N,L=2*(void 0===D?3:D)+(void 0===S?2:S);return r.createElement(b.W,{className:x},O||E?r.createElement("defs",null,r.createElement("clipPath",{id:"clipPath-".concat(k)},r.createElement("rect",{x:O?c:c-f/2,y:E?s:s-d/2,width:O?f:2*f,height:E?d:2*d})),!I&&r.createElement("clipPath",{id:"clipPath-dots-".concat(k)},r.createElement("rect",{x:c-L/2,y:s-L/2,width:f+L,height:d+L}))):null,A?null:this.renderArea(P,k),(i||A)&&this.renderDots(P,I,k),(!h||v)&&g.Z.renderCallByParent(this.props,o))}}],n=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curPoints:e.points,curBaseLine:e.baseLine,prevPoints:t.curPoints,prevBaseLine:t.curBaseLine}:e.points!==t.curPoints||e.baseLine!==t.curBaseLine?{curPoints:e.points,curBaseLine:e.baseLine}:null}}],t&&I(o.prototype,t),n&&I(o,n),Object.defineProperty(o,"prototype",{writable:!1}),o}(r.PureComponent);_(R,"displayName","Area"),_(R,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!A.m.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),_(R,"getBaseValue",function(e,t,n,r){var a=e.layout,i=e.baseValue,o=t.props.baseValue,l=null!=o?o:i;if((0,x.Et)(l)&&"number"==typeof l)return l;var s="horizontal"===a?r:n,c=s.scale.domain();if("number"===s.type){var u=Math.max(c[0],c[1]),p=Math.min(c[0],c[1]);return"dataMin"===l?p:"dataMax"===l||u<0?u:Math.max(Math.min(c[0],c[1]),0)}return"dataMin"===l?c[0]:"dataMax"===l?c[1]:c[0]}),_(R,"getComposedData",function(e){var t,n=e.props,r=e.item,a=e.xAxis,i=e.yAxis,o=e.xAxisTicks,l=e.yAxisTicks,s=e.bandSize,c=e.dataKey,u=e.stackedData,p=e.dataStartIndex,y=e.displayedData,f=e.offset,d=n.layout,h=u&&u.length,m=R.getBaseValue(n,r,a,i),v="horizontal"===d,b=!1,g=y.map(function(e,t){h?n=u[p+t]:Array.isArray(n=(0,O.kr)(e,c))?b=!0:n=[m,n];var n,r=null==n[1]||h&&null==(0,O.kr)(e,c);return v?{x:(0,O.nb)({axis:a,ticks:o,bandSize:s,entry:e,index:t}),y:r?null:i.scale(n[1]),value:n,payload:e}:{x:r?null:a.scale(n[1]),y:(0,O.nb)({axis:i,ticks:l,bandSize:s,entry:e,index:t}),value:n,payload:e}});return t=h||b?g.map(function(e){var t=Array.isArray(e.value)?e.value[0]:null;return v?{x:e.x,y:null!=t&&null!=e.y?i.scale(t):null}:{x:null!=t?a.scale(t):null,y:e.y}}):v?i.scale(m):a.scale(m),N({points:g,baseLine:t,layout:d,isRange:b},f)}),_(R,"renderDotItem",function(e,t){var n;if(r.isValidElement(e))n=r.cloneElement(e,t);else if(l()(e))n=e(t);else{var i=(0,a.A)("recharts-area-dot","boolean"!=typeof e?e.className:""),o=t.key,s=j(t,P);n=r.createElement(v.c,D({},s,{key:o,className:i}))}return n})}}]);