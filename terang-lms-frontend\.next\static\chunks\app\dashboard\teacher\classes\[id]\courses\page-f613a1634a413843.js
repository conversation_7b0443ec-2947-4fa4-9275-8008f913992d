try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="83f0fd39-dc70-4ae0-9085-cf6242375b5e",e._sentryDebugIdIdentifier="sentry-dbid-83f0fd39-dc70-4ae0-9085-cf6242375b5e")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6158],{12833:(e,t,s)=>{"use strict";s.d(t,{I:()=>c,SQ:()=>d,_2:()=>u,hO:()=>m,lp:()=>x,mB:()=>f,rI:()=>l,ty:()=>i});var a=s(95155);s(12115);var r=s(47971),n=s(5917),o=s(64269);function l(e){let{...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"dropdown-menu",...t,"data-sentry-element":"DropdownMenuPrimitive.Root","data-sentry-component":"DropdownMenu","data-sentry-source-file":"dropdown-menu.tsx"})}function i(e){let{...t}=e;return(0,a.jsx)(r.l9,{"data-slot":"dropdown-menu-trigger",...t,"data-sentry-element":"DropdownMenuPrimitive.Trigger","data-sentry-component":"DropdownMenuTrigger","data-sentry-source-file":"dropdown-menu.tsx"})}function d(e){let{className:t,sideOffset:s=4,...n}=e;return(0,a.jsx)(r.ZL,{"data-sentry-element":"DropdownMenuPrimitive.Portal","data-sentry-component":"DropdownMenuContent","data-sentry-source-file":"dropdown-menu.tsx",children:(0,a.jsx)(r.UC,{"data-slot":"dropdown-menu-content",sideOffset:s,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...n,"data-sentry-element":"DropdownMenuPrimitive.Content","data-sentry-source-file":"dropdown-menu.tsx"})})}function c(e){let{...t}=e;return(0,a.jsx)(r.YJ,{"data-slot":"dropdown-menu-group",...t,"data-sentry-element":"DropdownMenuPrimitive.Group","data-sentry-component":"DropdownMenuGroup","data-sentry-source-file":"dropdown-menu.tsx"})}function u(e){let{className:t,inset:s,variant:n="default",...l}=e;return(0,a.jsx)(r.q7,{"data-slot":"dropdown-menu-item","data-inset":s,"data-variant":n,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground focus:[&_svg:not([class*='text-'])]:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,"data-sentry-element":"DropdownMenuPrimitive.Item","data-sentry-component":"DropdownMenuItem","data-sentry-source-file":"dropdown-menu.tsx"})}function m(e){let{className:t,children:s,checked:l,...i}=e;return(0,a.jsxs)(r.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:l,...i,"data-sentry-element":"DropdownMenuPrimitive.CheckboxItem","data-sentry-component":"DropdownMenuCheckboxItem","data-sentry-source-file":"dropdown-menu.tsx",children:[(0,a.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{"data-sentry-element":"DropdownMenuPrimitive.ItemIndicator","data-sentry-source-file":"dropdown-menu.tsx",children:(0,a.jsx)(n.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"dropdown-menu.tsx"})})}),s]})}function x(e){let{className:t,inset:s,...n}=e;return(0,a.jsx)(r.JU,{"data-slot":"dropdown-menu-label","data-inset":s,className:(0,o.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...n,"data-sentry-element":"DropdownMenuPrimitive.Label","data-sentry-component":"DropdownMenuLabel","data-sentry-source-file":"dropdown-menu.tsx"})}function f(e){let{className:t,...s}=e;return(0,a.jsx)(r.wv,{"data-slot":"dropdown-menu-separator",className:(0,o.cn)("bg-border -mx-1 my-1 h-px",t),...s,"data-sentry-element":"DropdownMenuPrimitive.Separator","data-sentry-component":"DropdownMenuSeparator","data-sentry-source-file":"dropdown-menu.tsx"})}},20764:(e,t,s)=>{"use strict";s.d(t,{$:()=>i,r:()=>l});var a=s(95155);s(12115);var r=s(32467),n=s(83101),o=s(64269);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:s,size:n,asChild:i=!1,...d}=e,c=i?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,o.cn)(l({variant:s,size:n,className:t})),...d,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},25532:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>x,gC:()=>m,l6:()=>d,yv:()=>c});var a=s(95155);s(12115);var r=s(47887),n=s(24033),o=s(5917),l=s(12108),i=s(64269);function d(e){let{...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"select",...t,"data-sentry-element":"SelectPrimitive.Root","data-sentry-component":"Select","data-sentry-source-file":"select.tsx"})}function c(e){let{...t}=e;return(0,a.jsx)(r.WT,{"data-slot":"select-value",...t,"data-sentry-element":"SelectPrimitive.Value","data-sentry-component":"SelectValue","data-sentry-source-file":"select.tsx"})}function u(e){let{className:t,size:s="default",children:o,...l}=e;return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":s,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,"data-sentry-element":"SelectPrimitive.Trigger","data-sentry-component":"SelectTrigger","data-sentry-source-file":"select.tsx",children:[o,(0,a.jsx)(r.In,{asChild:!0,"data-sentry-element":"SelectPrimitive.Icon","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(n.A,{className:"size-4 opacity-50","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})]})}function m(e){let{className:t,children:s,position:n="popper",...o}=e;return(0,a.jsx)(r.ZL,{"data-sentry-element":"SelectPrimitive.Portal","data-sentry-component":"SelectContent","data-sentry-source-file":"select.tsx",children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...o,"data-sentry-element":"SelectPrimitive.Content","data-sentry-source-file":"select.tsx",children:[(0,a.jsx)(f,{"data-sentry-element":"SelectScrollUpButton","data-sentry-source-file":"select.tsx"}),(0,a.jsx)(r.LM,{className:(0,i.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),"data-sentry-element":"SelectPrimitive.Viewport","data-sentry-source-file":"select.tsx",children:s}),(0,a.jsx)(p,{"data-sentry-element":"SelectScrollDownButton","data-sentry-source-file":"select.tsx"})]})})}function x(e){let{className:t,children:s,...n}=e;return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,"data-sentry-element":"SelectPrimitive.Item","data-sentry-component":"SelectItem","data-sentry-source-file":"select.tsx",children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{"data-sentry-element":"SelectPrimitive.ItemIndicator","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(o.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"select.tsx"})})}),(0,a.jsx)(r.p4,{"data-sentry-element":"SelectPrimitive.ItemText","data-sentry-source-file":"select.tsx",children:s})]})}function f(e){let{className:t,...s}=e;return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...s,"data-sentry-element":"SelectPrimitive.ScrollUpButton","data-sentry-component":"SelectScrollUpButton","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(l.A,{className:"size-4","data-sentry-element":"ChevronUpIcon","data-sentry-source-file":"select.tsx"})})}function p(e){let{className:t,...s}=e;return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...s,"data-sentry-element":"SelectPrimitive.ScrollDownButton","data-sentry-component":"SelectScrollDownButton","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(n.A,{className:"size-4","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})}},31936:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(95155);s(12115);var r=s(64269);function n(e){let{className:t,type:s,...n}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},38004:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>u,Es:()=>x,L3:()=>f,c7:()=>m,lG:()=>l,rr:()=>p,zM:()=>i});var a=s(95155);s(12115);var r=s(89511),n=s(65229),o=s(64269);function l(e){let{...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"dialog",...t,"data-sentry-element":"DialogPrimitive.Root","data-sentry-component":"Dialog","data-sentry-source-file":"dialog.tsx"})}function i(e){let{...t}=e;return(0,a.jsx)(r.l9,{"data-slot":"dialog-trigger",...t,"data-sentry-element":"DialogPrimitive.Trigger","data-sentry-component":"DialogTrigger","data-sentry-source-file":"dialog.tsx"})}function d(e){let{...t}=e;return(0,a.jsx)(r.ZL,{"data-slot":"dialog-portal",...t,"data-sentry-element":"DialogPrimitive.Portal","data-sentry-component":"DialogPortal","data-sentry-source-file":"dialog.tsx"})}function c(e){let{className:t,...s}=e;return(0,a.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...s,"data-sentry-element":"DialogPrimitive.Overlay","data-sentry-component":"DialogOverlay","data-sentry-source-file":"dialog.tsx"})}function u(e){let{className:t,children:s,...l}=e;return(0,a.jsxs)(d,{"data-slot":"dialog-portal","data-sentry-element":"DialogPortal","data-sentry-component":"DialogContent","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(c,{"data-sentry-element":"DialogOverlay","data-sentry-source-file":"dialog.tsx"}),(0,a.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...l,"data-sentry-element":"DialogPrimitive.Content","data-sentry-source-file":"dialog.tsx",children:[s,(0,a.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","data-sentry-element":"DialogPrimitive.Close","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(n.A,{"data-sentry-element":"XIcon","data-sentry-source-file":"dialog.tsx"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",t),...s,"data-sentry-component":"DialogHeader","data-sentry-source-file":"dialog.tsx"})}function x(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...s,"data-sentry-component":"DialogFooter","data-sentry-source-file":"dialog.tsx"})}function f(e){let{className:t,...s}=e;return(0,a.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",t),...s,"data-sentry-element":"DialogPrimitive.Title","data-sentry-component":"DialogTitle","data-sentry-source-file":"dialog.tsx"})}function p(e){let{className:t,...s}=e;return(0,a.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...s,"data-sentry-element":"DialogPrimitive.Description","data-sentry-component":"DialogDescription","data-sentry-source-file":"dialog.tsx"})}},47886:(e,t,s)=>{"use strict";s.d(t,{WG:()=>r,cl:()=>o,qs:()=>a});let a={setUser:e=>{localStorage.setItem("auth_user",JSON.stringify(e))},getUser:()=>{{let e=localStorage.getItem("auth_user");return e?JSON.parse(e):null}},removeUser:()=>{localStorage.removeItem("auth_user")},isAuthenticated:()=>null!==a.getUser(),hasRole:e=>{let t=a.getUser();return(null==t?void 0:t.role)===e},isSuperAdmin:()=>a.hasRole("super_admin"),isTeacher:()=>a.hasRole("teacher"),isStudent:()=>a.hasRole("student")},r=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},n=()=>{let e=a.getUser();return e||(window.location.href="/auth/sign-in",null)},o=e=>{let t=n();return t?t.role!==e?(window.location.href=r(t),null):t:null}},64269:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n,z:()=>o});var a=s(2821),r=s(75889);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}function o(e){var t,s;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:r=0,sizeType:n="normal"}=a;if(0===e)return"0 Byte";let o=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,o)).toFixed(r)," ").concat("accurate"===n?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][o])?t:"Bytest":null!=(s=["Bytes","KB","MB","GB","TB"][o])?s:"Bytes")}},66094:(e,t,s)=>{"use strict";s.d(t,{BT:()=>i,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>o,wL:()=>c});var a=s(95155);s(12115);var r=s(64269);function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},81315:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>T});var a=s(95155),r=s(12115),n=s(20063),o=s(66094),l=s(20764),i=s(31936),d=s(88864),c=s(12833),u=s(38004),m=s(25532),x=s(88021),f=s(88941),p=s(35626),g=s(47937),h=s(6191),y=s(86651),v=s(91169),b=s(16485),j=s(37772),w=s(57828),N=s(71360),C=s(52619),D=s.n(C),k=s(47886),S=s(18720);function T(){let e=(0,n.useRouter)(),t=(0,n.useParams)().id,[s,C]=(0,r.useState)(!0),[T,P]=(0,r.useState)(null),[I,_]=(0,r.useState)([]),[z,A]=(0,r.useState)([]),[B,R]=(0,r.useState)(""),[M,E]=(0,r.useState)(null),[L,H]=(0,r.useState)(!1),[U,F]=(0,r.useState)(""),[O,V]=(0,r.useState)(!1);(0,r.useEffect)(()=>{t&&Z()},[t]);let Z=async()=>{try{let s=k.qs.getUser();if(!s){S.oR.error("Please log in to view class courses"),e.push("/auth/sign-in");return}let a=await fetch("/api/classes/".concat(t,"?teacherId=").concat(s.id)),r=await a.json();if(r.success&&r.class)P(r.class);else{S.oR.error(r.error||"Failed to fetch class data"),e.push("/dashboard/teacher/classes");return}let n=await fetch("/api/enrollments?type=course&classId=".concat(t,"&teacherId=").concat(s.id));if(n.ok){let e=await n.json();_(e.enrollments||[])}let o=await fetch("/api/courses?teacherId=".concat(s.id));if(o.ok){let e=await o.json();A(e.courses||[])}}catch(e){console.error("Error fetching data:",e),S.oR.error("Failed to fetch class courses")}finally{C(!1)}},$=async e=>{try{let t=k.qs.getUser();if(!t)return void S.oR.error("Please log in to remove course assignments");E(e);let s=await fetch("/api/enrollments/".concat(e,"?teacherId=").concat(t.id,"&type=course"),{method:"DELETE",headers:{"Content-Type":"application/json"}}),a=await s.json();a.success?(S.oR.success("Course assignment removed successfully!"),_(t=>t.filter(t=>t.id!==e))):S.oR.error(a.error||"Failed to remove course assignment")}catch(e){console.error("Error removing assignment:",e),S.oR.error("Failed to remove course assignment")}finally{E(null)}},q=async()=>{if(!U)return void S.oR.error("Please select a course");let e=k.qs.getUser();if(!e||"teacher"!==e.role)return void S.oR.error("Access denied");V(!0);try{let s=await fetch("/api/enrollments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:"course",courseId:parseInt(U),classId:parseInt(t),teacherId:e.id})});if(s.ok)S.oR.success("Course assigned to class successfully"),H(!1),F(""),Z();else{let e=await s.json();S.oR.error(e.error||"Failed to assign course")}}catch(e){console.error("Error assigning course:",e),S.oR.error("An error occurred while assigning course")}finally{V(!1)}},G=I.filter(e=>e.courseName.toLowerCase().includes(B.toLowerCase())||e.courseCode.toLowerCase().includes(B.toLowerCase())||e.courseDescription.toLowerCase().includes(B.toLowerCase()));return s?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(f.E,{className:"h-10 w-20"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(f.E,{className:"h-8 w-64"}),(0,a.jsx)(f.E,{className:"h-4 w-96"})]})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsx)(f.E,{className:"h-6 w-48"}),(0,a.jsx)(f.E,{className:"h-4 w-64"})]}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(f.E,{className:"h-10 w-full"}),(0,a.jsx)("div",{className:"space-y-2",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,a.jsx)(f.E,{className:"h-16 w-full"},t))})]})})]})]}):T?(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"ClassCoursesPage","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(D(),{href:"/dashboard/teacher/classes/".concat(t),"data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(l.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ArrowLeft","data-sentry-source-file":"page.tsx"}),"Back"]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold tracking-tight",children:[T.name," - Assigned Courses"]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage courses assigned to this class"})]})]}),(0,a.jsxs)(o.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(o.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(g.A,{className:"h-5 w-5","data-sentry-element":"BookOpen","data-sentry-source-file":"page.tsx"}),"Class Information"]})}),(0,a.jsx)(o.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:T.studentCount}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Students"})]}),(0,a.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:I.length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Assigned Courses"})]}),(0,a.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:[I.length>0?Math.round(I.reduce((e,t)=>e+t.completionRate,0)/I.length):0,"%"]}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Avg. Completion"})]})]})})]}),(0,a.jsxs)(o.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(o.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(o.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Assigned Courses"}),(0,a.jsxs)(o.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:["Courses currently assigned to ",T.name]})]}),(0,a.jsxs)(l.$,{onClick:()=>H(!0),"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Plus","data-sentry-source-file":"page.tsx"}),"Assign Course"]})]})}),(0,a.jsx)(o.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4","data-sentry-element":"Search","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(i.p,{placeholder:"Search assigned courses...",value:B,onChange:e=>R(e.target.value),className:"pl-10","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),G.length>0?(0,a.jsxs)(d.Table,{children:[(0,a.jsx)(d.TableHeader,{children:(0,a.jsxs)(d.TableRow,{children:[(0,a.jsx)(d.TableHead,{children:"Course"}),(0,a.jsx)(d.TableHead,{children:"Code"}),(0,a.jsx)(d.TableHead,{children:"Students"}),(0,a.jsx)(d.TableHead,{children:"Completion"}),(0,a.jsx)(d.TableHead,{children:"Assigned Date"}),(0,a.jsx)(d.TableHead,{className:"text-right",children:"Actions"})]})}),(0,a.jsx)(d.TableBody,{children:G.map(e=>(0,a.jsxs)(d.TableRow,{children:[(0,a.jsx)(d.TableCell,{children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.courseName}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground line-clamp-1",children:e.courseDescription})]})}),(0,a.jsx)(d.TableCell,{children:(0,a.jsx)(x.E,{variant:"outline",children:e.courseCode})}),(0,a.jsx)(d.TableCell,{children:(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"}),e.studentCount]})}),(0,a.jsx)(d.TableCell,{children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-16 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"".concat(e.completionRate,"%")}})}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.completionRate,"%"]})]})}),(0,a.jsx)(d.TableCell,{children:(0,a.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),new Date(e.enrolledAt).toLocaleDateString()]})}),(0,a.jsx)(d.TableCell,{className:"text-right",children:(0,a.jsxs)(c.rI,{children:[(0,a.jsx)(c.ty,{asChild:!0,children:(0,a.jsx)(l.$,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,a.jsx)(j.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(c.SQ,{align:"end",children:[(0,a.jsx)(c._2,{asChild:!0,children:(0,a.jsxs)(D(),{href:"/dashboard/teacher/courses/".concat(e.courseId),children:[(0,a.jsx)(w.A,{className:"mr-2 h-4 w-4"}),"View Course"]})}),(0,a.jsxs)(c._2,{className:"text-red-600",onClick:()=>$(e.id),disabled:M===e.id,children:[M===e.id?(0,a.jsx)("div",{className:"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-red-600 border-t-transparent"}):(0,a.jsx)(N.A,{className:"mr-2 h-4 w-4"}),"Remove Assignment"]})]})]})})]},e.id))})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(g.A,{className:"mx-auto h-12 w-12 text-muted-foreground mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No courses assigned"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:B?"No courses match your search criteria.":"This class doesn't have any assigned courses yet."}),!B&&(0,a.jsxs)(l.$,{onClick:()=>H(!0),children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Assign First Course"]})]})]})})]}),(0,a.jsx)(u.lG,{open:L,onOpenChange:H,"data-sentry-element":"Dialog","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(u.Cf,{className:"sm:max-w-[425px]","data-sentry-element":"DialogContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(u.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(u.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"page.tsx",children:["Assign Course to ",null==T?void 0:T.name]}),(0,a.jsx)(u.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"page.tsx",children:"Select a course to assign to this class."})]}),(0,a.jsx)("div",{className:"grid gap-4 py-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Course"}),(0,a.jsxs)(m.l6,{value:U,onValueChange:F,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(m.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(m.yv,{placeholder:"Select a course","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,a.jsx)(m.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:z.filter(e=>!I.some(t=>t.courseId===e.id)).map(e=>(0,a.jsx)(m.eb,{value:e.id.toString(),children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:e.courseCode})]})},e.id))})]})]})}),(0,a.jsxs)(u.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(l.$,{variant:"outline",onClick:()=>{H(!1),F("")},disabled:O,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Cancel"}),(0,a.jsx)(l.$,{onClick:q,disabled:O,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:O?"Assigning...":"Assign Course"})]})]})})]}):(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold",children:"Class not found"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-2",children:"The class you're looking for doesn't exist."}),(0,a.jsx)(D(),{href:"/dashboard/teacher/classes",children:(0,a.jsxs)(l.$,{className:"mt-4",children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Back to Classes"]})})]})})}},88021:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var a=s(95155);s(12115);var r=s(32467),n=s(83101),o=s(64269);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,asChild:n=!1,...i}=e,d=n?r.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,o.cn)(l({variant:s}),t),...i,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},88864:(e,t,s)=>{"use strict";s.d(t,{Table:()=>n,TableBody:()=>l,TableCell:()=>c,TableHead:()=>d,TableHeader:()=>o,TableRow:()=>i});var a=s(95155);s(12115);var r=s(64269);function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto","data-sentry-component":"Table","data-sentry-source-file":"table.tsx",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",t),...s})})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",t),...s,"data-sentry-component":"TableHeader","data-sentry-source-file":"table.tsx"})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",t),...s,"data-sentry-component":"TableBody","data-sentry-source-file":"table.tsx"})}function i(e){let{className:t,...s}=e;return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...s,"data-sentry-component":"TableRow","data-sentry-source-file":"table.tsx"})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...s,"data-sentry-component":"TableHead","data-sentry-source-file":"table.tsx"})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...s,"data-sentry-component":"TableCell","data-sentry-source-file":"table.tsx"})}},88941:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var a=s(95155),r=s(64269);function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"skeleton",className:(0,r.cn)("bg-accent animate-pulse rounded-md",t),...s,"data-sentry-component":"Skeleton","data-sentry-source-file":"skeleton.tsx"})}},93261:(e,t,s)=>{Promise.resolve().then(s.bind(s,81315))}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,7055,4736,660,8720,6093,9568,7971,2248,4850,8441,3840,7358],()=>t(93261)),_N_E=e.O()}]);