try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="484e5740-222f-4f51-92fc-4cf3be6eb32c",e._sentryDebugIdIdentifier="sentry-dbid-484e5740-222f-4f51-92fc-4cf3be6eb32c")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[401],{4662:(e,t,n)=>{"use strict";n.d(t,{Fc:()=>o,TN:()=>c,XL:()=>l});var s=n(95155);n(12115);var r=n(83101),a=n(64269);let i=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:n,...r}=e;return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,a.cn)(i({variant:n}),t),...r,"data-sentry-component":"Alert","data-sentry-source-file":"alert.tsx"})}function l(e){let{className:t,...n}=e;return(0,s.jsx)("div",{"data-slot":"alert-title",className:(0,a.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...n,"data-sentry-component":"AlertTitle","data-sentry-source-file":"alert.tsx"})}function c(e){let{className:t,...n}=e;return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,a.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...n,"data-sentry-component":"AlertDescription","data-sentry-source-file":"alert.tsx"})}},20764:(e,t,n)=>{"use strict";n.d(t,{$:()=>l,r:()=>o});var s=n(95155);n(12115);var r=n(32467),a=n(83101),i=n(64269);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:n,size:a,asChild:l=!1,...c}=e,d=l?r.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:n,size:a,className:t})),...c,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},26737:(e,t,n)=>{"use strict";n.d(t,{k:()=>o});var s=n(95155),r=n(12115),a=n(9484),i=n(64269);let o=r.forwardRef((e,t)=>{let{className:n,value:r,...o}=e;return(0,s.jsx)(a.bL,{ref:t,className:(0,i.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",n),...o,children:(0,s.jsx)(a.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});o.displayName=a.bL.displayName},27223:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>eu});var s=n(95155),r=n(12115),a=n(20063),i=n(20764),o=n(66094),l=n(31936),c=n(42526),d=n(47254),u=n(26737),m=n(46201),p=n(4662),h=n(21786),g=n(89715),x=n(42529),y=n(35299),f=n(1473),v=n(90799),w=n(26983),N=n(47937),b=n(30814),R=n(89442),I=n(6132),A=n(78874),E=n(6191),j=n(7125),C=n(18720);class S extends Error{constructor(e,t,n){super(e),this.code=t,this.details=n,this.name="AIGenerationError"}}var O=n(12014),T=n(15376).Buffer;let q=null,U=new Map,z=()=>{if(q)return q;let e="AIzaSyA-2p3GQJYEPNVodSRp20m2spI4Zwthp6s";if(!e)throw new S("Gemini API key is not configured. Please set NEXT_PUBLIC_GEMINI_API_KEY environment variable.","MISSING_API_KEY");try{return q=new O.M4({apiKey:e})}catch(e){throw new S("Failed to initialize Gemini AI client","CLIENT_INIT_ERROR",e)}},_=async e=>{try{let t=await e.arrayBuffer();return T.from(t).toString("base64")}catch(e){throw new S("Failed to convert file to base64","FILE_CONVERSION_ERROR",e)}},G=async function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"gemini-2.5-flash";try{let s=z(),r=(await s.models.generateContent({model:n,contents:e,config:{...t,thinkingConfig:{thinkingBudget:0}}})).text||"";if(!r.trim())throw Error("Empty response from Gemini API");try{let e=JSON.parse(r);return console.log("[Gemini Structured Output - Parsed]",e),e}catch(e){throw Error("Failed to parse structured output: ".concat(e))}}catch(e){throw new S("Failed to generate structured content with Gemini API","STRUCTURED_GENERATION_ERROR",e)}},D=async function(e,t,n){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"gemini-2.5-flash";try{let r=z(),a=await r.models.generateContentStream({model:s,contents:e,config:{...t,thinkingConfig:{thinkingBudget:0}}}),i="";for await(let e of a){let t=e.text||"";t&&(i+=t,n&&n(t))}if(!i.trim())throw Error("Empty response from Gemini API");try{let e=JSON.parse(i);return console.log("[Gemini Structured Stream - Final Parsed]",e),e}catch(e){throw Error("Failed to parse structured output: ".concat(e))}}catch(e){throw new S("Failed to generate structured content with Gemini API streaming","STRUCTURED_STREAM_ERROR",e)}},Z=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"gemini-2.5-flash";try{let s=z().chats.create({model:n,history:t}),r={id:e,chat:s,history:[...t],createdAt:new Date};return U.set(e,r),r}catch(e){throw new S("Failed to create chat session","CHAT_SESSION_ERROR",e)}},M=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"gemini-2.5-flash",s=U.get(e);return s||Z(e,t,n)},L=async(e,t,n,s)=>{try{let r,a;console.group("[sendStructuredChatMessage] Session ID: ".concat(e,", Schema: ").concat(n?"YES":"NO"));let i=U.get(e);if(!i)throw new S("Chat session not found: ".concat(e),"SESSION_NOT_FOUND");i.history.push(t);let o=i.history;if(console.log("--\x3e \uD83D\uDCE9 Sending history to Gemini:"),console.table(o),n)if(s){console.log("\uD83D\uDD27 Using STRUCTURED STREAMING output with schema");let e=await D(o,n,s);r=JSON.stringify(e),a=e,console.log("<-- \uD83D\uDCC4 Received parsed structured streaming response from Gemini:"),console.log(e)}else{console.log("\uD83D\uDD27 Using STRUCTURED output with schema");let e=await G(o,n);r=JSON.stringify(e),a=e,console.log("<-- \uD83D\uDCC4 Received parsed structured response from Gemini:"),console.log(e)}else if(s){console.log("\uD83D\uDCDD Using REGULAR STREAMING text output (no schema)");let e=z(),t=await e.models.generateContentStream({model:"gemini-2.5-flash",contents:o,config:{thinkingConfig:{thinkingBudget:0}}}),n="";for await(let e of t){let t=e.text||"";t&&(n+=t,s(t))}a=r=n,console.log("<-- \uD83D\uDCDD Received streaming raw text response from Gemini:"),console.log(r)}else{console.log("\uD83D\uDCDD Using REGULAR text output (no schema)");let e=z();a=r=(await e.models.generateContent({model:"gemini-2.5-flash",contents:o,config:{thinkingConfig:{thinkingBudget:0}}})).text||"",console.log("<-- \uD83D\uDCDD Received raw text response from Gemini:"),console.log(r)}if(!r.trim())throw Error("Empty response from Gemini API");return i.history.push({role:"model",parts:[{text:r}]}),console.log("✅ History updated successfully."),a}catch(e){if(console.error("[sendStructuredChatMessage] Error:",e),e instanceof S)throw e;throw new S("Failed to send chat message","CHAT_MESSAGE_ERROR",e)}finally{console.groupEnd()}},B={responseMimeType:"application/json",responseSchema:{type:O.ZU.OBJECT,properties:{courseName:{type:O.ZU.STRING,description:"Clear, descriptive course title"},description:{type:O.ZU.STRING,description:"Comprehensive course description (2-3 sentences)"},modules:{type:O.ZU.ARRAY,items:{type:O.ZU.OBJECT,properties:{name:{type:O.ZU.STRING,description:"Module title"},description:{type:O.ZU.STRING,description:"Module description (1-2 sentences)"},chapters:{type:O.ZU.ARRAY,items:{type:O.ZU.OBJECT,properties:{name:{type:O.ZU.STRING,description:"Chapter title"},description:{type:O.ZU.STRING,description:"Chapter description (1 sentence)"},hasQuiz:{type:O.ZU.BOOLEAN,description:"Whether this chapter has a quiz"}},required:["name","description","hasQuiz"]}},hasModuleQuiz:{type:O.ZU.BOOLEAN,description:"Whether this module has a quiz"}},required:["name","description","chapters","hasModuleQuiz"]}},hasFinalExam:{type:O.ZU.BOOLEAN,description:"Whether the course has a final exam"}},required:["courseName","description","modules","hasFinalExam"]}},k=async(e,t,n)=>{try{let s=await _(e),r=F(n);M(t);let a=await L(t,{role:"user",parts:[{text:r},{inlineData:{mimeType:"application/pdf",data:s}}]},B);if("string"==typeof a)throw new S("AI response was not a valid course outline object","OUTLINE_GENERATION_ERROR");return P(a),a}catch(e){if(e instanceof S)throw e;throw new S("Failed to generate course outline from PDF","OUTLINE_GENERATION_ERROR",e)}},F=e=>{let t="";return e&&(t="\nAdditional Requirements:\n",e.courseName&&(t+='- Course name should be related to: "'.concat(e.courseName,'"\n')),e.courseDescription&&(t+='- Course should focus on: "'.concat(e.courseDescription,'"\n')),e.targetAudience&&(t+="- Target audience: ".concat(e.targetAudience,"\n")),e.difficulty&&(t+="- Difficulty level: ".concat(e.difficulty,"\n"))),"\nAnalyze the provided document and create a comprehensive course outline.\n\nGuidelines:\n- Create a maximum of 10 modules with clear, descriptive names\n- Each module should have 2-5 chapters\n- the amount of modules and chapters depend on the content of the document\n- Chapter names should be specific and actionable\n- Every chapter should include a quiz\n- Every module should include a module quiz\n- Final exam should be included for comprehensive courses\n- Ensure logical progression from basic to advanced concepts\n"+t},P=e=>{if(!e.courseName||"string"!=typeof e.courseName)throw new S("Invalid course outline: missing or invalid courseName","VALIDATION_ERROR");if(!e.description||"string"!=typeof e.description)throw new S("Invalid course outline: missing or invalid description","VALIDATION_ERROR");if(!Array.isArray(e.modules)||0===e.modules.length)throw new S("Invalid course outline: modules must be a non-empty array","VALIDATION_ERROR");if(e.modules.forEach((e,t)=>{if(!e.name||"string"!=typeof e.name)throw new S("Invalid module at index ".concat(t,": missing or invalid name"),"VALIDATION_ERROR");if(!e.description||"string"!=typeof e.description)throw new S("Invalid module at index ".concat(t,": missing or invalid description"),"VALIDATION_ERROR");if(!Array.isArray(e.chapters)||0===e.chapters.length)throw new S("Invalid module at index ".concat(t,": chapters must be a non-empty array"),"VALIDATION_ERROR");if(e.chapters.forEach((e,n)=>{if(!e.name||"string"!=typeof e.name)throw new S("Invalid chapter at module ".concat(t,", chapter ").concat(n,": missing or invalid name"),"VALIDATION_ERROR");if(!e.description||"string"!=typeof e.description)throw new S("Invalid chapter at module ".concat(t,", chapter ").concat(n,": missing or invalid description"),"VALIDATION_ERROR");if("boolean"!=typeof e.hasQuiz)throw new S("Invalid chapter at module ".concat(t,", chapter ").concat(n,": hasQuiz must be boolean"),"VALIDATION_ERROR")}),"boolean"!=typeof e.hasModuleQuiz)throw new S("Invalid module at index ".concat(t,": hasModuleQuiz must be boolean"),"VALIDATION_ERROR")}),"boolean"!=typeof e.hasFinalExam)throw new S("Invalid course outline: hasFinalExam must be boolean","VALIDATION_ERROR")},Q={responseMimeType:"application/json",responseSchema:{type:O.ZU.OBJECT,properties:{content:{type:O.ZU.ARRAY,items:{type:O.ZU.OBJECT,properties:{id:{type:O.ZU.NUMBER},type:{type:O.ZU.STRING,enum:["text"]},value:{type:O.ZU.STRING,description:"Markdown content"}},required:["id","type","value"]}},quiz:{type:O.ZU.OBJECT,properties:{name:{type:O.ZU.STRING},description:{type:O.ZU.STRING},timeLimit:{type:O.ZU.NUMBER},minimumScore:{type:O.ZU.NUMBER},questions:{type:O.ZU.ARRAY,items:{type:O.ZU.OBJECT,properties:{orderIndex:{type:O.ZU.NUMBER},type:{type:O.ZU.STRING,enum:["multiple_choice","true_false","essay"]},question:{type:O.ZU.ARRAY,items:{type:O.ZU.OBJECT,properties:{id:{type:O.ZU.NUMBER},type:{type:O.ZU.STRING,enum:["text"]},value:{type:O.ZU.STRING}},required:["id","type","value"]}},options:{type:O.ZU.ARRAY,items:{type:O.ZU.OBJECT,properties:{id:{type:O.ZU.NUMBER},isCorrect:{type:O.ZU.BOOLEAN},content:{type:O.ZU.ARRAY,items:{type:O.ZU.OBJECT,properties:{id:{type:O.ZU.NUMBER},type:{type:O.ZU.STRING,enum:["text"]},value:{type:O.ZU.STRING}},required:["id","type","value"]}}},required:["id","isCorrect","content"]}},essayAnswer:{type:O.ZU.STRING},explanation:{type:O.ZU.ARRAY,items:{type:O.ZU.OBJECT,properties:{id:{type:O.ZU.NUMBER},type:{type:O.ZU.STRING,enum:["text"]},value:{type:O.ZU.STRING}},required:["id","type","value"]}},points:{type:O.ZU.NUMBER}},required:["orderIndex","type","question","points","essayAnswer","explanation"]}}},required:["name","description","timeLimit","minimumScore","questions"]}},required:["content","quiz"]}},V=async(e,t,n,s,r)=>{try{let a=J(e,t,n);M(s);let i=await L(s,{role:"user",parts:[{text:a}]},Q,r);if("object"!=typeof i||null===i)throw new S("Failed to generate valid content for chapter: ".concat(e.name),"CONTENT_GENERATION_ERROR");return Y(i,e.hasQuiz),i}catch(t){if(t instanceof S)throw t;throw new S("Failed to generate content for chapter: ".concat(e.name),"CONTENT_GENERATION_ERROR",t)}},J=(e,t,n)=>'\nGenerate comprehensive educational content for a chapter in an online course using the PDF content that i sent before.\n\nCourse Context:\n- Course: "'.concat(n.courseName,'"\n- Course Description: ').concat(n.description,'\n\nModule Context:\n- Module: "').concat(t.name,'"\n- Module Description: ').concat(t.description,'\n\nChapter Details:\n- Chapter: "').concat(e.name,'"\n- Chapter Description: ').concat(e.description,"\n- Needs Quiz: ").concat(e.hasQuiz,"\n\nGenerate comprehensive chapter content with the following requirements:\n\nContent Guidelines:\n- Write comprehensive, educational content in Markdown format\n- A Content can consist of many sections (heading)\n- Include clear headings, subheadings, and structure\n- Use examples, analogies, and practical applications\n- Make content engaging and easy to understand\n- Aim for 800-1500 words of substantial content\n- Use bullet points, numbered lists, and formatting for clarity\n- Always include a quiz if it needs one\n\n\nQuiz Guidelines (if Needs Quiz is true):\n- Create 10 questions focused specifically on this chapter\n- Questions should test key concepts and understanding from the chapter\n- Mix question types: 70% multiple choice, 20% true/false, 10% essay\n- For multiple choice: provide 4 options with only one correct answer\n- For true/false: make statements that are clearly true or false\n- For essay: ask questions that require understanding of chapter concepts\n- Set time limit between 5-15 minutes\n- Set minimum score between 70-80%\n- Distribute points based on question difficulty\n"),Y=(e,t)=>{if(!e.content||!Array.isArray(e.content)||0===e.content.length)throw new S("Invalid generated content: missing or invalid content field","VALIDATION_ERROR");if(e.content.map(e=>e.value).join("\n").trim().length<100)throw new S("Invalid generated content: content is too short","VALIDATION_ERROR");if(t){if(!e.quiz)throw new S("Invalid generated content: quiz is required but missing","VALIDATION_ERROR");W(e.quiz)}else if(null!==e.quiz&&void 0!==e.quiz)throw new S("Invalid generated content: quiz should be null when not required","VALIDATION_ERROR")},W=e=>{if(!e.name||"string"!=typeof e.name)throw new S("Invalid quiz: missing or invalid name","VALIDATION_ERROR");if(!e.description||"string"!=typeof e.description)throw new S("Invalid quiz: missing or invalid description","VALIDATION_ERROR");if("number"!=typeof e.timeLimit||e.timeLimit<=0)throw new S("Invalid quiz: timeLimit must be a positive number","VALIDATION_ERROR");if("number"!=typeof e.minimumScore||e.minimumScore<0||e.minimumScore>100)throw new S("Invalid quiz: minimumScore must be a number between 0 and 100","VALIDATION_ERROR");if(!Array.isArray(e.questions)||0===e.questions.length)throw new S("Invalid quiz: questions must be a non-empty array","VALIDATION_ERROR");e.questions.forEach((e,t)=>{if(!["multiple_choice","true_false","essay"].includes(e.type))throw new S("Invalid question at index ".concat(t,": type must be multiple_choice, true_false, or essay"),"VALIDATION_ERROR");if(!Array.isArray(e.question)||0===e.question.length)throw new S("Invalid question at index ".concat(t,": missing or invalid question text"),"VALIDATION_ERROR");if("multiple_choice"===e.type&&(!Array.isArray(e.options)||e.options.length<2))throw new S("Invalid question at index ".concat(t,": multiple choice questions must have at least 2 options"),"VALIDATION_ERROR");if("essay"===e.type){if(!e.essayAnswer||"string"!=typeof e.essayAnswer)throw new S("Invalid question at index ".concat(t,": missing or invalid essayAnswer"),"VALIDATION_ERROR")}else if("multiple_choice"===e.type&&(!Array.isArray(e.options)||e.options.length<2||!e.options.some(e=>!0===e.isCorrect)))throw new S("Invalid question at index ".concat(t,": must have options with at least one correct answer"),"VALIDATION_ERROR");if("number"!=typeof e.points||e.points<=0)throw new S("Invalid question at index ".concat(t,": points must be a positive number"),"VALIDATION_ERROR")})},$=(e,t)=>{let n={type:O.ZU.ARRAY,items:{type:O.ZU.OBJECT,properties:{orderIndex:{type:O.ZU.NUMBER,description:"Question order (starting from 1)"},type:{type:O.ZU.STRING,enum:["multiple_choice","true_false","essay"]},question:{type:O.ZU.ARRAY,items:{type:O.ZU.OBJECT,properties:{id:{type:O.ZU.NUMBER,description:"Starting from 1"},type:{type:O.ZU.STRING,enum:["text"]},value:{type:O.ZU.STRING,description:"Markdown formatted question text"}},required:["id","type","value"]}},options:{type:O.ZU.ARRAY,items:{type:O.ZU.OBJECT,properties:{id:{type:O.ZU.NUMBER,description:"Option ID starting from 1"},isCorrect:{type:O.ZU.BOOLEAN},content:{type:O.ZU.ARRAY,items:{type:O.ZU.OBJECT,properties:{id:{type:O.ZU.NUMBER,description:"Starting from 1"},type:{type:O.ZU.STRING,enum:["text"]},value:{type:O.ZU.STRING,description:"Option text"}},required:["id","type","value"]}}},required:["id","isCorrect","content"]}},essayAnswer:{type:O.ZU.STRING,description:"Sample answer for essay questions (empty string for non-essay)"},explanation:{type:O.ZU.ARRAY,items:{type:O.ZU.OBJECT,properties:{id:{type:O.ZU.NUMBER,description:"Starting from 1"},type:{type:O.ZU.STRING,enum:["text"]},value:{type:O.ZU.STRING,description:"Explanation of the correct answer"}},required:["id","type","value"]}},points:{type:O.ZU.NUMBER,description:"Points for this question"}},required:["orderIndex","type","question","points","essayAnswer","explanation"]}};return void 0!==e&&(n.items.minItems=e),void 0!==t&&(n.items.maxItems=t),{responseMimeType:"application/json",responseSchema:{type:O.ZU.OBJECT,properties:{name:{type:O.ZU.STRING,description:"Quiz title"},description:{type:O.ZU.STRING,description:"Quiz description explaining what it covers"},timeLimit:{type:O.ZU.NUMBER,description:"Time limit in minutes"},minimumScore:{type:O.ZU.NUMBER,description:"Minimum score percentage (0-100)"},questions:n},required:["name","description","timeLimit","minimumScore","questions"]}}},H=async(e,t,n,s)=>{try{let r=K(e,t);console.log("DEBUG: Prompt for module quiz:",r),M(n);let a=$(20,20);console.log("DEBUG: Schema for module quiz:",JSON.stringify(a,null,2));let i=await L(n,{role:"user",parts:[{text:r}]},a,s);if("object"!=typeof i||null===i)throw new S("Failed to generate valid quiz for module: ".concat(e.name),"QUIZ_GENERATION_ERROR");return i.name||(i.name="Generated Quiz"),i.description||(i.description="AI generated quiz"),"number"!=typeof i.timeLimit&&(i.timeLimit=30),"number"!=typeof i.minimumScore&&(i.minimumScore=70),Array.isArray(i.questions)||(i.questions=[]),et(i,"module"),i}catch(t){if(console.error("DEBUG: Error generating module quiz:",t),t instanceof S)throw t;throw new S("Failed to generate module quiz for: ".concat(e.name),"QUIZ_GENERATION_ERROR",t)}},X=async(e,t,n)=>{try{let s=ee(e);console.log("DEBUG: Prompt for final exam:",s),M(t);let r=$(40,40);console.log("DEBUG: Schema for final exam:",JSON.stringify(r,null,2));let a=await L(t,{role:"user",parts:[{text:s}]},r,n);if("object"!=typeof a||null===a)throw new S("Failed to generate valid final exam for course: ".concat(e.courseName),"QUIZ_GENERATION_ERROR");return a.name||(a.name="Generated Final Exam"),a.description||(a.description="AI generated final exam"),"number"!=typeof a.timeLimit&&(a.timeLimit=60),"number"!=typeof a.minimumScore&&(a.minimumScore=75),Array.isArray(a.questions)||(a.questions=[]),et(a,"final"),a}catch(t){if(console.error("DEBUG: Error generating final exam:",t),t instanceof S)throw t;throw new S("Failed to generate final exam for course: ".concat(e.courseName),"QUIZ_GENERATION_ERROR",t)}},K=(e,t)=>{let n=e.chapters.map(e=>"- ".concat(e.name,": ").concat(e.description)).join("\n");return'\nGenerate a comprehensive module quiz that tests understanding of an entire module using the PDF content that i sent before with only 20 QUESTIONS.\n\nCourse Context:\n- Course: "'.concat(t.courseName,'"\n- Course Description: ').concat(t.description,'\n\nModule Context:\n- Module: "').concat(e.name,'"\n- Module Description: ').concat(e.description,"\n\nChapters in this module:\n").concat(n,"\n\nGenerate a quiz with the following requirements:\n\nQuiz Guidelines:\n- The quiz MUST contain EXACTLY 20 questions. Do NOT generate more or fewer than 20 questions. (use orderIndex to determine the order of the questions)\n- Questions should cover key concepts from each chapter\n- Mix question types: 60% multiple choice, 30% true/false, 10% essay\n- Ensure questions are challenging but fair\n- For multiple choice: provide 4 options with only one correct answer\n- For true/false: make statements that are clearly true or false\n- For essay: ask questions that require synthesis of module concepts\n- Set time limit between 15-30 minutes\n- Set minimum score between 70-80%\n- Distribute points evenly across questions\n- Total points should be 100\n")},ee=e=>{let t=e.modules.map((e,t)=>{let n=e.chapters.map(e=>"    - ".concat(e.name)).join("\n");return"Module ".concat(t+1,": ").concat(e.name,"\n").concat(n)}).join("\n\n");return'\nGenerate a comprehensive final exam that tests understanding of the entire course using the PDF content that i sent before with only 40 QUESTIONS.\n\nCourse Context:\n- Course: "'.concat(e.courseName,'"\n- Course Description: ').concat(e.description,"\n\nCourse Structure:\n").concat(t,"\n\nGenerate a comprehensive final exam with the following requirements:\n\nExam Guidelines:\n- The exam MUST contain EXACTLY 40 questions. Do NOT generate more or fewer than 40 questions. (use orderIndex to determine the order of the questions)\n- Questions should cover key concepts from all modules proportionally\n- Mix question types: 50% multiple choice, 30% true/false, 20% essay\n- Include questions that require synthesis across multiple modules\n- For multiple choice: provide 4 options with only one correct answer\n- For true/false: make statements that are clearly true or false\n- For essay: ask comprehensive questions that demonstrate mastery\n- Set time limit between 45-90 minutes\n- Set minimum score between 75-85%\n- Distribute points to emphasize important concepts\n- Total points should be 100\n- Include some challenging questions that test deep understanding\n")},et=(e,t)=>{if(!e.name||"string"!=typeof e.name)throw new S("Invalid ".concat(t," quiz: missing or invalid name"),"VALIDATION_ERROR");if(!e.description||"string"!=typeof e.description)throw new S("Invalid ".concat(t," quiz: missing or invalid description"),"VALIDATION_ERROR");if("number"!=typeof e.timeLimit||e.timeLimit<=0)throw new S("Invalid ".concat(t," quiz: timeLimit must be a positive number"),"VALIDATION_ERROR");let{min:n,max:s}={chapter:{min:5,max:15},module:{min:15,max:30},final:{min:45,max:90}}[t];if(e.timeLimit<n||e.timeLimit>s)throw new S("Invalid ".concat(t," quiz: timeLimit should be between ").concat(n,"-").concat(s," minutes"),"VALIDATION_ERROR");if("number"!=typeof e.minimumScore||e.minimumScore<0||e.minimumScore>100)throw new S("Invalid ".concat(t," quiz: minimumScore must be between 0 and 100"),"VALIDATION_ERROR");if(!Array.isArray(e.questions)||0===e.questions.length)throw new S("Invalid ".concat(t," quiz: questions must be a non-empty array"),"VALIDATION_ERROR");let{min:r,max:a}={chapter:{min:3,max:10},module:{min:20,max:20},final:{min:40,max:40}}[t];if(e.questions.length<r)throw new S("Invalid ".concat(t," quiz: should have at least ").concat(r," questions"),"VALIDATION_ERROR");if(e.questions.length>a)throw new S("Invalid ".concat(t," quiz: should have at most ").concat(a," questions"),"VALIDATION_ERROR");e.questions.forEach((e,t)=>{if(!["multiple_choice","true_false","essay"].includes(e.type))throw new S("Invalid question at index ".concat(t,": type must be multiple_choice, true_false, or essay"),"VALIDATION_ERROR");if(!Array.isArray(e.question)||0===e.question.length)throw new S("Invalid question at index ".concat(t,": missing or invalid question text"),"VALIDATION_ERROR");if("multiple_choice"===e.type&&(!Array.isArray(e.options)||e.options.length<2))throw new S("Invalid question at index ".concat(t,": multiple choice questions must have at least 2 options"),"VALIDATION_ERROR");if("essay"===e.type&&(!e.essayAnswer||"string"!=typeof e.essayAnswer))throw new S("Invalid question at index ".concat(t,": essay questions must have essayAnswer"),"VALIDATION_ERROR")})};class en{initializeSteps(){this.steps=[];let e=1;this.courseOutline.modules.forEach((t,n)=>{t.chapters.forEach((s,r)=>{this.steps.push({id:"step-".concat(e++),name:"".concat(t.name," - ").concat(s.name),type:"chapter",status:"pending",moduleIndex:n,chapterIndex:r})}),t.hasModuleQuiz&&this.steps.push({id:"step-".concat(e++),name:"".concat(t.name," - Module Quiz"),type:"quiz",status:"pending",moduleIndex:n})}),this.courseOutline.hasFinalExam&&this.steps.push({id:"step-".concat(e++),name:"Final Exam",type:"final_exam",status:"pending"})}async generateAll(e){let t={chapters:new Map,moduleQuizzes:new Map,finalExam:null,errors:[]};this.progress.isGenerating=!0,this.updateProgress();try{for(let n=0;n<this.steps.length;n++){this.currentStepIndex=n;let s=this.steps[n];this.progress.currentStep=s.name,s.status="generating",this.updateProgress();try{let n=await this.generateStep(s,e);this.storeResult(s,n,t),s.status="completed",this.progress.completedSteps++,this.onStepComplete&&this.onStepComplete(s,n)}catch(n){s.status="error";let e={step:s.name,error:n instanceof Error?n.message:"Unknown error",details:n};t.errors.push(e),console.error("Failed to generate step: ".concat(s.name),n)}this.updateProgress()}}finally{this.progress.isGenerating=!1,this.progress.currentStep="Completed",this.updateProgress()}return t}async generateStep(e,t){switch(e.type){case"chapter":return this.generateChapterStep(e,t);case"quiz":return this.generateModuleQuizStep(e,t);case"final_exam":return this.generateFinalExamStep(t);default:throw new S("Unknown step type: ".concat(e.type),"INVALID_STEP_TYPE")}}async generateChapterStep(e,t){if(void 0===e.moduleIndex||void 0===e.chapterIndex)throw new S("Module and chapter indices are required for chapter generation","MISSING_INDICES");let n=this.courseOutline.modules[e.moduleIndex],s=n.chapters[e.chapterIndex];return await V(s,n,this.courseOutline,this.sessionId,t)}async generateModuleQuizStep(e,t){if(void 0===e.moduleIndex)throw new S("Module index is required for module quiz generation","MISSING_MODULE_INDEX");let n=this.courseOutline.modules[e.moduleIndex];return await H(n,this.courseOutline,this.sessionId,t)}async generateFinalExamStep(e){return await X(this.courseOutline,this.sessionId,e)}storeResult(e,t,n){switch(e.type){case"chapter":if(void 0!==e.moduleIndex&&void 0!==e.chapterIndex){let s="".concat(e.moduleIndex,"-").concat(e.chapterIndex);n.chapters.set(s,t)}break;case"quiz":void 0!==e.moduleIndex&&n.moduleQuizzes.set(e.moduleIndex,t);break;case"final_exam":n.finalExam=t}}updateProgress(){this.onProgressUpdate&&this.onProgressUpdate({...this.progress})}getProgress(){return{...this.progress}}getSteps(){return[...this.steps]}cancel(){this.progress.isGenerating=!1,this.progress.currentStep="Cancelled",this.updateProgress()}constructor(e,t,n,s){var r;this.courseOutline=e,this.steps=[],this.currentStepIndex=0,this.onProgressUpdate=t,this.onStepComplete=n,this.sessionId=s||"course-generation-".concat(Date.now()),this.initializeSteps(),this.progress={currentStep:(null==(r=this.steps[0])?void 0:r.name)||"Initializing",totalSteps:this.steps.length,completedSteps:0,isGenerating:!1}}}let es=async(e,t,n,s,r)=>{let a=new en(e,t,n,s);return await a.generateAll(r)},er=e=>{let t=0;return e.modules.forEach(e=>{t+=e.chapters.length,e.hasModuleQuiz&&(t+=1)}),e.hasFinalExam&&(t+=1),Math.ceil(2.5*t)};var ea=n(62879),ei=n(88021),eo=n(57828),el=n(71408),ec=n(34486);let ed=function(e){let{isStreaming:t,streamingText:n,currentStep:a,className:i=""}=e,l=(0,r.useRef)(null),[c,d]=(0,r.useState)([]),[u,m]=(0,r.useState)([]),p=(0,r.useRef)([]),h=(0,r.useCallback)(()=>{if(0===p.current.length)return;let e={lines:[...p.current],id:"buffer-".concat(Date.now(),"-").concat(Math.random())};d(t=>{let n=[...t,e];return n.length>2?n.slice(-2):n}),m(e=>[...e,...p.current].slice(-10)),p.current=[]},[]);return((0,r.useEffect)(()=>{let e=setInterval(()=>{h()},1e3);return()=>clearInterval(e)},[h]),(0,r.useEffect)(()=>{if(!n.trim()){d([]),m([]),p.current=[];return}p.current=n.split("\n").filter(e=>e.trim()).slice(-5)},[n]),(0,r.useEffect)(()=>{if(l.current&&c.length>0){let e=l.current.querySelector("[data-radix-scroll-area-viewport]");e&&setTimeout(()=>{e.scrollTo({top:e.scrollHeight,behavior:"smooth"})},100)}},[c]),t||n)?(0,s.jsxs)(o.Zp,{className:"".concat(i," border-blue-200 bg-blue-50/50"),"data-sentry-element":"Card","data-sentry-component":"LivePreview","data-sentry-source-file":"live-preview.tsx",children:[(0,s.jsxs)(o.aR,{className:"pb-3","data-sentry-element":"CardHeader","data-sentry-source-file":"live-preview.tsx",children:[(0,s.jsxs)(o.ZB,{className:"flex items-center gap-2 text-sm","data-sentry-element":"CardTitle","data-sentry-source-file":"live-preview.tsx",children:[(0,s.jsx)(eo.A,{className:"w-4 h-4 text-blue-600","data-sentry-element":"Eye","data-sentry-source-file":"live-preview.tsx"}),"AI Live Preview",t&&(0,s.jsxs)(ei.E,{variant:"secondary",className:"ml-auto",children:[(0,s.jsx)(y.A,{className:"w-3 h-3 mr-1 animate-spin"}),"Generating..."]})]}),a&&(0,s.jsx)("p",{className:"text-xs text-gray-600 mt-1",children:a})]}),(0,s.jsxs)(o.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"live-preview.tsx",children:[(0,s.jsx)(ea.ScrollArea,{ref:l,className:"h-32 w-full rounded border bg-white p-3 overflow-hidden","data-sentry-element":"ScrollArea","data-sentry-source-file":"live-preview.tsx",children:(0,s.jsx)("div",{className:"space-y-2 text-sm font-mono",children:(0,s.jsx)(el.N,{mode:"popLayout","data-sentry-element":"AnimatePresence","data-sentry-source-file":"live-preview.tsx",children:0===c.length?(0,s.jsx)(ec.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"text-gray-400 italic",children:t?"Waiting for AI response...":"No content"},"placeholder"):c.map(e=>(0,s.jsx)(ec.P.div,{initial:{opacity:0,y:20,height:0},animate:{opacity:1,y:0,height:"auto"},exit:{opacity:0,y:-20,height:0,transition:{duration:.3}},transition:{duration:.5,ease:[.4,0,.2,1]},className:"space-y-1",children:e.lines.map((t,n)=>(0,s.jsx)(ec.P.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},transition:{duration:.3,delay:.1*n},className:"text-gray-700",children:t},"".concat(e.id,"-").concat(n)))},e.id))})})}),n&&(0,s.jsxs)("div",{className:"mt-2 text-xs text-gray-500",children:["Characters: ",n.length,u.length>0&&(0,s.jsxs)("span",{className:"ml-2",children:["Lines: ",u.length," (buffered)"]})]})]})]}):null};function eu(){var e;let t=(0,a.useRouter)(),[n,d]=(0,r.useState)({step:"upload",uploadedFile:null,courseOutline:null,editableOutline:null,generationProgress:null,generationResults:null,isGenerating:!1,error:null,streamingText:"",currentStreamingStep:""}),m=(0,r.useCallback)(async e=>{var t;let n=null==(t=e.target.files)?void 0:t[0];if(n){if("application/pdf"!==n.type)return void C.oR.error("Please upload a PDF file");if(n.size>5242880)return void C.oR.error("File size must be less than 5MB");d(e=>({...e,uploadedFile:n,error:null})),C.oR.success("PDF uploaded successfully")}},[]),E=(0,r.useCallback)(async()=>{if(n.uploadedFile){d(e=>({...e,isGenerating:!0,error:null}));try{let e="course-generation-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),t=await k(n.uploadedFile,e,{maxModules:6,maxChaptersPerModule:8,includeQuizzes:!0,includeFinalExam:!0});d(n=>({...n,courseOutline:t,editableOutline:JSON.parse(JSON.stringify(t)),step:"outline",isGenerating:!1,sessionId:e})),C.oR.success("Course outline generated successfully!")}catch(t){let e=t instanceof S?t.message:"Failed to generate course outline";d(t=>({...t,error:e,isGenerating:!1})),C.oR.error(e)}}},[n.uploadedFile]),j=(0,r.useCallback)(e=>{d(t=>({...t,editableOutline:e}))},[]),O=(0,r.useCallback)(async()=>{if(n.editableOutline){d(e=>({...e,step:"generating",isGenerating:!0,error:null,streamingText:"",currentStreamingStep:"Initializing..."}));try{let e=n.sessionId||"course-generation-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),t=await es(n.editableOutline,e=>{d(t=>({...t,generationProgress:e,currentStreamingStep:e.currentStep}))},(e,t)=>{console.log("Completed step: ".concat(e.name),t),d(e=>({...e,streamingText:""}))},e,e=>{d(t=>({...t,streamingText:t.streamingText+e}))});d(e=>({...e,generationResults:t,step:"complete",isGenerating:!1})),C.oR.success("Course content generated successfully!")}catch(t){let e=t instanceof S?t.message:"Failed to generate course content";d(t=>({...t,error:e,isGenerating:!1})),C.oR.error(e)}}},[n.editableOutline]),T=(0,r.useCallback)(()=>{var e;if(!n.editableOutline||!n.generationResults)throw Error("No outline or results available");let t=n.editableOutline.modules.map((e,t)=>{var s;let r=e.chapters.map((e,s)=>{var r;let a="".concat(t,"-").concat(s),i=null==(r=n.generationResults)?void 0:r.chapters.get(a);return{id:"chapter-".concat(t,"-").concat(s),name:e.name,description:e.description,content:(null==i?void 0:i.content)||[],orderIndex:s,hasChapterQuiz:e.hasQuiz,chapterQuiz:e.hasQuiz&&(null==i?void 0:i.quiz)?{id:"quiz-".concat(t,"-").concat(s),name:i.quiz.name,description:i.quiz.description,timeLimit:i.quiz.timeLimit,minimumScore:i.quiz.minimumScore,questions:i.quiz.questions.map((e,n)=>{var r;return{id:"question-".concat(t,"-").concat(s,"-").concat(n),question:e.question,type:e.type,options:("multiple_choice"===e.type||"true_false"===e.type)&&(null==(r=e.options)?void 0:r.map((e,t)=>({content:e.content,isCorrect:e.isCorrect})))||[],essayAnswer:"essay"===e.type?e.essayAnswer:null,explanation:e.explanation,points:e.points||1,orderIndex:n}})}:void 0}}),a=null==(s=n.generationResults)?void 0:s.moduleQuizzes.get(t);return{id:"module-".concat(t),name:e.name,description:e.description,orderIndex:t,chapters:r,hasModuleQuiz:e.hasModuleQuiz,moduleQuiz:e.hasModuleQuiz&&a?{id:"module-quiz-".concat(t),name:a.name,description:a.description,timeLimit:a.timeLimit,minimumScore:a.minimumScore,questions:a.questions.map((e,n)=>{var s;return{id:"module-question-".concat(t,"-").concat(n),question:e.question,type:e.type,options:("multiple_choice"===e.type||"true_false"===e.type)&&(null==(s=e.options)?void 0:s.map((e,t)=>({content:e.content,isCorrect:e.isCorrect})))||[],essayAnswer:"essay"===e.type?e.essayAnswer:null,explanation:e.explanation,points:e.points||1,orderIndex:n}})}:void 0}}),s=null==(e=n.generationResults)?void 0:e.finalExam;return{name:n.editableOutline.courseName,description:n.editableOutline.description,instructor:"",courseCode:"",type:"self_paced",enrollmentType:"code",startDate:void 0,endDate:void 0,coverImage:void 0,coverImagePreview:void 0,modules:t,isPublished:!1,assignedClasses:[],finalExam:s?{id:"final-exam",name:s.name,description:s.description,timeLimit:s.timeLimit,minimumScore:s.minimumScore,questions:s.questions.map((e,t)=>{var n;return{id:"final-question-".concat(t),question:e.question,type:e.type,options:("multiple_choice"===e.type||"true_false"===e.type)&&(null==(n=e.options)?void 0:n.map((e,t)=>({content:e.content,isCorrect:e.isCorrect})))||[],essayAnswer:"essay"===e.type?e.essayAnswer:null,explanation:e.explanation,points:e.points||1,orderIndex:t}})}:void 0}},[n.editableOutline,n.generationResults]),q=(0,r.useCallback)(()=>{try{let e=T();console.log("AI Generated Course Data:",e),sessionStorage.setItem("ai_generated_course_data",JSON.stringify(e)),t.push("/dashboard/teacher/courses/new?from=generate")}catch(e){C.oR.error("Failed to prepare course data for wizard"),console.error("Error converting to course data:",e)}},[T,t]);return(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4","data-sentry-component":"GenerateCoursePage","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"AI Course Generator"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Transform your PDF materials into comprehensive courses with AI-powered content generation"})]}),n.error&&(0,s.jsxs)(p.Fc,{className:"mb-6 max-w-2xl mx-auto",children:[(0,s.jsx)(A.A,{className:"h-4 w-4"}),(0,s.jsx)(p.TN,{children:n.error})]}),"upload"===n.step&&(0,s.jsx)("div",{className:"max-w-2xl mx-auto","data-sentry-component":"renderUploadStep","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(o.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(o.aR,{className:"text-center","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(h.A,{className:"w-6 h-6 text-blue-600","data-sentry-element":"Upload","data-sentry-source-file":"page.tsx"})}),(0,s.jsx)(o.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Upload Course Material"}),(0,s.jsx)(o.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Upload a PDF document to generate a comprehensive course outline using AI"})]}),(0,s.jsxs)(o.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors",children:[(0,s.jsx)(g.A,{className:"mx-auto w-12 h-12 text-gray-400 mb-4","data-sentry-element":"FileText","data-sentry-source-file":"page.tsx"}),(0,s.jsxs)(c.J,{htmlFor:"pdf-upload",className:"cursor-pointer","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("span",{className:"text-lg font-medium",children:"Choose PDF file"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Maximum file size: 5 MB"})]}),(0,s.jsx)(l.p,{id:"pdf-upload",type:"file",accept:".pdf",onChange:m,className:"hidden","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),n.uploadedFile&&(0,s.jsxs)("div",{className:"flex items-center gap-3 p-4 bg-green-50 rounded-lg",children:[(0,s.jsx)(x.A,{className:"w-5 h-5 text-green-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-green-800",children:n.uploadedFile.name}),(0,s.jsxs)("p",{className:"text-sm text-green-600",children:[(n.uploadedFile.size/1048576).toFixed(2)," MB"]})]})]}),(0,s.jsx)(i.$,{onClick:E,disabled:!n.uploadedFile||n.isGenerating,className:"w-full",size:"lg","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:n.isGenerating?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(y.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Generating Outline..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(f.A,{className:"w-4 h-4 mr-2"}),"Generate Course Outline"]})})]})]})}),"outline"===n.step&&(0,s.jsx)("div",{className:"max-w-4xl mx-auto","data-sentry-component":"renderOutlineStep","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(o.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(o.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(o.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(v.A,{className:"w-5 h-5","data-sentry-element":"Edit3","data-sentry-source-file":"page.tsx"}),"Review & Edit Course Outline"]}),(0,s.jsx)(o.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Review the AI-generated course outline and make any necessary adjustments"})]}),(0,s.jsxs)(o.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[n.editableOutline&&(0,s.jsx)(em,{outline:n.editableOutline,onUpdate:j}),(0,s.jsxs)("div",{className:"flex justify-between mt-8",children:[(0,s.jsx)(i.$,{variant:"outline",onClick:()=>d(e=>({...e,step:"upload"})),"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Back to Upload"}),(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-500 flex items-center gap-2",children:[(0,s.jsx)(w.A,{className:"w-4 h-4","data-sentry-element":"Clock","data-sentry-source-file":"page.tsx"}),"Estimated time: ",n.editableOutline?er(n.editableOutline):0," minutes"]}),(0,s.jsxs)(i.$,{onClick:O,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(f.A,{className:"w-4 h-4 mr-2","data-sentry-element":"Brain","data-sentry-source-file":"page.tsx"}),"Generate Course Content"]})]})]})]})]})}),"generating"===n.step&&(0,s.jsx)("div",{className:"max-w-4xl mx-auto space-y-6","data-sentry-component":"renderGeneratingStep","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(o.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(o.aR,{className:"text-center","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(f.A,{className:"w-6 h-6 text-blue-600 animate-pulse","data-sentry-element":"Brain","data-sentry-source-file":"page.tsx"})}),(0,s.jsx)(o.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Generating Course Content"}),(0,s.jsx)(o.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"AI is creating comprehensive content for your course. This may take several minutes."})]}),(0,s.jsx)(o.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:n.generationProgress&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{children:"Progress"}),(0,s.jsxs)("span",{children:[n.generationProgress.completedSteps," / ",n.generationProgress.totalSteps]})]}),(0,s.jsx)(u.k,{value:n.generationProgress.completedSteps/n.generationProgress.totalSteps*100,className:"h-2"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"font-medium",children:n.generationProgress.currentStep}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:n.generationProgress.isGenerating?"Generating...":"Completed"})]})]})})]}),(0,s.jsx)(ed,{isStreaming:n.isGenerating,streamingText:n.streamingText,currentStep:n.currentStreamingStep,"data-sentry-element":"LivePreview","data-sentry-source-file":"page.tsx"})]})}),"complete"===n.step&&(0,s.jsx)("div",{className:"max-w-2xl mx-auto","data-sentry-component":"renderCompleteStep","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(o.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(o.aR,{className:"text-center","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(x.A,{className:"w-6 h-6 text-green-600","data-sentry-element":"CheckCircle","data-sentry-source-file":"page.tsx"})}),(0,s.jsx)(o.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Course Generated Successfully!"}),(0,s.jsx)(o.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Your AI-generated course is ready. You can now proceed to the Course Creation Wizard to finalize and publish it."})]}),(0,s.jsxs)(o.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[n.generationResults&&(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,s.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,s.jsx)(N.A,{className:"w-8 h-8 text-blue-600 mx-auto mb-2"}),(0,s.jsx)("p",{className:"font-medium",children:n.generationResults.chapters.size}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Chapters"})]}),(0,s.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg",children:[(0,s.jsx)(b.A,{className:"w-8 h-8 text-green-600 mx-auto mb-2"}),(0,s.jsx)("p",{className:"font-medium",children:n.generationResults.moduleQuizzes.size}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Module Quizzes"})]}),(0,s.jsxs)("div",{className:"p-4 bg-purple-50 rounded-lg",children:[(0,s.jsx)(R.A,{className:"w-8 h-8 text-purple-600 mx-auto mb-2"}),(0,s.jsx)("p",{className:"font-medium",children:+!!n.generationResults.finalExam}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Final Exam"})]})]}),(null==(e=n.generationResults)?void 0:e.errors)&&n.generationResults.errors.length>0&&(0,s.jsxs)(p.Fc,{children:[(0,s.jsx)(I.A,{className:"h-4 w-4"}),(0,s.jsx)(p.TN,{children:"Some content generation steps encountered errors. You can review and fix these in the Course Creation Wizard."})]}),(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)(i.$,{variant:"outline",onClick:()=>d(e=>({...e,step:"upload",uploadedFile:null,courseOutline:null,editableOutline:null,generationResults:null,streamingText:"",currentStreamingStep:""})),className:"flex-1","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Generate Another Course"}),(0,s.jsx)(i.$,{onClick:q,className:"flex-1","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Proceed to Course Wizard"})]})]})]})}),"generating"===n.step&&(0,s.jsxs)(p.Fc,{className:"max-w-4xl mx-auto mt-6 border-red-500 bg-red-50 text-red-800",children:[(0,s.jsx)(I.A,{className:"h-5 w-5 text-red-600"}),(0,s.jsx)(p.TN,{className:"font-medium",children:"⚠️ PERINGATAN: Jangan tinggalkan halaman ini saat course sedang di-generate untuk menghindari error. Proses ini mungkin memakan waktu beberapa menit."})]})]})}function em(e){let{outline:t,onUpdate:n}=e,r=(e,s)=>{n({...t,[e]:s})},a=(e,s,r)=>{let a=[...t.modules];a[e]={...a[e],[s]:r},n({...t,modules:a})},u=(e,s,r,a)=>{let i=[...t.modules],o=[...i[e].chapters];o[s]={...o[s],[r]:a},i[e]={...i[e],chapters:o},n({...t,modules:i})},p=e=>{let s=t.modules.filter((t,n)=>n!==e);n({...t,modules:s})},h=e=>{let s=[...t.modules];s[e]={...s[e],chapters:[...s[e].chapters,{name:"New Chapter",description:"Chapter description",hasQuiz:!1}]},n({...t,modules:s})},g=(e,s)=>{let r=[...t.modules],a=r[e].chapters.filter((e,t)=>t!==s);r[e]={...r[e],chapters:a},n({...t,modules:r})};return(0,s.jsxs)("div",{className:"space-y-6","data-sentry-component":"OutlineEditor","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"course-title","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Course Title"}),(0,s.jsx)(l.p,{id:"course-title",value:t.courseName,onChange:e=>n({...t,courseName:e.target.value}),className:"mt-1","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"course-description","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Course Description"}),(0,s.jsx)(d.T,{id:"course-description",value:t.description,onChange:e=>r("description",e.target.value),className:"mt-1",rows:3,"data-sentry-element":"Textarea","data-sentry-source-file":"page.tsx"})]})]}),(0,s.jsx)(m.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"page.tsx"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Course Modules"}),(0,s.jsxs)(i.$,{onClick:()=>{n({...t,modules:[...t.modules,{name:"New Module",description:"Module description",chapters:[{name:"New Chapter",description:"Chapter description",hasQuiz:!1}],hasModuleQuiz:!1}]})},size:"sm","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(E.A,{className:"w-4 h-4 mr-1","data-sentry-element":"Plus","data-sentry-source-file":"page.tsx"}),"Add Module"]})]}),t.modules.map((e,t)=>(0,s.jsxs)(o.Zp,{children:[(0,s.jsx)(o.aR,{children:(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,s.jsx)(l.p,{value:e.name,onChange:e=>a(t,"name",e.target.value),className:"font-medium"}),(0,s.jsx)(d.T,{value:e.description,onChange:e=>a(t,"description",e.target.value),rows:2})]}),(0,s.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>p(t),className:"text-red-600 hover:text-red-700",children:(0,s.jsx)(j.A,{className:"w-4 h-4"})})]})}),(0,s.jsx)(o.Wu,{children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(c.J,{className:"text-sm font-medium",children:"Chapters"}),(0,s.jsxs)(i.$,{onClick:()=>h(t),size:"sm",variant:"outline",children:[(0,s.jsx)(E.A,{className:"w-3 h-3 mr-1"}),"Add Chapter"]})]}),e.chapters.map((e,n)=>(0,s.jsxs)("div",{className:"flex gap-2 items-start p-3 border rounded",children:[(0,s.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,s.jsx)(l.p,{value:e.name,onChange:e=>u(t,n,"name",e.target.value),placeholder:"Chapter name",className:"text-sm"}),(0,s.jsx)(l.p,{value:e.description,onChange:e=>u(t,n,"description",e.target.value),placeholder:"Chapter description",className:"text-sm"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",id:"chapter-quiz-".concat(t,"-").concat(n),checked:e.hasQuiz,onChange:e=>u(t,n,"hasQuiz",e.target.checked)}),(0,s.jsx)(c.J,{htmlFor:"chapter-quiz-".concat(t,"-").concat(n),className:"text-xs",children:"Include chapter quiz"})]})]}),(0,s.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>g(t,n),className:"text-red-600 hover:text-red-700",children:(0,s.jsx)(j.A,{className:"w-3 h-3"})})]},n))]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 pt-2 border-t",children:[(0,s.jsx)("input",{type:"checkbox",id:"module-quiz-".concat(t),checked:e.hasModuleQuiz,onChange:e=>a(t,"hasModuleQuiz",e.target.checked)}),(0,s.jsx)(c.J,{htmlFor:"module-quiz-".concat(t),className:"text-sm",children:"Include module quiz"})]})]})})]},t))]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 p-4 border rounded",children:[(0,s.jsx)("input",{type:"checkbox",id:"final-exam",checked:t.hasFinalExam,onChange:e=>r("hasFinalExam",e.target.checked)}),(0,s.jsx)(c.J,{htmlFor:"final-exam","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Include final exam"})]})]})}},31936:(e,t,n)=>{"use strict";n.d(t,{p:()=>a});var s=n(95155);n(12115);var r=n(64269);function a(e){let{className:t,type:n,...a}=e;return(0,s.jsx)("input",{type:n,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...a,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},42526:(e,t,n)=>{"use strict";n.d(t,{J:()=>i});var s=n(95155);n(12115);var r=n(10489),a=n(64269);function i(e){let{className:t,...n}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,a.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...n,"data-sentry-element":"LabelPrimitive.Root","data-sentry-component":"Label","data-sentry-source-file":"label.tsx"})}},46201:(e,t,n)=>{"use strict";n.d(t,{Separator:()=>o});var s=n(95155),r=n(12115),a=n(57268),i=n(64269);let o=r.forwardRef((e,t)=>{let{className:n,orientation:r="horizontal",decorative:o=!0,...l}=e;return(0,s.jsx)(a.b,{ref:t,decorative:o,orientation:r,className:(0,i.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",n),...l})});o.displayName=a.b.displayName},46817:(e,t,n)=>{Promise.resolve().then(n.bind(n,27223))},47254:(e,t,n)=>{"use strict";n.d(t,{T:()=>a});var s=n(95155);n(12115);var r=n(64269);function a(e){let{className:t,...n}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...n,"data-sentry-component":"Textarea","data-sentry-source-file":"textarea.tsx"})}},62879:(e,t,n)=>{"use strict";n.d(t,{$:()=>o,ScrollArea:()=>i});var s=n(95155);n(12115);var r=n(59034),a=n(64269);function i(e){let{className:t,children:n,...i}=e;return(0,s.jsxs)(r.bL,{"data-slot":"scroll-area",className:(0,a.cn)("relative",t),...i,"data-sentry-element":"ScrollAreaPrimitive.Root","data-sentry-component":"ScrollArea","data-sentry-source-file":"scroll-area.tsx",children:[(0,s.jsx)(r.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1","data-sentry-element":"ScrollAreaPrimitive.Viewport","data-sentry-source-file":"scroll-area.tsx",children:n}),(0,s.jsx)(o,{"data-sentry-element":"ScrollBar","data-sentry-source-file":"scroll-area.tsx"}),(0,s.jsx)(r.OK,{"data-sentry-element":"ScrollAreaPrimitive.Corner","data-sentry-source-file":"scroll-area.tsx"})]})}function o(e){let{className:t,orientation:n="vertical",...i}=e;return(0,s.jsx)(r.VM,{"data-slot":"scroll-area-scrollbar",orientation:n,className:(0,a.cn)("flex touch-none p-px transition-colors select-none","vertical"===n&&"h-full w-2.5 border-l border-l-transparent","horizontal"===n&&"h-2.5 flex-col border-t border-t-transparent",t),...i,"data-sentry-element":"ScrollAreaPrimitive.ScrollAreaScrollbar","data-sentry-component":"ScrollBar","data-sentry-source-file":"scroll-area.tsx",children:(0,s.jsx)(r.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full","data-sentry-element":"ScrollAreaPrimitive.ScrollAreaThumb","data-sentry-source-file":"scroll-area.tsx"})})}},64269:(e,t,n)=>{"use strict";n.d(t,{cn:()=>a,z:()=>i});var s=n(2821),r=n(75889);function a(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.QP)((0,s.$)(t))}function i(e){var t,n;let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:r=0,sizeType:a="normal"}=s;if(0===e)return"0 Byte";let i=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,i)).toFixed(r)," ").concat("accurate"===a?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][i])?t:"Bytest":null!=(n=["Bytes","KB","MB","GB","TB"][i])?n:"Bytes")}},66094:(e,t,n)=>{"use strict";n.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>a,aR:()=>i,wL:()=>d});var s=n(95155);n(12115);var r=n(64269);function a(e){let{className:t,...n}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...n,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...n}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...n,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...n}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...n,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...n}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...n,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...n}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...n,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...n}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...n,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},88021:(e,t,n)=>{"use strict";n.d(t,{E:()=>l});var s=n(95155);n(12115);var r=n(32467),a=n(83101),i=n(64269);let o=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:n,asChild:a=!1,...l}=e,c=a?r.DX:"span";return(0,s.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(o({variant:n}),t),...l,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3798,4909,8720,9034,7420,4850,8441,3840,7358],()=>t(46817)),_N_E=e.O()}]);