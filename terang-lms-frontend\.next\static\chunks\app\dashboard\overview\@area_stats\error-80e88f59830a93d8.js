try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="dd0273a4-8047-4ea4-bc4d-5b5175579567",e._sentryDebugIdIdentifier="sentry-dbid-dd0273a4-8047-4ea4-bc4d-5b5175579567")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9072],{4662:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>i,TN:()=>d,XL:()=>o});var a=r(95155);r(12115);var l=r(83101),s=r(64269);let n=(0,l.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...l}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,s.cn)(n({variant:r}),t),...l,"data-sentry-component":"Alert","data-sentry-source-file":"alert.tsx"})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,s.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...r,"data-sentry-component":"AlertTitle","data-sentry-source-file":"alert.tsx"})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,s.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r,"data-sentry-component":"AlertDescription","data-sentry-source-file":"alert.tsx"})}},6132:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(71847).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},29767:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(95155),l=r(4662),s=r(6132);function n(e){let{error:t}=e;return(0,a.jsxs)(l.Fc,{variant:"destructive","data-sentry-element":"Alert","data-sentry-component":"AreaStatsError","data-sentry-source-file":"error.tsx",children:[(0,a.jsx)(s.A,{className:"h-4 w-4","data-sentry-element":"AlertCircle","data-sentry-source-file":"error.tsx"}),(0,a.jsx)(l.XL,{"data-sentry-element":"AlertTitle","data-sentry-source-file":"error.tsx",children:"Error"}),(0,a.jsxs)(l.TN,{"data-sentry-element":"AlertDescription","data-sentry-source-file":"error.tsx",children:["Failed to load area statistics: ",t.message]})]})}},64269:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s,z:()=>n});var a=r(2821),l=r(75889);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,l.QP)((0,a.$)(t))}function n(e){var t,r;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:l=0,sizeType:s="normal"}=a;if(0===e)return"0 Byte";let n=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,n)).toFixed(l)," ").concat("accurate"===s?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][n])?t:"Bytest":null!=(r=["Bytes","KB","MB","GB","TB"][n])?r:"Bytes")}},71847:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(12115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:d="",children:c,iconNode:u,...f}=e;return(0,a.createElement)("svg",{ref:t,...n,width:l,height:l,stroke:r,strokeWidth:o?24*Number(i)/Number(l):i,className:s("lucide",d),...f},[...u.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),o=(e,t)=>{let r=(0,a.forwardRef)((r,n)=>{let{className:o,...d}=r;return(0,a.createElement)(i,{ref:n,iconNode:t,className:s("lucide-".concat(l(e)),o),...d})});return r.displayName="".concat(e),r}},83101:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});var a=r(2821);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=a.$,n=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:n,defaultVariants:i}=t,o=Object.keys(n).map(e=>{let t=null==r?void 0:r[e],a=null==i?void 0:i[e];if(null===t)return null;let s=l(t)||l(a);return n[e][s]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return s(e,o,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:r,className:a,...l}=t;return Object.entries(l).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...d}[t]):({...i,...d})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},93044:(e,t,r)=>{Promise.resolve().then(r.bind(r,29767))}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,4850,8441,3840,7358],()=>t(93044)),_N_E=e.O()}]);