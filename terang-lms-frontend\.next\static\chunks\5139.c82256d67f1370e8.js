try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="de446d9f-80d3-4653-bfe4-9f78f3dc28df",e._sentryDebugIdIdentifier="sentry-dbid-de446d9f-80d3-4653-bfe4-9f78f3dc28df")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5139],{45139:(e,t,d)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=d(72687),f=d(37876),l=n._(d(14232)),s=d(1033);async function o(e){let{Component:t,ctx:d}=e;return{pageProps:await (0,s.loadGetInitialProps)(t,d)}}class u extends l.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,f.jsx)(e,{...t})}}u.origGetInitialProps=o,u.getInitialProps=o,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);