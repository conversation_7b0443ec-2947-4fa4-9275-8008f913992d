try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="7b9d6e3a-da10-44f1-aada-a8126a812b87",e._sentryDebugIdIdentifier="sentry-dbid-7b9d6e3a-da10-44f1-aada-a8126a812b87")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3793],{5548:(e,t,a)=>{"use strict";a.d(t,{default:()=>d});var n=a(95155),r=a(20764),s=a(38004),o=a(31936),i=a(47254),l=a(91620);function d(){let e=(0,l.O)(e=>e.addTask);return(0,n.jsxs)(s.lG,{"data-sentry-element":"Dialog","data-sentry-component":"NewTaskDialog","data-sentry-source-file":"new-task-dialog.tsx",children:[(0,n.jsx)(s.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"new-task-dialog.tsx",children:(0,n.jsx)(r.$,{variant:"secondary",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"new-task-dialog.tsx",children:"＋ Add New Todo"})}),(0,n.jsxs)(s.Cf,{className:"sm:max-w-[425px]","data-sentry-element":"DialogContent","data-sentry-source-file":"new-task-dialog.tsx",children:[(0,n.jsxs)(s.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"new-task-dialog.tsx",children:[(0,n.jsx)(s.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"new-task-dialog.tsx",children:"Add New Todo"}),(0,n.jsx)(s.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"new-task-dialog.tsx",children:"What do you want to get done today?"})]}),(0,n.jsxs)("form",{id:"todo-form",className:"grid gap-4 py-4",onSubmit:t=>{t.preventDefault();let{title:a,description:n}=Object.fromEntries(new FormData(t.currentTarget));"string"==typeof a&&"string"==typeof n&&e(a,n)},children:[(0,n.jsx)("div",{className:"grid grid-cols-4 items-center gap-4",children:(0,n.jsx)(o.p,{id:"title",name:"title",placeholder:"Todo title...",className:"col-span-4","data-sentry-element":"Input","data-sentry-source-file":"new-task-dialog.tsx"})}),(0,n.jsx)("div",{className:"grid grid-cols-4 items-center gap-4",children:(0,n.jsx)(i.T,{id:"description",name:"description",placeholder:"Description...",className:"col-span-4","data-sentry-element":"Textarea","data-sentry-source-file":"new-task-dialog.tsx"})})]}),(0,n.jsx)(s.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"new-task-dialog.tsx",children:(0,n.jsx)(s.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"new-task-dialog.tsx",children:(0,n.jsx)(r.$,{type:"submit",size:"sm",form:"todo-form","data-sentry-element":"Button","data-sentry-source-file":"new-task-dialog.tsx",children:"Add Todo"})})})]})]})}},12833:(e,t,a)=>{"use strict";a.d(t,{I:()=>c,SQ:()=>d,_2:()=>u,hO:()=>m,lp:()=>f,mB:()=>g,rI:()=>i,ty:()=>l});var n=a(95155);a(12115);var r=a(47971),s=a(5917),o=a(64269);function i(e){let{...t}=e;return(0,n.jsx)(r.bL,{"data-slot":"dropdown-menu",...t,"data-sentry-element":"DropdownMenuPrimitive.Root","data-sentry-component":"DropdownMenu","data-sentry-source-file":"dropdown-menu.tsx"})}function l(e){let{...t}=e;return(0,n.jsx)(r.l9,{"data-slot":"dropdown-menu-trigger",...t,"data-sentry-element":"DropdownMenuPrimitive.Trigger","data-sentry-component":"DropdownMenuTrigger","data-sentry-source-file":"dropdown-menu.tsx"})}function d(e){let{className:t,sideOffset:a=4,...s}=e;return(0,n.jsx)(r.ZL,{"data-sentry-element":"DropdownMenuPrimitive.Portal","data-sentry-component":"DropdownMenuContent","data-sentry-source-file":"dropdown-menu.tsx",children:(0,n.jsx)(r.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...s,"data-sentry-element":"DropdownMenuPrimitive.Content","data-sentry-source-file":"dropdown-menu.tsx"})})}function c(e){let{...t}=e;return(0,n.jsx)(r.YJ,{"data-slot":"dropdown-menu-group",...t,"data-sentry-element":"DropdownMenuPrimitive.Group","data-sentry-component":"DropdownMenuGroup","data-sentry-source-file":"dropdown-menu.tsx"})}function u(e){let{className:t,inset:a,variant:s="default",...i}=e;return(0,n.jsx)(r.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":s,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground focus:[&_svg:not([class*='text-'])]:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i,"data-sentry-element":"DropdownMenuPrimitive.Item","data-sentry-component":"DropdownMenuItem","data-sentry-source-file":"dropdown-menu.tsx"})}function m(e){let{className:t,children:a,checked:i,...l}=e;return(0,n.jsxs)(r.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:i,...l,"data-sentry-element":"DropdownMenuPrimitive.CheckboxItem","data-sentry-component":"DropdownMenuCheckboxItem","data-sentry-source-file":"dropdown-menu.tsx",children:[(0,n.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,n.jsx)(r.VF,{"data-sentry-element":"DropdownMenuPrimitive.ItemIndicator","data-sentry-source-file":"dropdown-menu.tsx",children:(0,n.jsx)(s.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"dropdown-menu.tsx"})})}),a]})}function f(e){let{className:t,inset:a,...s}=e;return(0,n.jsx)(r.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,o.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...s,"data-sentry-element":"DropdownMenuPrimitive.Label","data-sentry-component":"DropdownMenuLabel","data-sentry-source-file":"dropdown-menu.tsx"})}function g(e){let{className:t,...a}=e;return(0,n.jsx)(r.wv,{"data-slot":"dropdown-menu-separator",className:(0,o.cn)("bg-border -mx-1 my-1 h-px",t),...a,"data-sentry-element":"DropdownMenuPrimitive.Separator","data-sentry-component":"DropdownMenuSeparator","data-sentry-source-file":"dropdown-menu.tsx"})}},14883:(e,t,a)=>{"use strict";a.d(t,{$v:()=>x,EO:()=>u,Lt:()=>i,Rx:()=>y,Zr:()=>p,ck:()=>f,r7:()=>g,tv:()=>l,wd:()=>m});var n=a(95155);a(12115);var r=a(35646),s=a(64269),o=a(20764);function i(e){let{...t}=e;return(0,n.jsx)(r.bL,{"data-slot":"alert-dialog",...t,"data-sentry-element":"AlertDialogPrimitive.Root","data-sentry-component":"AlertDialog","data-sentry-source-file":"alert-dialog.tsx"})}function l(e){let{...t}=e;return(0,n.jsx)(r.l9,{"data-slot":"alert-dialog-trigger",...t,"data-sentry-element":"AlertDialogPrimitive.Trigger","data-sentry-component":"AlertDialogTrigger","data-sentry-source-file":"alert-dialog.tsx"})}function d(e){let{...t}=e;return(0,n.jsx)(r.ZL,{"data-slot":"alert-dialog-portal",...t,"data-sentry-element":"AlertDialogPrimitive.Portal","data-sentry-component":"AlertDialogPortal","data-sentry-source-file":"alert-dialog.tsx"})}function c(e){let{className:t,...a}=e;return(0,n.jsx)(r.hJ,{"data-slot":"alert-dialog-overlay",className:(0,s.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a,"data-sentry-element":"AlertDialogPrimitive.Overlay","data-sentry-component":"AlertDialogOverlay","data-sentry-source-file":"alert-dialog.tsx"})}function u(e){let{className:t,...a}=e;return(0,n.jsxs)(d,{"data-sentry-element":"AlertDialogPortal","data-sentry-component":"AlertDialogContent","data-sentry-source-file":"alert-dialog.tsx",children:[(0,n.jsx)(c,{"data-sentry-element":"AlertDialogOverlay","data-sentry-source-file":"alert-dialog.tsx"}),(0,n.jsx)(r.UC,{"data-slot":"alert-dialog-content",className:(0,s.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...a,"data-sentry-element":"AlertDialogPrimitive.Content","data-sentry-source-file":"alert-dialog.tsx"})]})}function m(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,s.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a,"data-sentry-component":"AlertDialogHeader","data-sentry-source-file":"alert-dialog.tsx"})}function f(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,s.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a,"data-sentry-component":"AlertDialogFooter","data-sentry-source-file":"alert-dialog.tsx"})}function g(e){let{className:t,...a}=e;return(0,n.jsx)(r.hE,{"data-slot":"alert-dialog-title",className:(0,s.cn)("text-lg font-semibold",t),...a,"data-sentry-element":"AlertDialogPrimitive.Title","data-sentry-component":"AlertDialogTitle","data-sentry-source-file":"alert-dialog.tsx"})}function x(e){let{className:t,...a}=e;return(0,n.jsx)(r.VY,{"data-slot":"alert-dialog-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-element":"AlertDialogPrimitive.Description","data-sentry-component":"AlertDialogDescription","data-sentry-source-file":"alert-dialog.tsx"})}function y(e){let{className:t,...a}=e;return(0,n.jsx)(r.rc,{className:(0,s.cn)((0,o.r)(),t),...a,"data-sentry-element":"AlertDialogPrimitive.Action","data-sentry-component":"AlertDialogAction","data-sentry-source-file":"alert-dialog.tsx"})}function p(e){let{className:t,...a}=e;return(0,n.jsx)(r.ZD,{className:(0,s.cn)((0,o.r)({variant:"outline"}),t),...a,"data-sentry-element":"AlertDialogPrimitive.Cancel","data-sentry-component":"AlertDialogCancel","data-sentry-source-file":"alert-dialog.tsx"})}},20764:(e,t,a)=>{"use strict";a.d(t,{$:()=>l,r:()=>i});var n=a(95155);a(12115);var r=a(32467),s=a(83101),o=a(64269);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:s,asChild:l=!1,...d}=e,c=l?r.DX:"button";return(0,n.jsx)(c,{"data-slot":"button",className:(0,o.cn)(i({variant:a,size:s,className:t})),...d,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},25484:(e,t,a)=>{Promise.resolve().then(a.bind(a,62879)),Promise.resolve().then(a.bind(a,73975)),Promise.resolve().then(a.bind(a,5548))},31936:(e,t,a)=>{"use strict";a.d(t,{p:()=>s});var n=a(95155);a(12115);var r=a(64269);function s(e){let{className:t,type:a,...s}=e;return(0,n.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},38004:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>f,L3:()=>g,c7:()=>m,lG:()=>i,rr:()=>x,zM:()=>l});var n=a(95155);a(12115);var r=a(89511),s=a(65229),o=a(64269);function i(e){let{...t}=e;return(0,n.jsx)(r.bL,{"data-slot":"dialog",...t,"data-sentry-element":"DialogPrimitive.Root","data-sentry-component":"Dialog","data-sentry-source-file":"dialog.tsx"})}function l(e){let{...t}=e;return(0,n.jsx)(r.l9,{"data-slot":"dialog-trigger",...t,"data-sentry-element":"DialogPrimitive.Trigger","data-sentry-component":"DialogTrigger","data-sentry-source-file":"dialog.tsx"})}function d(e){let{...t}=e;return(0,n.jsx)(r.ZL,{"data-slot":"dialog-portal",...t,"data-sentry-element":"DialogPrimitive.Portal","data-sentry-component":"DialogPortal","data-sentry-source-file":"dialog.tsx"})}function c(e){let{className:t,...a}=e;return(0,n.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a,"data-sentry-element":"DialogPrimitive.Overlay","data-sentry-component":"DialogOverlay","data-sentry-source-file":"dialog.tsx"})}function u(e){let{className:t,children:a,...i}=e;return(0,n.jsxs)(d,{"data-slot":"dialog-portal","data-sentry-element":"DialogPortal","data-sentry-component":"DialogContent","data-sentry-source-file":"dialog.tsx",children:[(0,n.jsx)(c,{"data-sentry-element":"DialogOverlay","data-sentry-source-file":"dialog.tsx"}),(0,n.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...i,"data-sentry-element":"DialogPrimitive.Content","data-sentry-source-file":"dialog.tsx",children:[a,(0,n.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","data-sentry-element":"DialogPrimitive.Close","data-sentry-source-file":"dialog.tsx",children:[(0,n.jsx)(s.A,{"data-sentry-element":"XIcon","data-sentry-source-file":"dialog.tsx"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a,"data-sentry-component":"DialogHeader","data-sentry-source-file":"dialog.tsx"})}function f(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a,"data-sentry-component":"DialogFooter","data-sentry-source-file":"dialog.tsx"})}function g(e){let{className:t,...a}=e;return(0,n.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",t),...a,"data-sentry-element":"DialogPrimitive.Title","data-sentry-component":"DialogTitle","data-sentry-source-file":"dialog.tsx"})}function x(e){let{className:t,...a}=e;return(0,n.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-element":"DialogPrimitive.Description","data-sentry-component":"DialogDescription","data-sentry-source-file":"dialog.tsx"})}},47254:(e,t,a)=>{"use strict";a.d(t,{T:()=>s});var n=a(95155);a(12115);var r=a(64269);function s(e){let{className:t,...a}=e;return(0,n.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a,"data-sentry-component":"Textarea","data-sentry-source-file":"textarea.tsx"})}},62879:(e,t,a)=>{"use strict";a.d(t,{$:()=>i,ScrollArea:()=>o});var n=a(95155);a(12115);var r=a(59034),s=a(64269);function o(e){let{className:t,children:a,...o}=e;return(0,n.jsxs)(r.bL,{"data-slot":"scroll-area",className:(0,s.cn)("relative",t),...o,"data-sentry-element":"ScrollAreaPrimitive.Root","data-sentry-component":"ScrollArea","data-sentry-source-file":"scroll-area.tsx",children:[(0,n.jsx)(r.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1","data-sentry-element":"ScrollAreaPrimitive.Viewport","data-sentry-source-file":"scroll-area.tsx",children:a}),(0,n.jsx)(i,{"data-sentry-element":"ScrollBar","data-sentry-source-file":"scroll-area.tsx"}),(0,n.jsx)(r.OK,{"data-sentry-element":"ScrollAreaPrimitive.Corner","data-sentry-source-file":"scroll-area.tsx"})]})}function i(e){let{className:t,orientation:a="vertical",...o}=e;return(0,n.jsx)(r.VM,{"data-slot":"scroll-area-scrollbar",orientation:a,className:(0,s.cn)("flex touch-none p-px transition-colors select-none","vertical"===a&&"h-full w-2.5 border-l border-l-transparent","horizontal"===a&&"h-2.5 flex-col border-t border-t-transparent",t),...o,"data-sentry-element":"ScrollAreaPrimitive.ScrollAreaScrollbar","data-sentry-component":"ScrollBar","data-sentry-source-file":"scroll-area.tsx",children:(0,n.jsx)(r.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full","data-sentry-element":"ScrollAreaPrimitive.ScrollAreaThumb","data-sentry-source-file":"scroll-area.tsx"})})}},64269:(e,t,a)=>{"use strict";a.d(t,{cn:()=>s,z:()=>o});var n=a(2821),r=a(75889);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,n.$)(t))}function o(e){var t,a;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:r=0,sizeType:s="normal"}=n;if(0===e)return"0 Byte";let o=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,o)).toFixed(r)," ").concat("accurate"===s?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][o])?t:"Bytest":null!=(a=["Bytes","KB","MB","GB","TB"][o])?a:"Bytes")}},66094:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>i,Zp:()=>s,aR:()=>o,wL:()=>c});var n=a(95155);a(12115);var r=a(64269);function s(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},73975:(e,t,a)=>{"use strict";a.d(t,{KanbanBoard:()=>A});var n=a(95155),r=a(12115),s=a(47650),o=a(91620);function i(e){if(!e)return!1;let t=e.data.current;return(null==t?void 0:t.type)==="Column"||(null==t?void 0:t.type)==="Task"}var l=a(17787),d=a(49027),c=a(27587),u=a(83101),m=a(49476),f=a(20764),g=a(66094),x=a(31719),y=a(14883),p=a(12833),v=a(31936),b=a(18720);function h(e){let{title:t,id:a}=e,[s,i]=r.useState(t),l=(0,o.O)(e=>e.updateCol),d=(0,o.O)(e=>e.removeCol),[c,u]=r.useState(!0),[m,g]=r.useState(!1),h=r.useRef(null);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("form",{onSubmit:e=>{e.preventDefault(),u(!c),l(a,s),(0,b.oR)("".concat(t," updated to ").concat(s))},children:(0,n.jsx)(v.p,{value:s,onChange:e=>i(e.target.value),className:"mt-0! mr-auto text-base disabled:cursor-pointer disabled:border-none disabled:opacity-100",disabled:c,ref:h,"data-sentry-element":"Input","data-sentry-source-file":"column-action.tsx"})}),(0,n.jsxs)(p.rI,{modal:!1,"data-sentry-element":"DropdownMenu","data-sentry-source-file":"column-action.tsx",children:[(0,n.jsx)(p.ty,{asChild:!0,"data-sentry-element":"DropdownMenuTrigger","data-sentry-source-file":"column-action.tsx",children:(0,n.jsxs)(f.$,{variant:"secondary",className:"ml-1","data-sentry-element":"Button","data-sentry-source-file":"column-action.tsx",children:[(0,n.jsx)("span",{className:"sr-only",children:"Actions"}),(0,n.jsx)(x.Oer,{className:"h-4 w-4","data-sentry-element":"DotsHorizontalIcon","data-sentry-source-file":"column-action.tsx"})]})}),(0,n.jsxs)(p.SQ,{align:"end","data-sentry-element":"DropdownMenuContent","data-sentry-source-file":"column-action.tsx",children:[(0,n.jsx)(p._2,{onSelect:()=>{u(!c),setTimeout(()=>{var e;h.current&&(null==(e=h.current)||e.focus())},500)},"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"column-action.tsx",children:"Rename"}),(0,n.jsx)(p.mB,{"data-sentry-element":"DropdownMenuSeparator","data-sentry-source-file":"column-action.tsx"}),(0,n.jsx)(p._2,{onSelect:()=>g(!0),className:"text-red-600","data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"column-action.tsx",children:"Delete Section"})]})]}),(0,n.jsx)(y.Lt,{open:m,onOpenChange:g,"data-sentry-element":"AlertDialog","data-sentry-source-file":"column-action.tsx",children:(0,n.jsxs)(y.EO,{"data-sentry-element":"AlertDialogContent","data-sentry-source-file":"column-action.tsx",children:[(0,n.jsxs)(y.wd,{"data-sentry-element":"AlertDialogHeader","data-sentry-source-file":"column-action.tsx",children:[(0,n.jsx)(y.r7,{"data-sentry-element":"AlertDialogTitle","data-sentry-source-file":"column-action.tsx",children:"Are you sure want to delete column?"}),(0,n.jsx)(y.$v,{"data-sentry-element":"AlertDialogDescription","data-sentry-source-file":"column-action.tsx",children:"NOTE: All tasks related to this category will also be deleted."})]}),(0,n.jsxs)(y.ck,{"data-sentry-element":"AlertDialogFooter","data-sentry-source-file":"column-action.tsx",children:[(0,n.jsx)(y.Zr,{"data-sentry-element":"AlertDialogCancel","data-sentry-source-file":"column-action.tsx",children:"Cancel"}),(0,n.jsx)(f.$,{variant:"destructive",onClick:()=>{setTimeout(()=>document.body.style.pointerEvents="",100),g(!1),d(a),(0,b.oR)("This column has been deleted.")},"data-sentry-element":"Button","data-sentry-source-file":"column-action.tsx",children:"Delete"})]})]})})]})}var w=a(88021);function j(e){let{task:t,isOverlay:a}=e,{setNodeRef:r,attributes:s,listeners:o,transform:i,transition:l,isDragging:x}=(0,d.gl)({id:t.id,data:{type:"Task",task:t},attributes:{roleDescription:"Task"}}),y={transition:l,transform:c.Ks.Translate.toString(i)},p=(0,u.F)("mb-2",{variants:{dragging:{over:"ring-2 opacity-30",overlay:"ring-2 ring-primary"}}});return(0,n.jsxs)(g.Zp,{ref:r,style:y,className:p({dragging:a?"overlay":x?"over":void 0}),"data-sentry-element":"Card","data-sentry-component":"TaskCard","data-sentry-source-file":"task-card.tsx",children:[(0,n.jsxs)(g.aR,{className:"space-between border-secondary relative flex flex-row border-b-2 px-3 py-3","data-sentry-element":"CardHeader","data-sentry-source-file":"task-card.tsx",children:[(0,n.jsxs)(f.$,{variant:"ghost",...s,...o,className:"text-secondary-foreground/50 -ml-2 h-auto cursor-grab p-1","data-sentry-element":"Button","data-sentry-source-file":"task-card.tsx",children:[(0,n.jsx)("span",{className:"sr-only",children:"Move task"}),(0,n.jsx)(m.A,{"data-sentry-element":"GripVertical","data-sentry-source-file":"task-card.tsx"})]}),(0,n.jsx)(w.E,{variant:"outline",className:"ml-auto font-semibold","data-sentry-element":"Badge","data-sentry-source-file":"task-card.tsx",children:"Task"})]}),(0,n.jsx)(g.Wu,{className:"px-3 pt-3 pb-6 text-left whitespace-pre-wrap","data-sentry-element":"CardContent","data-sentry-source-file":"task-card.tsx",children:t.title})]})}var D=a(62879);function k(e){let{column:t,tasks:a,isOverlay:s}=e,o=(0,r.useMemo)(()=>a.map(e=>e.id),[a]),{setNodeRef:i,attributes:l,listeners:x,transform:y,transition:p,isDragging:v}=(0,d.gl)({id:t.id,data:{type:"Column",column:t},attributes:{roleDescription:"Column: ".concat(t.title)}}),b={transition:p,transform:c.Ks.Translate.toString(y)},w=(0,u.F)("h-[75vh] max-h-[75vh] w-[350px] max-w-full bg-secondary flex flex-col shrink-0 snap-center",{variants:{dragging:{default:"border-2 border-transparent",over:"ring-2 opacity-30",overlay:"ring-2 ring-primary"}}});return(0,n.jsxs)(g.Zp,{ref:i,style:b,className:w({dragging:s?"overlay":v?"over":void 0}),"data-sentry-element":"Card","data-sentry-component":"BoardColumn","data-sentry-source-file":"board-column.tsx",children:[(0,n.jsxs)(g.aR,{className:"space-between flex flex-row items-center border-b-2 p-4 text-left font-semibold","data-sentry-element":"CardHeader","data-sentry-source-file":"board-column.tsx",children:[(0,n.jsxs)(f.$,{variant:"ghost",...l,...x,className:"text-primary/50 relative -ml-2 h-auto cursor-grab p-1","data-sentry-element":"Button","data-sentry-source-file":"board-column.tsx",children:[(0,n.jsx)("span",{className:"sr-only",children:"Move column: ".concat(t.title)}),(0,n.jsx)(m.A,{"data-sentry-element":"GripVertical","data-sentry-source-file":"board-column.tsx"})]}),(0,n.jsx)(h,{id:t.id,title:t.title,"data-sentry-element":"ColumnActions","data-sentry-source-file":"board-column.tsx"})]}),(0,n.jsx)(g.Wu,{className:"flex grow flex-col gap-4 overflow-x-hidden p-2","data-sentry-element":"CardContent","data-sentry-source-file":"board-column.tsx",children:(0,n.jsx)(D.ScrollArea,{className:"h-full","data-sentry-element":"ScrollArea","data-sentry-source-file":"board-column.tsx",children:(0,n.jsx)(d.gB,{items:o,"data-sentry-element":"SortableContext","data-sentry-source-file":"board-column.tsx",children:a.map(e=>(0,n.jsx)(j,{task:e},e.id))})})})]})}function C(e){let{children:t}=e,a=(0,l.fF)(),r=(0,u.F)("px-2  pb-4 md:px-0 flex lg:justify-start",{variants:{dragging:{default:"",active:"snap-none"}}});return(0,n.jsxs)(D.ScrollArea,{className:"w-full rounded-md whitespace-nowrap","data-sentry-element":"ScrollArea","data-sentry-component":"BoardContainer","data-sentry-source-file":"board-column.tsx",children:[(0,n.jsx)("div",{className:r({dragging:a.active?"active":"default"}),children:(0,n.jsx)("div",{className:"flex flex-row items-start justify-center gap-4",children:t})}),(0,n.jsx)(D.$,{orientation:"horizontal","data-sentry-element":"ScrollBar","data-sentry-source-file":"board-column.tsx"})]})}var N=a(38004);function T(){let e=(0,o.O)(e=>e.addCol);return(0,n.jsxs)(N.lG,{"data-sentry-element":"Dialog","data-sentry-component":"NewSectionDialog","data-sentry-source-file":"new-section-dialog.tsx",children:[(0,n.jsx)(N.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"new-section-dialog.tsx",children:(0,n.jsx)(f.$,{variant:"secondary",size:"lg",className:"w-full","data-sentry-element":"Button","data-sentry-source-file":"new-section-dialog.tsx",children:"＋ Add New Section"})}),(0,n.jsxs)(N.Cf,{className:"sm:max-w-[425px]","data-sentry-element":"DialogContent","data-sentry-source-file":"new-section-dialog.tsx",children:[(0,n.jsxs)(N.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"new-section-dialog.tsx",children:[(0,n.jsx)(N.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"new-section-dialog.tsx",children:"Add New Section"}),(0,n.jsx)(N.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"new-section-dialog.tsx",children:"What section you want to add today?"})]}),(0,n.jsx)("form",{id:"todo-form",className:"grid gap-4 py-4",onSubmit:t=>{t.preventDefault();let{title:a}=Object.fromEntries(new FormData(t.currentTarget));"string"==typeof a&&e(a)},children:(0,n.jsx)("div",{className:"grid grid-cols-4 items-center gap-4",children:(0,n.jsx)(v.p,{id:"title",name:"title",placeholder:"Section title...",className:"col-span-4","data-sentry-element":"Input","data-sentry-source-file":"new-section-dialog.tsx"})})}),(0,n.jsx)(N.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"new-section-dialog.tsx",children:(0,n.jsx)(N.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"new-section-dialog.tsx",children:(0,n.jsx)(f.$,{type:"submit",size:"sm",form:"todo-form","data-sentry-element":"Button","data-sentry-source-file":"new-section-dialog.tsx",children:"Add Section"})})})]})]})}function A(){let e=(0,o.O)(e=>e.columns),t=(0,o.O)(e=>e.setCols),a=(0,r.useRef)("TODO"),c=(0,r.useMemo)(()=>e.map(e=>e.id),[e]),u=(0,o.O)(e=>e.tasks),m=(0,o.O)(e=>e.setTasks),[f,g]=(0,r.useState)(null),[x,y]=(0,r.useState)(!1),[p,v]=(0,r.useState)(null),b=(0,l.FR)((0,l.MS)(l.cA),(0,l.MS)(l.IG));if((0,r.useEffect)(()=>{y(!0)},[x]),(0,r.useEffect)(()=>{o.O.persist.rehydrate()},[]),x)return(0,n.jsxs)(l.Mp,{accessibility:{announcements:{onDragStart(t){var n,r;let{active:s}=t;if(i(s)){if((null==(n=s.data.current)?void 0:n.type)==="Column"){let t=c.findIndex(e=>e===s.id),a=e[t];return"Picked up Column ".concat(null==a?void 0:a.title," at position: ").concat(t+1," of ").concat(c.length)}else if((null==(r=s.data.current)?void 0:r.type)==="Task"){a.current=s.data.current.task.status;let{tasksInColumn:e,taskPosition:t,column:n}=h(s.id,a.current);return"Picked up Task ".concat(s.data.current.task.title," at position: ").concat(t+1," of ").concat(e.length," in column ").concat(null==n?void 0:n.title)}}},onDragOver(e){var t,n,r,s;let{active:o,over:l}=e;if(i(o)&&i(l)){if((null==(t=o.data.current)?void 0:t.type)==="Column"&&(null==(n=l.data.current)?void 0:n.type)==="Column"){let e=c.findIndex(e=>e===l.id);return"Column ".concat(o.data.current.column.title," was moved over ").concat(l.data.current.column.title," at position ").concat(e+1," of ").concat(c.length)}else if((null==(r=o.data.current)?void 0:r.type)==="Task"&&(null==(s=l.data.current)?void 0:s.type)==="Task"){let{tasksInColumn:e,taskPosition:t,column:n}=h(l.id,l.data.current.task.status);return l.data.current.task.status!==a.current?"Task ".concat(o.data.current.task.title," was moved over column ").concat(null==n?void 0:n.title," in position ").concat(t+1," of ").concat(e.length):"Task was moved over position ".concat(t+1," of ").concat(e.length," in column ").concat(null==n?void 0:n.title)}}},onDragEnd(e){var t,n,r,s;let{active:o,over:l}=e;if(!i(o)||!i(l)){a.current="TODO";return}if((null==(t=o.data.current)?void 0:t.type)==="Column"&&(null==(n=l.data.current)?void 0:n.type)==="Column"){let e=c.findIndex(e=>e===l.id);return"Column ".concat(o.data.current.column.title," was dropped into position ").concat(e+1," of ").concat(c.length)}if((null==(r=o.data.current)?void 0:r.type)==="Task"&&(null==(s=l.data.current)?void 0:s.type)==="Task"){let{tasksInColumn:e,taskPosition:t,column:n}=h(l.id,l.data.current.task.status);return l.data.current.task.status!==a.current?"Task was dropped into column ".concat(null==n?void 0:n.title," in position ").concat(t+1," of ").concat(e.length):"Task was dropped into position ".concat(t+1," of ").concat(e.length," in column ").concat(null==n?void 0:n.title)}a.current="TODO"},onDragCancel(e){var t;let{active:n}=e;if(a.current="TODO",i(n))return"Dragging ".concat(null==(t=n.data.current)?void 0:t.type," cancelled.")}}},sensors:b,onDragStart:function(e){if(!i(e.active))return;let t=e.active.data.current;return(null==t?void 0:t.type)==="Column"?void g(t.column):(null==t?void 0:t.type)==="Task"?void v(t.task):void 0},onDragEnd:function(a){g(null),v(null);let{active:n,over:r}=a;if(!r)return;let s=n.id,o=r.id;if(!i(n))return;let l=n.data.current;if(s===o||(null==l?void 0:l.type)!=="Column")return;let c=e.findIndex(e=>e.id===s),u=e.findIndex(e=>e.id===o);t((0,d.be)(e,c,u))},onDragOver:function(e){let{active:t,over:a}=e;if(!a)return;let n=t.id,r=a.id;if(n===r||!i(t)||!i(a))return;let s=t.data.current,o=a.data.current,l=(null==s?void 0:s.type)==="Task",c=(null==s?void 0:s.type)==="Task";if(!l)return;if(l&&c){let e=u.findIndex(e=>e.id===n),t=u.findIndex(e=>e.id===r),a=u[e],s=u[t];a&&s&&a.status!==s.status&&(a.status=s.status,m((0,d.be)(u,e,t-1))),m((0,d.be)(u,e,t))}let f=(null==o?void 0:o.type)==="Column";if(l&&f){let e=u.findIndex(e=>e.id===n),t=u[e];t&&(t.status=r,m((0,d.be)(u,e,e)))}},"data-sentry-element":"DndContext","data-sentry-component":"KanbanBoard","data-sentry-source-file":"kanban-board.tsx",children:[(0,n.jsx)(C,{"data-sentry-element":"BoardContainer","data-sentry-source-file":"kanban-board.tsx",children:(0,n.jsxs)(d.gB,{items:c,"data-sentry-element":"SortableContext","data-sentry-source-file":"kanban-board.tsx",children:[null==e?void 0:e.map((t,a)=>(0,n.jsxs)(r.Fragment,{children:[(0,n.jsx)(k,{column:t,tasks:u.filter(e=>e.status===t.id)}),a===(null==e?void 0:e.length)-1&&(0,n.jsx)("div",{className:"w-[300px]",children:(0,n.jsx)(T,{})})]},t.id)),!e.length&&(0,n.jsx)(T,{})]})}),"document"in window&&(0,s.createPortal)((0,n.jsxs)(l.Hd,{children:[f&&(0,n.jsx)(k,{isOverlay:!0,column:f,tasks:u.filter(e=>e.status===f.id)}),p&&(0,n.jsx)(j,{task:p,isOverlay:!0})]}),document.body)]});function h(t,a){let n=u.filter(e=>e.status===a),r=n.findIndex(e=>e.id===t);return{tasksInColumn:n,taskPosition:r,column:e.find(e=>e.id===a)}}}},88021:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var n=a(95155);a(12115);var r=a(32467),s=a(83101),o=a(64269);let i=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:a,asChild:s=!1,...l}=e,d=s?r.DX:"span";return(0,n.jsx)(d,{"data-slot":"badge",className:(0,o.cn)(i({variant:a}),t),...l,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},91620:(e,t,a)=>{"use strict";a.d(t,{O:()=>l});var n=a(25959),r=a(74879),s=a(89863);let o=[{id:"TODO",title:"Todo"}],i=[{id:"task1",status:"TODO",title:"Project initiation and planning"},{id:"task2",status:"TODO",title:"Gather requirements from stakeholders"}],l=(0,n.v)()((0,s.Zr)(e=>({tasks:i,columns:o,draggedTask:null,addTask:(t,a)=>e(e=>({tasks:[...e.tasks,{id:(0,r.A)(),title:t,description:a,status:"TODO"}]})),updateCol:(t,a)=>e(e=>({columns:e.columns.map(e=>e.id===t?{...e,title:a}:e)})),addCol:t=>e(e=>({columns:[...e.columns,{title:t,id:e.columns.length?t.toUpperCase():"TODO"}]})),dragTask:t=>e({draggedTask:t}),removeTask:t=>e(e=>({tasks:e.tasks.filter(e=>e.id!==t)})),removeCol:t=>e(e=>({columns:e.columns.filter(e=>e.id!==t)})),setTasks:t=>e({tasks:t}),setCols:t=>e({columns:t})}),{name:"task-store",skipHydration:!0}))}},e=>{var t=t=>e(e.s=t);e.O(0,[5105,4909,4736,660,8720,6093,7971,9034,4680,4850,8441,3840,7358],()=>t(25484)),_N_E=e.O()}]);