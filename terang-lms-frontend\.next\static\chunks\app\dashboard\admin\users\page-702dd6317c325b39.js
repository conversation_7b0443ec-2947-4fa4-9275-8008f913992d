try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="c14c197d-dad6-4533-8c65-029d388aecff",e._sentryDebugIdIdentifier="sentry-dbid-c14c197d-dad6-4533-8c65-029d388aecff")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4274],{6191:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},11010:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},12833:(e,t,a)=>{"use strict";a.d(t,{I:()=>c,SQ:()=>o,_2:()=>u,hO:()=>m,lp:()=>x,mB:()=>p,rI:()=>l,ty:()=>i});var s=a(95155);a(12115);var r=a(47971),n=a(5917),d=a(64269);function l(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"dropdown-menu",...t,"data-sentry-element":"DropdownMenuPrimitive.Root","data-sentry-component":"DropdownMenu","data-sentry-source-file":"dropdown-menu.tsx"})}function i(e){let{...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"dropdown-menu-trigger",...t,"data-sentry-element":"DropdownMenuPrimitive.Trigger","data-sentry-component":"DropdownMenuTrigger","data-sentry-source-file":"dropdown-menu.tsx"})}function o(e){let{className:t,sideOffset:a=4,...n}=e;return(0,s.jsx)(r.ZL,{"data-sentry-element":"DropdownMenuPrimitive.Portal","data-sentry-component":"DropdownMenuContent","data-sentry-source-file":"dropdown-menu.tsx",children:(0,s.jsx)(r.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...n,"data-sentry-element":"DropdownMenuPrimitive.Content","data-sentry-source-file":"dropdown-menu.tsx"})})}function c(e){let{...t}=e;return(0,s.jsx)(r.YJ,{"data-slot":"dropdown-menu-group",...t,"data-sentry-element":"DropdownMenuPrimitive.Group","data-sentry-component":"DropdownMenuGroup","data-sentry-source-file":"dropdown-menu.tsx"})}function u(e){let{className:t,inset:a,variant:n="default",...l}=e;return(0,s.jsx)(r.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":n,className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground focus:[&_svg:not([class*='text-'])]:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,"data-sentry-element":"DropdownMenuPrimitive.Item","data-sentry-component":"DropdownMenuItem","data-sentry-source-file":"dropdown-menu.tsx"})}function m(e){let{className:t,children:a,checked:l,...i}=e;return(0,s.jsxs)(r.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:l,...i,"data-sentry-element":"DropdownMenuPrimitive.CheckboxItem","data-sentry-component":"DropdownMenuCheckboxItem","data-sentry-source-file":"dropdown-menu.tsx",children:[(0,s.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{"data-sentry-element":"DropdownMenuPrimitive.ItemIndicator","data-sentry-source-file":"dropdown-menu.tsx",children:(0,s.jsx)(n.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"dropdown-menu.tsx"})})}),a]})}function x(e){let{className:t,inset:a,...n}=e;return(0,s.jsx)(r.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,d.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...n,"data-sentry-element":"DropdownMenuPrimitive.Label","data-sentry-component":"DropdownMenuLabel","data-sentry-source-file":"dropdown-menu.tsx"})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(r.wv,{"data-slot":"dropdown-menu-separator",className:(0,d.cn)("bg-border -mx-1 my-1 h-px",t),...a,"data-sentry-element":"DropdownMenuPrimitive.Separator","data-sentry-component":"DropdownMenuSeparator","data-sentry-source-file":"dropdown-menu.tsx"})}},15894:(e,t,a)=>{"use strict";a.d(t,{d:()=>r});var s=a(18720);function r(){return{toast:e=>{let{title:t,description:a,variant:r="default"}=e;"destructive"===r?s.oR.error(t,{description:a}):s.oR.success(t,{description:a})}}}},19408:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},20508:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},20764:(e,t,a)=>{"use strict";a.d(t,{$:()=>i,r:()=>l});var s=a(95155);a(12115);var r=a(32467),n=a(83101),d=a(64269);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:a,size:n,asChild:i=!1,...o}=e,c=i?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,d.cn)(l({variant:a,size:n,className:t})),...o,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},25532:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>x,gC:()=>m,l6:()=>o,yv:()=>c});var s=a(95155);a(12115);var r=a(47887),n=a(24033),d=a(5917),l=a(12108),i=a(64269);function o(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t,"data-sentry-element":"SelectPrimitive.Root","data-sentry-component":"Select","data-sentry-source-file":"select.tsx"})}function c(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t,"data-sentry-element":"SelectPrimitive.Value","data-sentry-component":"SelectValue","data-sentry-source-file":"select.tsx"})}function u(e){let{className:t,size:a="default",children:d,...l}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,"data-sentry-element":"SelectPrimitive.Trigger","data-sentry-component":"SelectTrigger","data-sentry-source-file":"select.tsx",children:[d,(0,s.jsx)(r.In,{asChild:!0,"data-sentry-element":"SelectPrimitive.Icon","data-sentry-source-file":"select.tsx",children:(0,s.jsx)(n.A,{className:"size-4 opacity-50","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})]})}function m(e){let{className:t,children:a,position:n="popper",...d}=e;return(0,s.jsx)(r.ZL,{"data-sentry-element":"SelectPrimitive.Portal","data-sentry-component":"SelectContent","data-sentry-source-file":"select.tsx",children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...d,"data-sentry-element":"SelectPrimitive.Content","data-sentry-source-file":"select.tsx",children:[(0,s.jsx)(p,{"data-sentry-element":"SelectScrollUpButton","data-sentry-source-file":"select.tsx"}),(0,s.jsx)(r.LM,{className:(0,i.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),"data-sentry-element":"SelectPrimitive.Viewport","data-sentry-source-file":"select.tsx",children:a}),(0,s.jsx)(f,{"data-sentry-element":"SelectScrollDownButton","data-sentry-source-file":"select.tsx"})]})})}function x(e){let{className:t,children:a,...n}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,"data-sentry-element":"SelectPrimitive.Item","data-sentry-component":"SelectItem","data-sentry-source-file":"select.tsx",children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{"data-sentry-element":"SelectPrimitive.ItemIndicator","data-sentry-source-file":"select.tsx",children:(0,s.jsx)(d.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"select.tsx"})})}),(0,s.jsx)(r.p4,{"data-sentry-element":"SelectPrimitive.ItemText","data-sentry-source-file":"select.tsx",children:a})]})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,"data-sentry-element":"SelectPrimitive.ScrollUpButton","data-sentry-component":"SelectScrollUpButton","data-sentry-source-file":"select.tsx",children:(0,s.jsx)(l.A,{className:"size-4","data-sentry-element":"ChevronUpIcon","data-sentry-source-file":"select.tsx"})})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,"data-sentry-element":"SelectPrimitive.ScrollDownButton","data-sentry-component":"SelectScrollDownButton","data-sentry-source-file":"select.tsx",children:(0,s.jsx)(n.A,{className:"size-4","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})}},31936:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var s=a(95155);a(12115);var r=a(64269);function n(e){let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},35299:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},37772:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},58545:(e,t,a)=>{Promise.resolve().then(a.bind(a,69072))},64269:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n,z:()=>d});var s=a(2821),r=a(75889);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}function d(e){var t,a;let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:r=0,sizeType:n="normal"}=s;if(0===e)return"0 Byte";let d=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,d)).toFixed(r)," ").concat("accurate"===n?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][d])?t:"Bytest":null!=(a=["Bytes","KB","MB","GB","TB"][d])?a:"Bytes")}},66094:(e,t,a)=>{"use strict";a.d(t,{BT:()=>i,Wu:()=>o,ZB:()=>l,Zp:()=>n,aR:()=>d,wL:()=>c});var s=a(95155);a(12115);var r=a(64269);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},69072:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>k});var s=a(95155),r=a(12115),n=a(66094),d=a(20764),l=a(31936),i=a(88021),o=a(25532),c=a(88864),u=a(12833),m=a(6191),x=a(86651);let p=(0,a(71847).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);var f=a(19408),y=a(35299),h=a(20508),g=a(37772),v=a(11010),b=a(71360),w=a(52619),j=a.n(w),N=a(15894);function k(){let[e,t]=(0,r.useState)(""),[a,w]=(0,r.useState)("all"),[k,S]=(0,r.useState)("all"),[T,C]=(0,r.useState)([]),[_,A]=(0,r.useState)([]),[M,I]=(0,r.useState)(!0),[z,B]=(0,r.useState)(null),{toast:D}=(0,N.d)(),P=(0,r.useRef)(!0),H=(0,r.useCallback)(async()=>{try{let e=await fetch("/api/institutions"),t=await e.json();t.success&&A(t.data.institutions)}catch(e){console.error("Error fetching institutions:",e)}},[]),E=(0,r.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"all";try{I(!0);let s=new URLSearchParams;e&&s.append("search",e),"all"!==t&&s.append("role",t),"all"!==a&&s.append("institution_id",a);let r=await fetch("/api/users?".concat(s.toString())),n=await r.json();n.success?C(n.data.users):D({title:"Error",description:n.error||"Failed to fetch users",variant:"destructive"})}catch(e){console.error("Error fetching users:",e),D({title:"Error",description:"Failed to fetch users",variant:"destructive"})}finally{I(!1)}},[]),R=async t=>{if(confirm("Are you sure you want to delete this user? This action cannot be undone."))try{B(t);let s=await fetch("/api/users/".concat(t),{method:"DELETE"}),r=await s.json();r.success?(D({title:"Success",description:"User deleted successfully"}),E(e,a,k)):D({title:"Error",description:r.error||"Failed to delete user",variant:"destructive"})}catch(e){console.error("Error deleting user:",e),D({title:"Error",description:"Failed to delete user",variant:"destructive"})}finally{B(null)}};(0,r.useEffect)(()=>{H(),E()},[]),(0,r.useEffect)(()=>{if(P.current){P.current=!1;return}let t=setTimeout(()=>{E(e,a,k)},500);return()=>clearTimeout(t)},[e,a,k,E]);let U=e=>{let t="super_admin"===e?"default":"teacher"===e?"secondary":"outline";return(0,s.jsx)(i.E,{variant:t,"data-sentry-element":"Badge","data-sentry-component":"getRoleBadge","data-sentry-source-file":"page.tsx",children:e.replace("_"," ")})};return(0,s.jsxs)("div",{className:"space-y-6","data-sentry-component":"UsersPage","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Users"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Manage all users on the platform"})]}),(0,s.jsx)(j(),{href:"/dashboard/admin/users/new","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(d.$,{"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Plus","data-sentry-source-file":"page.tsx"}),"Add User"]})})]}),(0,s.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(n.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"All Users"}),(0,s.jsx)(n.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"View and manage all registered users"})]}),(0,s.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"mb-4 flex items-center space-x-2",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(x.A,{className:"text-muted-foreground absolute top-2.5 left-2 h-4 w-4","data-sentry-element":"Search","data-sentry-source-file":"page.tsx"}),(0,s.jsx)(l.p,{placeholder:"Search users by name or email...",value:e,onChange:e=>t(e.target.value),className:"pl-8","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(o.l6,{value:a,onValueChange:w,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(o.bq,{className:"w-[150px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(p,{className:"mr-2 h-4 w-4","data-sentry-element":"Filter","data-sentry-source-file":"page.tsx"}),(0,s.jsx)(o.yv,{placeholder:"Role","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(o.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(o.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"All Roles"}),(0,s.jsx)(o.eb,{value:"student","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Student"}),(0,s.jsx)(o.eb,{value:"teacher","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Teacher"}),(0,s.jsx)(o.eb,{value:"super_admin","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Super Admin"})]})]}),(0,s.jsxs)(o.l6,{value:k,onValueChange:S,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(o.bq,{className:"w-[200px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(f.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Building2","data-sentry-source-file":"page.tsx"}),(0,s.jsx)(o.yv,{placeholder:"Institution","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(o.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(o.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"All Institutions"}),(0,s.jsx)(o.eb,{value:"null","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"No Institution"}),_.map(e=>(0,s.jsx)(o.eb,{value:e.id.toString(),children:e.name},e.id))]})]})]}),(0,s.jsx)("div",{className:"rounded-md border",children:(0,s.jsxs)(c.Table,{"data-sentry-element":"Table","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(c.TableHeader,{"data-sentry-element":"TableHeader","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(c.TableRow,{"data-sentry-element":"TableRow","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(c.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"User"}),(0,s.jsx)(c.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Role"}),(0,s.jsx)(c.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Institution"}),(0,s.jsx)(c.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Created"}),(0,s.jsx)(c.TableHead,{className:"w-[70px]","data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Actions"})]})}),(0,s.jsx)(c.TableBody,{"data-sentry-element":"TableBody","data-sentry-source-file":"page.tsx",children:M?(0,s.jsx)(c.TableRow,{children:(0,s.jsxs)(c.TableCell,{colSpan:5,className:"py-8 text-center",children:[(0,s.jsx)(y.A,{className:"mx-auto h-6 w-6 animate-spin"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-2 text-sm",children:"Loading users..."})]})}):0===T.length?(0,s.jsx)(c.TableRow,{children:(0,s.jsxs)(c.TableCell,{colSpan:5,className:"py-8 text-center",children:[(0,s.jsx)(h.A,{className:"text-muted-foreground mx-auto mb-4 h-12 w-12"}),(0,s.jsx)("h3",{className:"mb-2 text-sm font-semibold",children:"No users found"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4 text-sm",children:e||"all"!==a||"all"!==k?"Try adjusting your search or filters.":"Get started by adding a new user."}),!e&&"all"===a&&"all"===k&&(0,s.jsx)(j(),{href:"/dashboard/admin/users/new",children:(0,s.jsxs)(d.$,{children:[(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Add User"]})})]})}):T.map(e=>(0,s.jsxs)(c.TableRow,{children:[(0,s.jsx)(c.TableCell,{children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(h.A,{className:"text-muted-foreground h-4 w-4"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:e.name}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:e.email})]})]})}),(0,s.jsx)(c.TableCell,{children:U(e.role)}),(0,s.jsx)(c.TableCell,{children:e.institution_name?(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)(f.A,{className:"text-muted-foreground h-3 w-3"}),(0,s.jsx)("span",{className:"text-sm",children:e.institution_name})]}):(0,s.jsx)("span",{className:"text-muted-foreground text-sm",children:"No institution"})}),(0,s.jsx)(c.TableCell,{children:(0,s.jsx)("span",{className:"text-sm",children:new Date(e.created_at).toLocaleDateString()})}),(0,s.jsx)(c.TableCell,{children:(0,s.jsxs)(u.rI,{children:[(0,s.jsx)(u.ty,{asChild:!0,children:(0,s.jsx)(d.$,{variant:"ghost",className:"h-8 w-8 p-0",disabled:z===e.id,children:z===e.id?(0,s.jsx)(y.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(g.A,{className:"h-4 w-4"})})}),(0,s.jsxs)(u.SQ,{align:"end",children:[(0,s.jsx)(u._2,{asChild:!0,children:(0,s.jsxs)(j(),{href:"/dashboard/admin/users/".concat(e.id),children:[(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Edit"]})}),(0,s.jsxs)(u._2,{className:"text-red-600",onClick:()=>R(e.id),disabled:z===e.id||"super_admin"===e.role,children:[(0,s.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},e.id))})]})})]})]})]})}},71360:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},86651:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},88021:(e,t,a)=>{"use strict";a.d(t,{E:()=>i});var s=a(95155);a(12115);var r=a(32467),n=a(83101),d=a(64269);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:a,asChild:n=!1,...i}=e,o=n?r.DX:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,d.cn)(l({variant:a}),t),...i,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},88864:(e,t,a)=>{"use strict";a.d(t,{Table:()=>n,TableBody:()=>l,TableCell:()=>c,TableHead:()=>o,TableHeader:()=>d,TableRow:()=>i});var s=a(95155);a(12115);var r=a(64269);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto","data-sentry-component":"Table","data-sentry-source-file":"table.tsx",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",t),...a})})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",t),...a,"data-sentry-component":"TableHeader","data-sentry-source-file":"table.tsx"})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",t),...a,"data-sentry-component":"TableBody","data-sentry-source-file":"table.tsx"})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a,"data-sentry-component":"TableRow","data-sentry-source-file":"table.tsx"})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a,"data-sentry-component":"TableHead","data-sentry-source-file":"table.tsx"})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a,"data-sentry-component":"TableCell","data-sentry-source-file":"table.tsx"})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,7055,4736,660,8720,6093,9568,7971,4850,8441,3840,7358],()=>t(58545)),_N_E=e.O()}]);