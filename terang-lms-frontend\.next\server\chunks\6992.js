try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},a=(new e.Error).stack;a&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[a]="efab67e7-61f0-4b9d-95f8-e5c629bc5c5e",e._sentryDebugIdIdentifier="sentry-dbid-efab67e7-61f0-4b9d-95f8-e5c629bc5c5e")}catch(e){}"use strict";exports.id=6992,exports.ids=[6992],exports.modules={4978:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},6410:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},8238:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},11477:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("Hourglass",[["path",{d:"M5 22h14",key:"ehvnwv"}],["path",{d:"M5 2h14",key:"pdyrp9"}],["path",{d:"M17 22v-4.172a2 2 0 0 0-.586-1.414L12 12l-4.414 4.414A2 2 0 0 0 7 17.828V22",key:"1d314k"}],["path",{d:"M7 2v4.172a2 2 0 0 0 .586 1.414L12 12l4.414-4.414A2 2 0 0 0 17 6.172V2",key:"1vvvr6"}]])},12258:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("ClipboardList",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},12793:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]])},14687:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("LifeBuoy",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m4.93 4.93 4.24 4.24",key:"1ymg45"}],["path",{d:"m14.83 9.17 4.24-4.24",key:"1cb5xl"}],["path",{d:"m14.83 14.83 4.24 4.24",key:"q42g0n"}],["path",{d:"m9.17 14.83-4.24 4.24",key:"bqpfvv"}],["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}]])},14908:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},15019:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("Rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]])},22646:(e,a,t)=>{t.d(a,{C1:()=>w,bL:()=>M});var r=t(93491),i=t(42014),n=t(10158),d=t(18682),o=t(76322),l=t(78476),s=t(96432),h=t(55462),u=t(90604),c=t(91754),m="Checkbox",[p,y]=(0,n.A)(m),[f,k]=p(m);function g(e){let{__scopeCheckbox:a,checked:t,children:i,defaultChecked:n,disabled:d,form:l,name:s,onCheckedChange:h,required:u,value:p="on",internal_do_not_use_render:y}=e,[k,g]=(0,o.i)({prop:t,defaultProp:n??!1,onChange:h,caller:m}),[b,v]=r.useState(null),[M,x]=r.useState(null),w=r.useRef(!1),A=!b||!!l||!!b.closest("form"),j={checked:k,disabled:d,setChecked:g,control:b,setControl:v,name:s,form:l,value:p,hasConsumerStoppedPropagationRef:w,required:u,defaultChecked:!P(n)&&n,isFormControl:A,bubbleInput:M,setBubbleInput:x};return(0,c.jsx)(f,{scope:a,...j,children:"function"==typeof y?y(j):i})}var b="CheckboxTrigger",v=r.forwardRef(({__scopeCheckbox:e,onKeyDown:a,onClick:t,...n},o)=>{let{control:l,value:s,disabled:h,checked:m,required:p,setControl:y,setChecked:f,hasConsumerStoppedPropagationRef:g,isFormControl:v,bubbleInput:M}=k(b,e),x=(0,i.s)(o,y),w=r.useRef(m);return r.useEffect(()=>{let e=l?.form;if(e){let a=()=>f(w.current);return e.addEventListener("reset",a),()=>e.removeEventListener("reset",a)}},[l,f]),(0,c.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":P(m)?"mixed":m,"aria-required":p,"data-state":S(m),"data-disabled":h?"":void 0,disabled:h,value:s,...n,ref:x,onKeyDown:(0,d.m)(a,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,d.m)(t,e=>{f(e=>!!P(e)||!e),M&&v&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})})});v.displayName=b;var M=r.forwardRef((e,a)=>{let{__scopeCheckbox:t,name:r,checked:i,defaultChecked:n,required:d,disabled:o,value:l,onCheckedChange:s,form:h,...u}=e;return(0,c.jsx)(g,{__scopeCheckbox:t,checked:i,defaultChecked:n,disabled:o,required:d,onCheckedChange:s,name:r,form:h,value:l,internal_do_not_use_render:({isFormControl:e})=>(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(v,{...u,ref:a,__scopeCheckbox:t}),e&&(0,c.jsx)(j,{__scopeCheckbox:t})]})})});M.displayName=m;var x="CheckboxIndicator",w=r.forwardRef((e,a)=>{let{__scopeCheckbox:t,forceMount:r,...i}=e,n=k(x,t);return(0,c.jsx)(h.C,{present:r||P(n.checked)||!0===n.checked,children:(0,c.jsx)(u.sG.span,{"data-state":S(n.checked),"data-disabled":n.disabled?"":void 0,...i,ref:a,style:{pointerEvents:"none",...e.style}})})});w.displayName=x;var A="CheckboxBubbleInput",j=r.forwardRef(({__scopeCheckbox:e,...a},t)=>{let{control:n,hasConsumerStoppedPropagationRef:d,checked:o,defaultChecked:h,required:m,disabled:p,name:y,value:f,form:g,bubbleInput:b,setBubbleInput:v}=k(A,e),M=(0,i.s)(t,v),x=(0,l.Z)(o),w=(0,s.X)(n);r.useEffect(()=>{if(!b)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,a=!d.current;if(x!==o&&e){let t=new Event("click",{bubbles:a});b.indeterminate=P(o),e.call(b,!P(o)&&o),b.dispatchEvent(t)}},[b,x,o,d]);let j=r.useRef(!P(o)&&o);return(0,c.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:h??j.current,required:m,disabled:p,name:y,value:f,form:g,...a,tabIndex:-1,ref:M,style:{...a.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function P(e){return"indeterminate"===e}function S(e){return P(e)?"indeterminate":e?"checked":"unchecked"}j.displayName=A},24727:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},28235:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},28280:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("Type",[["polyline",{points:"4 7 4 4 20 4 20 7",key:"1nosan"}],["line",{x1:"9",x2:"15",y1:"20",y2:"20",key:"swin9y"}],["line",{x1:"12",x2:"12",y1:"4",y2:"20",key:"1tx1rr"}]])},29602:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},31667:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},33772:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("Text",[["path",{d:"M17 6.1H3",key:"wptmhv"}],["path",{d:"M21 12.1H3",key:"1j38uz"}],["path",{d:"M15.1 18H3",key:"1nb16a"}]])},36445:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("Navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]])},37607:(e,a,t)=>{t.d(a,{bL:()=>x,zi:()=>w});var r=t(93491),i=t(18682),n=t(42014),d=t(10158),o=t(76322),l=t(78476),s=t(96432),h=t(90604),u=t(91754),c="Switch",[m,p]=(0,d.A)(c),[y,f]=m(c),k=r.forwardRef((e,a)=>{let{__scopeSwitch:t,name:d,checked:l,defaultChecked:s,required:m,disabled:p,value:f="on",onCheckedChange:k,form:g,...b}=e,[x,w]=r.useState(null),A=(0,n.s)(a,e=>w(e)),j=r.useRef(!1),P=!x||g||!!x.closest("form"),[S,D]=(0,o.i)({prop:l,defaultProp:s??!1,onChange:k,caller:c});return(0,u.jsxs)(y,{scope:t,checked:S,disabled:p,children:[(0,u.jsx)(h.sG.button,{type:"button",role:"switch","aria-checked":S,"aria-required":m,"data-state":M(S),"data-disabled":p?"":void 0,disabled:p,value:f,...b,ref:A,onClick:(0,i.m)(e.onClick,e=>{D(e=>!e),P&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})}),P&&(0,u.jsx)(v,{control:x,bubbles:!j.current,name:d,value:f,checked:S,required:m,disabled:p,form:g,style:{transform:"translateX(-100%)"}})]})});k.displayName=c;var g="SwitchThumb",b=r.forwardRef((e,a)=>{let{__scopeSwitch:t,...r}=e,i=f(g,t);return(0,u.jsx)(h.sG.span,{"data-state":M(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:a})});b.displayName=g;var v=r.forwardRef(({__scopeSwitch:e,control:a,checked:t,bubbles:i=!0,...d},o)=>{let h=r.useRef(null),c=(0,n.s)(h,o),m=(0,l.Z)(t),p=(0,s.X)(a);return r.useEffect(()=>{let e=h.current;if(!e)return;let a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==t&&a){let r=new Event("click",{bubbles:i});a.call(e,t),e.dispatchEvent(r)}},[m,t,i]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...d,tabIndex:-1,ref:c,style:{...d.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function M(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var x=k,w=b},42352:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},44620:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("MonitorPlay",[["path",{d:"M10 7.75a.75.75 0 0 1 1.142-.638l3.664 2.249a.75.75 0 0 1 0 1.278l-3.664 2.25a.75.75 0 0 1-1.142-.64z",key:"1pctta"}],["path",{d:"M12 17v4",key:"1riwvh"}],["path",{d:"M8 21h8",key:"1ev6f3"}],["rect",{x:"2",y:"3",width:"20",height:"14",rx:"2",key:"x3v2xh"}]])},46934:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("Shuffle",[["path",{d:"m18 14 4 4-4 4",key:"10pe0f"}],["path",{d:"m18 2 4 4-4 4",key:"pucp1d"}],["path",{d:"M2 18h1.973a4 4 0 0 0 3.3-1.7l5.454-8.6a4 4 0 0 1 3.3-1.7H22",key:"1ailkh"}],["path",{d:"M2 6h1.972a4 4 0 0 1 3.6 2.2",key:"km57vx"}],["path",{d:"M22 18h-6.041a4 4 0 0 1-3.3-1.8l-.359-.45",key:"os18l9"}]])},54264:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("Video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},55863:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},57850:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]])},65090:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("HardHat",[["path",{d:"M10 10V5a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v5",key:"1p9q5i"}],["path",{d:"M14 6a6 6 0 0 1 6 6v3",key:"1hnv84"}],["path",{d:"M4 15v-3a6 6 0 0 1 6-6",key:"9ciidu"}],["rect",{x:"2",y:"15",width:"20",height:"4",rx:"1",key:"g3x8cw"}]])},66207:(e,a,t)=>{t.d(a,{b:()=>o});var r=t(93491),i=t(90604),n=t(91754),d=r.forwardRef((e,a)=>(0,n.jsx)(i.sG.label,{...e,ref:a,onMouseDown:a=>{a.target.closest("button, input, select, textarea")||(e.onMouseDown?.(a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));d.displayName="Label";var o=d},73562:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},82546:(e,a,t)=>{t.d(a,{UC:()=>L,VY:()=>q,ZD:()=>I,ZL:()=>E,bL:()=>C,hE:()=>K,hJ:()=>W,l9:()=>N,rc:()=>z});var r=t(93491),i=t(10158),n=t(42014),d=t(18227),o=t(18682),l=t(16435),s=t(91754),h="AlertDialog",[u,c]=(0,i.A)(h,[d.Hs]),m=(0,d.Hs)(),p=e=>{let{__scopeAlertDialog:a,...t}=e,r=m(a);return(0,s.jsx)(d.bL,{...r,...t,modal:!0})};p.displayName=h;var y=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,i=m(t);return(0,s.jsx)(d.l9,{...i,...r,ref:a})});y.displayName="AlertDialogTrigger";var f=e=>{let{__scopeAlertDialog:a,...t}=e,r=m(a);return(0,s.jsx)(d.ZL,{...r,...t})};f.displayName="AlertDialogPortal";var k=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,i=m(t);return(0,s.jsx)(d.hJ,{...i,...r,ref:a})});k.displayName="AlertDialogOverlay";var g="AlertDialogContent",[b,v]=u(g),M=(0,l.Dc)("AlertDialogContent"),x=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,children:i,...l}=e,h=m(t),u=r.useRef(null),c=(0,n.s)(a,u),p=r.useRef(null);return(0,s.jsx)(d.G$,{contentName:g,titleName:w,docsSlug:"alert-dialog",children:(0,s.jsx)(b,{scope:t,cancelRef:p,children:(0,s.jsxs)(d.UC,{role:"alertdialog",...h,...l,ref:c,onOpenAutoFocus:(0,o.m)(l.onOpenAutoFocus,e=>{e.preventDefault(),p.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(M,{children:i}),(0,s.jsx)(R,{contentRef:u})]})})})});x.displayName=g;var w="AlertDialogTitle",A=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,i=m(t);return(0,s.jsx)(d.hE,{...i,...r,ref:a})});A.displayName=w;var j="AlertDialogDescription",P=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,i=m(t);return(0,s.jsx)(d.VY,{...i,...r,ref:a})});P.displayName=j;var S=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,i=m(t);return(0,s.jsx)(d.bm,{...i,...r,ref:a})});S.displayName="AlertDialogAction";var D="AlertDialogCancel",H=r.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...r}=e,{cancelRef:i}=v(D,t),o=m(t),l=(0,n.s)(a,i);return(0,s.jsx)(d.bm,{...o,...r,ref:l})});H.displayName=D;var R=({contentRef:e})=>{let a=`\`${g}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${g}\` by passing a \`${j}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${g}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return r.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(a)},[a,e]),null},C=p,N=y,E=f,W=k,L=x,z=S,I=H,K=A,q=P},85108:(e,a,t)=>{t.d(a,{id:()=>h});let r={lessThanXSeconds:{one:"kurang dari 1 detik",other:"kurang dari {{count}} detik"},xSeconds:{one:"1 detik",other:"{{count}} detik"},halfAMinute:"setengah menit",lessThanXMinutes:{one:"kurang dari 1 menit",other:"kurang dari {{count}} menit"},xMinutes:{one:"1 menit",other:"{{count}} menit"},aboutXHours:{one:"sekitar 1 jam",other:"sekitar {{count}} jam"},xHours:{one:"1 jam",other:"{{count}} jam"},xDays:{one:"1 hari",other:"{{count}} hari"},aboutXWeeks:{one:"sekitar 1 minggu",other:"sekitar {{count}} minggu"},xWeeks:{one:"1 minggu",other:"{{count}} minggu"},aboutXMonths:{one:"sekitar 1 bulan",other:"sekitar {{count}} bulan"},xMonths:{one:"1 bulan",other:"{{count}} bulan"},aboutXYears:{one:"sekitar 1 tahun",other:"sekitar {{count}} tahun"},xYears:{one:"1 tahun",other:"{{count}} tahun"},overXYears:{one:"lebih dari 1 tahun",other:"lebih dari {{count}} tahun"},almostXYears:{one:"hampir 1 tahun",other:"hampir {{count}} tahun"}};var i=t(79508);let n={date:(0,i.k)({formats:{full:"EEEE, d MMMM yyyy",long:"d MMMM yyyy",medium:"d MMM yyyy",short:"d/M/yyyy"},defaultWidth:"full"}),time:(0,i.k)({formats:{full:"HH.mm.ss",long:"HH.mm.ss",medium:"HH.mm",short:"HH.mm"},defaultWidth:"full"}),dateTime:(0,i.k)({formats:{full:"{{date}} 'pukul' {{time}}",long:"{{date}} 'pukul' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},d={lastWeek:"eeee 'lalu pukul' p",yesterday:"'Kemarin pukul' p",today:"'Hari ini pukul' p",tomorrow:"'Besok pukul' p",nextWeek:"eeee 'pukul' p",other:"P"};var o=t(99158);let l={ordinalNumber:(e,a)=>"ke-"+Number(e),era:(0,o.o)({values:{narrow:["SM","M"],abbreviated:["SM","M"],wide:["Sebelum Masehi","Masehi"]},defaultWidth:"wide"}),quarter:(0,o.o)({values:{narrow:["1","2","3","4"],abbreviated:["K1","K2","K3","K4"],wide:["Kuartal ke-1","Kuartal ke-2","Kuartal ke-3","Kuartal ke-4"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,o.o)({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","Mei","Jun","Jul","Agt","Sep","Okt","Nov","Des"],wide:["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"]},defaultWidth:"wide"}),day:(0,o.o)({values:{narrow:["M","S","S","R","K","J","S"],short:["Min","Sen","Sel","Rab","Kam","Jum","Sab"],abbreviated:["Min","Sen","Sel","Rab","Kam","Jum","Sab"],wide:["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"]},defaultWidth:"wide"}),dayPeriod:(0,o.o)({values:{narrow:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"},abbreviated:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"},wide:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"}},defaultWidth:"wide",formattingValues:{narrow:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"},abbreviated:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"},wide:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"}},defaultFormattingWidth:"wide"})};var s=t(3024);let h={code:"id",formatDistance:(e,a,t)=>{let i,n=r[e];if(i="string"==typeof n?n:1===a?n.one:n.other.replace("{{count}}",a.toString()),t?.addSuffix)if(t.comparison&&t.comparison>0)return"dalam waktu "+i;else return i+" yang lalu";return i},formatLong:n,formatRelative:(e,a,t,r)=>d[e],localize:l,match:{ordinalNumber:(0,t(41200).K)({matchPattern:/^ke-(\d+)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,s.A)({matchPatterns:{narrow:/^(sm|m)/i,abbreviated:/^(s\.?\s?m\.?|s\.?\s?e\.?\s?u\.?|m\.?|e\.?\s?u\.?)/i,wide:/^(sebelum masehi|sebelum era umum|masehi|era umum)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^s/i,/^(m|e)/i]},defaultParseWidth:"any"}),quarter:(0,s.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^K-?\s[1234]/i,wide:/^Kuartal ke-?\s?[1234]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,s.A)({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|mei|jun|jul|agt|sep|okt|nov|des)/i,wide:/^(januari|februari|maret|april|mei|juni|juli|agustus|september|oktober|november|desember)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^ma/i,/^ap/i,/^me/i,/^jun/i,/^jul/i,/^ag/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,s.A)({matchPatterns:{narrow:/^[srkjm]/i,short:/^(min|sen|sel|rab|kam|jum|sab)/i,abbreviated:/^(min|sen|sel|rab|kam|jum|sab)/i,wide:/^(minggu|senin|selasa|rabu|kamis|jumat|sabtu)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^m/i,/^s/i,/^s/i,/^r/i,/^k/i,/^j/i,/^s/i],any:[/^m/i,/^sen/i,/^sel/i,/^r/i,/^k/i,/^j/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:(0,s.A)({matchPatterns:{narrow:/^(a|p|tengah m|tengah h|(di(\swaktu)?) (pagi|siang|sore|malam))/i,any:/^([ap]\.?\s?m\.?|tengah malam|tengah hari|(di(\swaktu)?) (pagi|siang|sore|malam))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^pm/i,midnight:/^tengah m/i,noon:/^tengah h/i,morning:/pagi/i,afternoon:/siang/i,evening:/sore/i,night:/malam/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:1}}},99462:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(55732).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])}};
//# sourceMappingURL=6992.js.map