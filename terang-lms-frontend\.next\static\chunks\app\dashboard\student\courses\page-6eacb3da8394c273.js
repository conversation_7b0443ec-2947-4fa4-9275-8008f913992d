try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},s=(new e.Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="988edd11-ed6f-48ce-a7b2-24063b3fd033",e._sentryDebugIdIdentifier="sentry-dbid-988edd11-ed6f-48ce-a7b2-24063b3fd033")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1376],{9484:(e,s,t)=>{"use strict";t.d(s,{C1:()=>j,bL:()=>b});var a=t(12115),r=t(3468),n=t(97602),i=t(95155),l="Progress",[d,o]=(0,r.A)(l),[c,u]=d(l),m=a.forwardRef((e,s)=>{var t,a,r,l;let{__scopeProgress:d,value:o=null,max:u,getValueLabel:m=p,...x}=e;(u||0===u)&&!v(u)&&console.error((t="".concat(u),a="Progress","Invalid prop `max` of value `".concat(t,"` supplied to `").concat(a,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let f=v(u)?u:100;null===o||y(o,f)||console.error((r="".concat(o),l="Progress","Invalid prop `value` of value `".concat(r,"` supplied to `").concat(l,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let b=y(o,f)?o:null,j=g(b)?m(b,f):void 0;return(0,i.jsx)(c,{scope:d,value:b,max:f,children:(0,i.jsx)(n.sG.div,{"aria-valuemax":f,"aria-valuemin":0,"aria-valuenow":g(b)?b:void 0,"aria-valuetext":j,role:"progressbar","data-state":h(b,f),"data-value":null!=b?b:void 0,"data-max":f,...x,ref:s})})});m.displayName=l;var x="ProgressIndicator",f=a.forwardRef((e,s)=>{var t;let{__scopeProgress:a,...r}=e,l=u(x,a);return(0,i.jsx)(n.sG.div,{"data-state":h(l.value,l.max),"data-value":null!=(t=l.value)?t:void 0,"data-max":l.max,...r,ref:s})});function p(e,s){return"".concat(Math.round(e/s*100),"%")}function h(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function g(e){return"number"==typeof e}function v(e){return g(e)&&!isNaN(e)&&e>0}function y(e,s){return g(e)&&!isNaN(e)&&e<=s&&e>=0}f.displayName=x;var b=m,j=f},12800:(e,s,t)=>{"use strict";t.d(s,{Xi:()=>o,av:()=>c,j7:()=>d,tU:()=>l});var a=t(95155),r=t(12115),n=t(25667),i=t(64269);let l=n.bL,d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.B8,{ref:s,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...r})});d.displayName=n.B8.displayName;let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.l9,{ref:s,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm cursor-pointer",t),...r})});o.displayName=n.l9.displayName;let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(n.UC,{ref:s,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...r})});c.displayName=n.UC.displayName},20764:(e,s,t)=>{"use strict";t.d(s,{$:()=>d,r:()=>l});var a=t(95155);t(12115);var r=t(32467),n=t(83101),i=t(64269);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:t,size:n,asChild:d=!1,...o}=e,c=d?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:t,size:n,className:s})),...o,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},26737:(e,s,t)=>{"use strict";t.d(s,{k:()=>l});var a=t(95155),r=t(12115),n=t(9484),i=t(64269);let l=r.forwardRef((e,s)=>{let{className:t,value:r,...l}=e;return(0,a.jsx)(n.bL,{ref:s,className:(0,i.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...l,children:(0,a.jsx)(n.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});l.displayName=n.bL.displayName},26983:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(71847).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},31936:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var a=t(95155);t(12115);var r=t(64269);function n(e){let{className:s,type:t,...n}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...n,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},42529:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(71847).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},47886:(e,s,t)=>{"use strict";t.d(s,{WG:()=>r,cl:()=>i,qs:()=>a});let a={setUser:e=>{localStorage.setItem("auth_user",JSON.stringify(e))},getUser:()=>{{let e=localStorage.getItem("auth_user");return e?JSON.parse(e):null}},removeUser:()=>{localStorage.removeItem("auth_user")},isAuthenticated:()=>null!==a.getUser(),hasRole:e=>{let s=a.getUser();return(null==s?void 0:s.role)===e},isSuperAdmin:()=>a.hasRole("super_admin"),isTeacher:()=>a.hasRole("teacher"),isStudent:()=>a.hasRole("student")},r=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},n=()=>{let e=a.getUser();return e||(window.location.href="/auth/sign-in",null)},i=e=>{let s=n();return s?s.role!==e?(window.location.href=r(s),null):s:null}},47937:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(71847).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},52472:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(71847).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},64269:(e,s,t)=>{"use strict";t.d(s,{cn:()=>n,z:()=>i});var a=t(2821),r=t(75889);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}function i(e){var s,t;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:r=0,sizeType:n="normal"}=a;if(0===e)return"0 Byte";let i=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,i)).toFixed(r)," ").concat("accurate"===n?null!=(s=["Bytes","KiB","MiB","GiB","TiB"][i])?s:"Bytest":null!=(t=["Bytes","KB","MB","GB","TB"][i])?t:"Bytes")}},65163:(e,s,t)=>{Promise.resolve().then(t.bind(t,82947))},66094:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>c});var a=t(95155);t(12115);var r=t(64269);function n(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function o(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",s),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},82947:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var a=t(95155),r=t(12115),n=t(18720),i=t(47886),l=t(66094),d=t(20764),o=t(31936),c=t(88021),u=t(26737),m=t(12800),x=t(86651),f=t(26983),p=t(42529),h=t(52472),g=t(85921),v=t(47937),y=t(52619),b=t.n(y);function j(){let[e,s]=(0,r.useState)(""),[t,y]=(0,r.useState)(""),[j,N]=(0,r.useState)([]),[w,k]=(0,r.useState)([]),[C,A]=(0,r.useState)(!0),[B,R]=(0,r.useState)(!1);(0,r.useEffect)(()=>{T()},[]);let T=async()=>{try{let e=i.qs.getUser();if(!e)return void n.oR.error("Please log in to view courses");let s=await fetch("/api/enrollments?studentId=".concat(e.id));if(s.ok){let e=await s.json();N(e)}let t=await fetch("/api/courses");if(t.ok){let e=await t.json();k(e)}}catch(e){console.error("Error fetching courses:",e),n.oR.error("Failed to load courses")}finally{A(!1)}},S=async()=>{if(!t.trim())return;let e=i.qs.getUser();if(!e)return void n.oR.error("Please log in to enroll in courses");R(!0);try{let s=await fetch("/api/enrollments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({studentId:e.id,courseCode:t.trim()})});if(s.ok){let e=await s.json();n.oR.success("Successfully enrolled in ".concat(e.courseName,"!")),y(""),T()}else{let e=await s.json();n.oR.error(e.message||"Failed to enroll in course")}}catch(e){console.error("Enrollment error:",e),n.oR.error("An error occurred while enrolling")}finally{R(!1)}},_=j.filter(s=>s.name.toLowerCase().includes(e.toLowerCase())||s.description.toLowerCase().includes(e.toLowerCase())),E=w.filter(s=>s.name.toLowerCase().includes(e.toLowerCase())||s.description.toLowerCase().includes(e.toLowerCase()));return(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"StudentCoursesPage","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"My Courses"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Access your enrolled courses and discover new ones"})]})}),(0,a.jsxs)(l.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(l.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Quick Enrollment"}),(0,a.jsx)(l.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Enter a course code to quickly enroll in a course"})]}),(0,a.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(o.p,{placeholder:"Enter course code (e.g., MATH101)",value:t,onChange:e=>y(e.target.value.toUpperCase()),className:"flex-1","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(d.$,{onClick:S,disabled:!t.trim()||B,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:B?"Enrolling...":"Enroll"})]})})]}),(0,a.jsxs)(m.tU,{defaultValue:"enrolled",className:"space-y-6","data-sentry-element":"Tabs","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(m.j7,{"data-sentry-element":"TabsList","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(m.Xi,{value:"enrolled","data-sentry-element":"TabsTrigger","data-sentry-source-file":"page.tsx",children:"My Courses"}),(0,a.jsx)(m.Xi,{value:"available","data-sentry-element":"TabsTrigger","data-sentry-source-file":"page.tsx",children:"Available Courses"})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(x.A,{className:"text-muted-foreground absolute top-2.5 left-2 h-4 w-4","data-sentry-element":"Search","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(o.p,{placeholder:"Search courses...",value:e,onChange:e=>s(e.target.value),className:"pl-8","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]})}),(0,a.jsxs)(m.av,{value:"enrolled","data-sentry-element":"TabsContent","data-sentry-source-file":"page.tsx",children:[C?(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[1,2,3].map(e=>(0,a.jsxs)(l.Zp,{className:"flex flex-col",children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"bg-muted h-4 w-3/4 rounded animate-pulse"}),(0,a.jsx)("div",{className:"bg-muted h-3 w-1/2 rounded animate-pulse"})]})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"bg-muted h-3 w-full rounded animate-pulse"}),(0,a.jsx)("div",{className:"bg-muted h-3 w-2/3 rounded animate-pulse"})]})})]},e))}):(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:_.map(e=>(0,a.jsxs)(l.Zp,{className:"flex flex-col",children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(l.ZB,{className:"text-lg",children:e.name}),(0,a.jsx)("code",{className:"bg-muted rounded px-2 py-1 text-sm",children:e.courseCode})]}),(0,a.jsx)(c.E,{variant:"verified"===e.type?"default":"secondary",children:e.type})]}),(0,a.jsx)(l.BT,{children:e.description})]}),(0,a.jsxs)(l.Wu,{className:"flex-1 space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{children:"Progress"}),(0,a.jsxs)("span",{children:[e.progress,"%"]})]}),(0,a.jsx)(u.k,{value:e.progress,className:"h-2"}),(0,a.jsxs)("p",{className:"text-muted-foreground text-xs",children:[e.completedModules," of ",e.totalModules," modules completed"]})]}),"in_progress"===e.status&&e.nextChapter&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Next:"}),(0,a.jsx)("p",{className:"text-muted-foreground text-sm",children:e.nextChapter})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"text-muted-foreground flex items-center space-x-2 text-sm",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:["Due: ",e.dueDate?new Date(e.dueDate).toLocaleDateString():"No due date"]})]}),(0,a.jsx)(c.E,{variant:"completed"===e.status?"default":"outline",children:"completed"===e.status?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-1 h-3 w-3"}),"Completed"]}):"In Progress"})]})]}),(0,a.jsx)("div",{className:"p-6 pt-0",children:(0,a.jsx)(b(),{href:"/dashboard/student/courses/".concat(e.id),children:(0,a.jsx)(d.$,{className:"w-full",children:"completed"===e.status?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"View Certificate"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Continue Learning"]})})})})]},e.id))}),!C&&0===_.length&&(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"py-8 text-center",children:[(0,a.jsx)(v.A,{className:"text-muted-foreground mx-auto h-12 w-12"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-semibold",children:"No enrolled courses"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-1 text-sm",children:e?"No courses match your search.":"Get started by enrolling in a course."})]})})})]}),(0,a.jsxs)(m.av,{value:"available","data-sentry-element":"TabsContent","data-sentry-source-file":"page.tsx",children:[C?(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[1,2,3].map(e=>(0,a.jsxs)(l.Zp,{className:"flex flex-col",children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"bg-muted h-4 w-3/4 rounded animate-pulse"}),(0,a.jsx)("div",{className:"bg-muted h-3 w-1/2 rounded animate-pulse"})]})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"bg-muted h-3 w-full rounded animate-pulse"}),(0,a.jsx)("div",{className:"bg-muted h-3 w-2/3 rounded animate-pulse"})]})})]},e))}):(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:E.map(e=>(0,a.jsxs)(l.Zp,{className:"flex flex-col",children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(l.ZB,{className:"text-lg",children:e.name}),(0,a.jsx)("code",{className:"bg-muted rounded px-2 py-1 text-sm",children:e.courseCode})]}),(0,a.jsx)(c.E,{variant:"verified"===e.type?"default":"secondary",children:e.type})]}),(0,a.jsx)(l.BT,{children:e.description})]}),(0,a.jsx)(l.Wu,{className:"flex-1 space-y-4",children:(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Instructor:"}),(0,a.jsx)("span",{children:e.instructor})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Duration:"}),(0,a.jsx)("span",{children:e.duration})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Difficulty:"}),(0,a.jsx)(c.E,{variant:"outline",className:"text-xs",children:e.difficulty})]})]})}),(0,a.jsx)("div",{className:"p-6 pt-0",children:(0,a.jsxs)(d.$,{className:"w-full",variant:"outline",children:[(0,a.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Request Enrollment"]})})]},e.id))}),!C&&0===E.length&&(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"py-8 text-center",children:[(0,a.jsx)(v.A,{className:"text-muted-foreground mx-auto h-12 w-12"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-semibold",children:"No available courses"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-1 text-sm",children:e?"No courses match your search.":"Check back later for new courses."})]})})})]})]})]})}},85921:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(71847).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},86651:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(71847).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},88021:(e,s,t)=>{"use strict";t.d(s,{E:()=>d});var a=t(95155);t(12115);var r=t(32467),n=t(83101),i=t(64269);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:s,variant:t,asChild:n=!1,...d}=e,o=n?r.DX:"span";return(0,a.jsx)(o,{"data-slot":"badge",className:(0,i.cn)(l({variant:t}),s),...d,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}}},e=>{var s=s=>e(e.s=s);e.O(0,[4909,7055,4736,8720,5667,4850,8441,3840,7358],()=>s(65163)),_N_E=e.O()}]);