try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="471e95c6-e918-40b8-b167-4469688a10f4",e._sentryDebugIdIdentifier="sentry-dbid-471e95c6-e918-40b8-b167-4469688a10f4")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{1337:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var n=r(95155),o=r(5379);function a(e){let{children:t,...r}=e;return(0,n.jsx)(o.N,{...r,"data-sentry-element":"NextThemesProvider","data-sentry-component":"ThemeProvider","data-sentry-source-file":"theme-provider.tsx",children:t})}},5379:(e,t,r)=>{"use strict";r.d(t,{D:()=>c,N:()=>u});var n=r(12115),o=(e,t,r,n,o,a,s,i)=>{let l=document.documentElement,c=["light","dark"];function u(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&a?o.map(e=>a[e]||e):o;r?(l.classList.remove(...n),l.classList.add(a&&a[t]?a[t]:t)):l.setAttribute(e,t)}),r=t,i&&c.includes(r)&&(l.style.colorScheme=r)}if(n)u(n);else try{let e=localStorage.getItem(t)||r,n=s&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;u(n)}catch(e){}},a=["light","dark"],s="(prefers-color-scheme: dark)",i=n.createContext(void 0),l={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=n.useContext(i))?e:l},u=e=>n.useContext(i)?n.createElement(n.Fragment,null,e.children):n.createElement(m,{...e}),d=["light","dark"],m=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:o=!0,enableColorScheme:l=!0,storageKey:c="theme",themes:u=d,defaultTheme:m=o?"system":"light",attribute:y="data-theme",value:b,children:v,nonce:w,scriptProps:S}=e,[x,_]=n.useState(()=>h(c,m)),[k,P]=n.useState(()=>"system"===x?g():x),T=b?Object.values(b):u,C=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&o&&(t=g());let n=b?b[t]:t,s=r?f(w):null,i=document.documentElement,c=e=>{"class"===e?(i.classList.remove(...T),n&&i.classList.add(n)):e.startsWith("data-")&&(n?i.setAttribute(e,n):i.removeAttribute(e))};if(Array.isArray(y)?y.forEach(c):c(y),l){let e=a.includes(m)?m:null,r=a.includes(t)?t:e;i.style.colorScheme=r}null==s||s()},[w]),E=n.useCallback(e=>{let t="function"==typeof e?e(x):e;_(t);try{localStorage.setItem(c,t)}catch(e){}},[x]),L=n.useCallback(e=>{P(g(e)),"system"===x&&o&&!t&&C("system")},[x,t]);n.useEffect(()=>{let e=window.matchMedia(s);return e.addListener(L),L(e),()=>e.removeListener(L)},[L]),n.useEffect(()=>{let e=e=>{e.key===c&&(e.newValue?_(e.newValue):E(m))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[E]),n.useEffect(()=>{C(null!=t?t:x)},[t,x]);let N=n.useMemo(()=>({theme:x,setTheme:E,forcedTheme:t,resolvedTheme:"system"===x?k:x,themes:o?[...u,"system"]:u,systemTheme:o?k:void 0}),[x,E,t,k,o,u]);return n.createElement(i.Provider,{value:N},n.createElement(p,{forcedTheme:t,storageKey:c,attribute:y,enableSystem:o,enableColorScheme:l,defaultTheme:m,value:b,themes:u,nonce:w,scriptProps:S}),v)},p=n.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:a,enableSystem:s,enableColorScheme:i,defaultTheme:l,value:c,themes:u,nonce:d,scriptProps:m}=e,p=JSON.stringify([a,r,l,t,u,c,s,i]).slice(1,-1);return n.createElement("script",{...m,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(o.toString(),")(").concat(p,")")}})}),h=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},f=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},g=e=>(e||(e=window.matchMedia(s)),e.matches?"dark":"light")},5721:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var n=r(95155),o=r(12115);let a=(0,o.createContext)(void 0);function s(e){let{children:t,initialTheme:r}=e,[s,i]=(0,o.useState)(()=>r||"default");return(0,o.useEffect)(()=>{document.cookie="".concat("active_theme","=").concat(s,"; path=/; max-age=31536000; SameSite=Lax; ").concat("https:"===window.location.protocol?"Secure;":""),Array.from(document.body.classList).filter(e=>e.startsWith("theme-")).forEach(e=>{document.body.classList.remove(e)}),document.body.classList.add("theme-".concat(s)),s.endsWith("-scaled")&&document.body.classList.add("theme-scaled")},[s]),(0,n.jsx)(a.Provider,{value:{activeTheme:s,setActiveTheme:i},"data-sentry-element":"ThemeContext.Provider","data-sentry-component":"ActiveThemeProvider","data-sentry-source-file":"active-theme.tsx",children:t})}function i(e){let{activeThemeValue:t,children:r}=e;return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(s,{initialTheme:t,"data-sentry-element":"ActiveThemeProvider","data-sentry-source-file":"providers.tsx",children:r})})}},12758:(e,t,r)=>{e.exports=r(19298)()},13673:()=>{},18198:e=>{e.exports={style:{fontFamily:"'Instrument Sans', 'Instrument Sans Fallback'",fontStyle:"normal"},className:"__className_3d9088",variable:"__variable_3d9088"}},19298:(e,t,r)=>{"use strict";var n=r(53341);function o(){}function a(){}a.resetWarningCache=o,e.exports=function(){function e(e,t,r,o,a,s){if(s!==n){var i=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:o};return r.PropTypes=r,r}},20063:(e,t,r)=>{"use strict";var n=r(47260);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},21887:(e,t,r)=>{"use strict";var n=Object.create,o=Object.defineProperty,a=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.getPrototypeOf,l=Object.prototype.hasOwnProperty,c=(e,t)=>o(e,"name",{value:t,configurable:!0}),u=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of s(t))l.call(e,i)||i===r||o(e,i,{get:()=>t[i],enumerable:!(n=a(t,i))||n.enumerable});return e},d=(e,t,r)=>(r=null!=e?n(i(e)):{},u(!t&&e&&e.__esModule?r:o(r,"default",{value:e,enumerable:!0}),e)),m={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(m,{default:()=>v,useTopLoader:()=>y}),e.exports=u(o({},"__esModule",{value:!0}),m);var p=d(r(12758)),h=d(r(12115)),f=d(r(56111)),g=d(r(56111)),y=c(()=>({start:()=>g.start(),done:e=>g.done(e),remove:()=>g.remove(),setProgress:e=>g.set(e),inc:e=>g.inc(e),trickle:()=>g.trickle(),isStarted:()=>g.isStarted(),isRendered:()=>g.isRendered(),getPositioningCSS:()=>g.getPositioningCSS()}),"useTopLoader"),b=c(e=>{let{color:t,height:r,showSpinner:n,crawl:o,crawlSpeed:a,initialPosition:s,easing:i,speed:l,shadow:u,template:d,zIndex:m=1600,showAtBottom:p=!1,showForHashAnchor:g=!0}=e,y=null!=t?t:"#29d",b=u||void 0===u?u?"box-shadow:".concat(u):"box-shadow:0 0 10px ".concat(y,",0 0 5px ").concat(y):"",v=h.createElement("style",null,"#nprogress{pointer-events:none}#nprogress .bar{background:".concat(y,";position:fixed;z-index:").concat(m,";").concat(p?"bottom: 0;":"top: 0;","left:0;width:100%;height:").concat(null!=r?r:3,"px}#nprogress .peg{display:block;position:absolute;right:0;width:100px;height:100%;").concat(b,";opacity:1;-webkit-transform:rotate(3deg) translate(0px,-4px);-ms-transform:rotate(3deg) translate(0px,-4px);transform:rotate(3deg) translate(0px,-4px)}#nprogress .spinner{display:block;position:fixed;z-index:").concat(m,";").concat(p?"bottom: 15px;":"top: 15px;","right:15px}#nprogress .spinner-icon{width:18px;height:18px;box-sizing:border-box;border:2px solid transparent;border-top-color:").concat(y,";border-left-color:").concat(y,";border-radius:50%;-webkit-animation:nprogress-spinner 400ms linear infinite;animation:nprogress-spinner 400ms linear infinite}.nprogress-custom-parent{overflow:hidden;position:relative}.nprogress-custom-parent #nprogress .bar,.nprogress-custom-parent #nprogress .spinner{position:absolute}@-webkit-keyframes nprogress-spinner{0%{-webkit-transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg)}}@keyframes nprogress-spinner{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}")),w=c(e=>new URL(e,window.location.href).href,"toAbsoluteURL"),S=c((e,t)=>{let r=new URL(w(e)),n=new URL(w(t));return r.href.split("#")[0]===n.href.split("#")[0]},"isHashAnchor"),x=c((e,t)=>{let r=new URL(w(e)),n=new URL(w(t));return r.hostname.replace(/^www\./,"")===n.hostname.replace(/^www\./,"")},"isSameHostName");return h.useEffect(()=>{function e(e,t){let r=new URL(e),n=new URL(t);if(r.hostname===n.hostname&&r.pathname===n.pathname&&r.search===n.search){let e=r.hash,t=n.hash;return e!==t&&r.href.replace(e,"")===n.href.replace(t,"")}return!1}f.configure({showSpinner:null==n||n,trickle:null==o||o,trickleSpeed:null!=a?a:200,minimum:null!=s?s:.08,easing:null!=i?i:"ease",speed:null!=l?l:200,template:null!=d?d:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'}),c(e,"isAnchorOfCurrentUrl");var t=document.querySelectorAll("html");let r=c(()=>t.forEach(e=>e.classList.remove("nprogress-busy")),"removeNProgressClass");function u(e){for(;e&&"a"!==e.tagName.toLowerCase();)e=e.parentElement;return e}function m(t){try{let n=t.target,o=u(n),a=null==o?void 0:o.href;if(a){let n=window.location.href,s=""!==o.target,i=["tel:","mailto:","sms:","blob:","download:"].some(e=>a.startsWith(e));if(!x(window.location.href,o.href))return;let l=e(n,a)||S(window.location.href,o.href);if(!g&&l)return;a===n||s||i||l||t.ctrlKey||t.metaKey||t.shiftKey||t.altKey||!w(o.href).startsWith("http")?(f.start(),f.done(),r()):f.start()}}catch(e){f.start(),f.done()}}function p(){f.done(),r()}function h(){f.done()}return c(u,"findClosestAnchor"),c(m,"handleClick"),(e=>{let t=e.pushState;e.pushState=function(){for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return f.done(),r(),t.apply(e,o)}})(window.history),(e=>{let t=e.replaceState;e.replaceState=function(){for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return f.done(),r(),t.apply(e,o)}})(window.history),c(p,"handlePageHide"),c(h,"handleBackAndForth"),window.addEventListener("popstate",h),document.addEventListener("click",m),window.addEventListener("pagehide",p),()=>{document.removeEventListener("click",m),window.removeEventListener("pagehide",p),window.removeEventListener("popstate",h)}},[]),v},"NextTopLoader"),v=b;b.propTypes={color:p.string,height:p.number,showSpinner:p.bool,crawl:p.bool,crawlSpeed:p.number,initialPosition:p.number,easing:p.string,speed:p.number,template:p.string,shadow:p.oneOfType([p.string,p.bool]),zIndex:p.number,showAtBottom:p.bool}},23997:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_f367f3",variable:"__variable_f367f3"}},44702:()=>{},49345:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},53341:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},56111:function(e,t,r){var n,o;void 0===(o="function"==typeof(n=function(){var e,t,r,n={};n.version="0.2.0";var o=n.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};function a(e,t,r){return e<t?t:e>r?r:e}n.configure=function(e){var t,r;for(t in e)void 0!==(r=e[t])&&e.hasOwnProperty(t)&&(o[t]=r);return this},n.status=null,n.set=function(e){var t=n.isStarted();n.status=1===(e=a(e,o.minimum,1))?null:e;var r=n.render(!t),l=r.querySelector(o.barSelector),c=o.speed,u=o.easing;return r.offsetWidth,s(function(t){var a,s,d,m;""===o.positionUsing&&(o.positionUsing=n.getPositioningCSS()),i(l,(a=e,s=c,d=u,(m="translate3d"===o.positionUsing?{transform:"translate3d("+(-1+a)*100+"%,0,0)"}:"translate"===o.positionUsing?{transform:"translate("+(-1+a)*100+"%,0)"}:{"margin-left":(-1+a)*100+"%"}).transition="all "+s+"ms "+d,m)),1===e?(i(r,{transition:"none",opacity:1}),r.offsetWidth,setTimeout(function(){i(r,{transition:"all "+c+"ms linear",opacity:0}),setTimeout(function(){n.remove(),t()},c)},c)):setTimeout(t,c)}),this},n.isStarted=function(){return"number"==typeof n.status},n.start=function(){n.status||n.set(0);var e=function(){setTimeout(function(){n.status&&(n.trickle(),e())},o.trickleSpeed)};return o.trickle&&e(),this},n.done=function(e){return e||n.status?n.inc(.3+.5*Math.random()).set(1):this},n.inc=function(e){var t=n.status;return t?("number"!=typeof e&&(e=(1-t)*a(Math.random()*t,.1,.95)),t=a(t+e,0,.994),n.set(t)):n.start()},n.trickle=function(){return n.inc(Math.random()*o.trickleRate)},e=0,t=0,n.promise=function(r){return r&&"resolved"!==r.state()&&(0===t&&n.start(),e++,t++,r.always(function(){0==--t?(e=0,n.done()):n.set((e-t)/e)})),this},n.render=function(e){if(n.isRendered())return document.getElementById("nprogress");c(document.documentElement,"nprogress-busy");var t=document.createElement("div");t.id="nprogress",t.innerHTML=o.template;var r,a=t.querySelector(o.barSelector),s=e?"-100":(-1+(n.status||0))*100,l=document.querySelector(o.parent);return i(a,{transition:"all 0 linear",transform:"translate3d("+s+"%,0,0)"}),!o.showSpinner&&(r=t.querySelector(o.spinnerSelector))&&m(r),l!=document.body&&c(l,"nprogress-custom-parent"),l.appendChild(t),t},n.remove=function(){u(document.documentElement,"nprogress-busy"),u(document.querySelector(o.parent),"nprogress-custom-parent");var e=document.getElementById("nprogress");e&&m(e)},n.isRendered=function(){return!!document.getElementById("nprogress")},n.getPositioningCSS=function(){var e=document.body.style,t="WebkitTransform"in e?"Webkit":"MozTransform"in e?"Moz":"msTransform"in e?"ms":"OTransform"in e?"O":"";return t+"Perspective"in e?"translate3d":t+"Transform"in e?"translate":"margin"};var s=(r=[],function(e){r.push(e),1==r.length&&function e(){var t=r.shift();t&&t(e)}()}),i=function(){var e=["Webkit","O","Moz","ms"],t={};function r(r,n,o){var a;n=t[a=(a=n).replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,function(e,t){return t.toUpperCase()})]||(t[a]=function(t){var r=document.body.style;if(t in r)return t;for(var n,o=e.length,a=t.charAt(0).toUpperCase()+t.slice(1);o--;)if((n=e[o]+a)in r)return n;return t}(a)),r.style[n]=o}return function(e,t){var n,o,a=arguments;if(2==a.length)for(n in t)void 0!==(o=t[n])&&t.hasOwnProperty(n)&&r(e,n,o);else r(e,a[1],a[2])}}();function l(e,t){return("string"==typeof e?e:d(e)).indexOf(" "+t+" ")>=0}function c(e,t){var r=d(e),n=r+t;l(r,t)||(e.className=n.substring(1))}function u(e,t){var r,n=d(e);l(e,t)&&(e.className=(r=n.replace(" "+t+" "," ")).substring(1,r.length-1))}function d(e){return(" "+(e.className||"")+" ").replace(/\s+/gi," ")}function m(e){e&&e.parentNode&&e.parentNode.removeChild(e)}return n})?n.call(t,r,t,e):n)||(e.exports=o)},57410:e=>{e.exports={style:{fontFamily:"'Quicksand', 'Quicksand Fallback'",fontStyle:"normal"},className:"__className_572227",variable:"__variable_572227"}},64510:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,57410,23)),Promise.resolve().then(r.t.bind(r,49345,23)),Promise.resolve().then(r.t.bind(r,18198,23)),Promise.resolve().then(r.t.bind(r,64986,23)),Promise.resolve().then(r.t.bind(r,71337,23)),Promise.resolve().then(r.t.bind(r,23997,23)),Promise.resolve().then(r.t.bind(r,21887,23)),Promise.resolve().then(r.bind(r,76454)),Promise.resolve().then(r.t.bind(r,13673,23)),Promise.resolve().then(r.t.bind(r,44702,23)),Promise.resolve().then(r.bind(r,5721)),Promise.resolve().then(r.bind(r,1337)),Promise.resolve().then(r.bind(r,79679))},64986:e=>{e.exports={style:{fontFamily:"'Noto Sans Mono', 'Noto Sans Mono Fallback'",fontStyle:"normal"},className:"__className_89e83c",variable:"__variable_89e83c"}},71337:e=>{e.exports={style:{fontFamily:"'Mulish', 'Mulish Fallback'",fontStyle:"normal"},className:"__className_9738f2",variable:"__variable_9738f2"}},76454:(e,t,r)=>{"use strict";r.d(t,{NuqsAdapter:()=>s});var n=r(97502),o=r(20063),a=r(12115),s=(0,n.Hx)(function(){let e=(0,o.useRouter)(),t=(0,o.useSearchParams)(),[r,s]=(0,a.useOptimistic)(t);return{searchParams:r,updateUrl:(0,a.useCallback)((t,r)=>{(0,a.startTransition)(()=>{r.shallow||s(t);let o=function(e,t){let r=e.split("#")[0]??"";return r+(0,n.OB)(t)+location.hash}(location.origin+location.pathname,t);(0,n.Yz)("[nuqs queue (app)] Updating url: %s",o),("push"===r.history?history.pushState:history.replaceState).call(history,null,"",o),r.scroll&&window.scrollTo(0,0),r.shallow||e.replace(o,{scroll:!1})})},[]),rateLimitFactor:3}})},79679:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>s});var n=r(95155),o=r(5379),a=r(18720);let s=e=>{let{...t}=e,{theme:r="system"}=(0,o.D)();return(0,n.jsx)(a.l$,{theme:r,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...t,"data-sentry-element":"Sonner","data-sentry-component":"Toaster","data-sentry-source-file":"sonner.tsx"})}},97502:(e,t,r)=>{"use strict";r.d(t,{Hx:()=>d,OB:()=>s,R8:()=>c,V7:()=>m,Yz:()=>l,z3:()=>a});var n=r(12115),o={303:"Multiple adapter contexts detected. This might happen in monorepos.",404:"nuqs requires an adapter to work with your framework.",409:"Multiple versions of the library are loaded. This may lead to unexpected behavior. Currently using `%s`, but `%s` (via the %s adapter) was about to load on top.",414:"Max safe URL length exceeded. Some browsers may not be able to accept this URL. Consider limiting the amount of state stored in the URL.",429:"URL update rate-limited by the browser. Consider increasing `throttleMs` for key(s) `%s`. %O",500:"Empty search params cache. Search params can't be accessed in Layouts.",501:"Search params cache already populated. Have you called `parse` twice?"};function a(e){return`[nuqs] ${o[e]}
  See https://err.47ng.com/NUQS-${e}`}function s(e){if(0===e.size)return"";let t=[];for(let[r,n]of e.entries()){let e=r.replace(/#/g,"%23").replace(/&/g,"%26").replace(/\+/g,"%2B").replace(/=/g,"%3D").replace(/\?/g,"%3F");t.push(`${e}=${n.replace(/%/g,"%25").replace(/\+/g,"%2B").replace(/ /g,"+").replace(/#/g,"%23").replace(/&/g,"%26").replace(/"/g,"%22").replace(/'/g,"%27").replace(/`/g,"%60").replace(/</g,"%3C").replace(/>/g,"%3E").replace(/[\x00-\x1F]/g,e=>encodeURIComponent(e))}`)}return"?"+t.join("&")}var i=function(){try{if("undefined"==typeof localStorage)return!1;let e="nuqs-localStorage-test";localStorage.setItem(e,e);let t=localStorage.getItem(e)===e;if(localStorage.removeItem(e),!t)return!1}catch(e){return console.error("[nuqs]: debug mode is disabled (localStorage unavailable).",e),!1}return(localStorage.getItem("debug")??"").includes("nuqs")}();function l(e,...t){if(!i)return;let r=function(e,...t){return e.replace(/%[sfdO]/g,e=>{let r=t.shift();return"%O"===e&&r?JSON.stringify(r).replace(/"([^"]+)":/g,"$1:"):String(r)})}(e,...t);performance.mark(r);try{console.log(e,...t)}catch(e){console.log(r)}}function c(e,...t){i&&console.warn(e,...t)}var u=(0,n.createContext)({useAdapter(){throw Error(a(404))}});function d(e){return({children:t,...r})=>(0,n.createElement)(u.Provider,{...r,value:{useAdapter:e}},t)}function m(){let e=(0,n.useContext)(u);if(!("useAdapter"in e))throw Error(a(404));return e.useAdapter()}u.displayName="NuqsAdapterContext",i&&"undefined"!=typeof window&&(window.__NuqsAdapterContext&&window.__NuqsAdapterContext!==u&&console.error(a(303)),window.__NuqsAdapterContext=u)}},e=>{var t=t=>e(e.s=t);e.O(0,[3768,2978,441,8720,4850,8441,3840,7358],()=>t(64510)),_N_E=e.O()}]);