try{let t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},e=(new t.Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="25ab5861-aa00-4a14-9c6b-23128db2b14f",t._sentryDebugIdIdentifier="sentry-dbid-25ab5861-aa00-4a14-9c6b-23128db2b14f")}catch(t){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6093],{66093:(t,e,n)=>{n.d(e,{Mz:()=>et,i3:()=>en,UC:()=>ee,bL:()=>t7,Bk:()=>t$});var r=n(12115);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,f=Math.floor,s=t=>({x:t,y:t}),u={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function d(t,e){return"function"==typeof t?t(e):t}function p(t){return t.split("-")[0]}function h(t){return t.split("-")[1]}function g(t){return"x"===t?"y":"x"}function m(t){return"y"===t?"height":"width"}let y=new Set(["top","bottom"]);function w(t){return y.has(p(t))?"y":"x"}function x(t){return t.replace(/start|end/g,t=>c[t])}let v=["left","right"],b=["right","left"],A=["top","bottom"],R=["bottom","top"];function S(t){return t.replace(/left|right|bottom|top/g,t=>u[t])}function T(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function L(t){let{x:e,y:n,width:r,height:i}=t;return{width:r,height:i,top:n,left:e,right:e+r,bottom:n+i,x:e,y:n}}function E(t,e,n){let r,{reference:i,floating:o}=t,l=w(e),a=g(w(e)),f=m(a),s=p(e),u="y"===l,c=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,y=i[f]/2-o[f]/2;switch(s){case"top":r={x:c,y:i.y-o.height};break;case"bottom":r={x:c,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(e)){case"start":r[a]-=y*(n&&u?-1:1);break;case"end":r[a]+=y*(n&&u?-1:1)}return r}let C=async(t,e,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),f=await (null==l.isRTL?void 0:l.isRTL(e)),s=await l.getElementRects({reference:t,floating:e,strategy:i}),{x:u,y:c}=E(s,r,f),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:g}=a[n],{x:m,y:y,data:w,reset:x}=await g({x:u,y:c,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:s,platform:l,elements:{reference:t,floating:e}});u=null!=m?m:u,c=null!=y?y:c,p={...p,[o]:{...p[o],...w}},x&&h<=50&&(h++,"object"==typeof x&&(x.placement&&(d=x.placement),x.rects&&(s=!0===x.rects?await l.getElementRects({reference:t,floating:e,strategy:i}):x.rects),{x:u,y:c}=E(s,d,f)),n=-1)}return{x:u,y:c,placement:d,strategy:i,middlewareData:p}};async function O(t,e){var n;void 0===e&&(e={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:f}=t,{boundary:s="clippingAncestors",rootBoundary:u="viewport",elementContext:c="floating",altBoundary:p=!1,padding:h=0}=d(e,t),g=T(h),m=a[p?"floating"===c?"reference":"floating":c],y=L(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(m)))||n?m:m.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:s,rootBoundary:u,strategy:f})),w="floating"===c?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,x=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),v=await (null==o.isElement?void 0:o.isElement(x))&&await (null==o.getScale?void 0:o.getScale(x))||{x:1,y:1},b=L(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:x,strategy:f}):w);return{top:(y.top-b.top+g.top)/v.y,bottom:(b.bottom-y.bottom+g.bottom)/v.y,left:(y.left-b.left+g.left)/v.x,right:(b.right-y.right+g.right)/v.x}}function P(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function k(t){return i.some(e=>t[e]>=0)}let D=new Set(["left","top"]);async function H(t,e){let{placement:n,platform:r,elements:i}=t,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=h(n),f="y"===w(n),s=D.has(l)?-1:1,u=o&&f?-1:1,c=d(e,t),{mainAxis:g,crossAxis:m,alignmentAxis:y}="number"==typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return a&&"number"==typeof y&&(m="end"===a?-1*y:y),f?{x:m*u,y:g*s}:{x:g*s,y:m*u}}function F(){return"undefined"!=typeof window}function N(t){return W(t)?(t.nodeName||"").toLowerCase():"#document"}function j(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function M(t){var e;return null==(e=(W(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function W(t){return!!F()&&(t instanceof Node||t instanceof j(t).Node)}function z(t){return!!F()&&(t instanceof Element||t instanceof j(t).Element)}function B(t){return!!F()&&(t instanceof HTMLElement||t instanceof j(t).HTMLElement)}function _(t){return!!F()&&"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof j(t).ShadowRoot)}let I=new Set(["inline","contents"]);function V(t){let{overflow:e,overflowX:n,overflowY:r,display:i}=tt(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!I.has(i)}let X=new Set(["table","td","th"]),Y=[":popover-open",":modal"];function G(t){return Y.some(e=>{try{return t.matches(e)}catch(t){return!1}})}let $=["transform","translate","scale","rotate","perspective"],q=["transform","translate","scale","rotate","perspective","filter"],U=["paint","layout","strict","content"];function J(t){let e=K(),n=z(t)?tt(t):t;return $.some(t=>!!n[t]&&"none"!==n[t])||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||q.some(t=>(n.willChange||"").includes(t))||U.some(t=>(n.contain||"").includes(t))}function K(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Q=new Set(["html","body","#document"]);function Z(t){return Q.has(N(t))}function tt(t){return j(t).getComputedStyle(t)}function te(t){return z(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function tn(t){if("html"===N(t))return t;let e=t.assignedSlot||t.parentNode||_(t)&&t.host||M(t);return _(e)?e.host:e}function tr(t,e,n){var r;void 0===e&&(e=[]),void 0===n&&(n=!0);let i=function t(e){let n=tn(e);return Z(n)?e.ownerDocument?e.ownerDocument.body:e.body:B(n)&&V(n)?n:t(n)}(t),o=i===(null==(r=t.ownerDocument)?void 0:r.body),l=j(i);if(o){let t=ti(l);return e.concat(l,l.visualViewport||[],V(i)?i:[],t&&n?tr(t):[])}return e.concat(i,tr(i,[],n))}function ti(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function to(t){let e=tt(t),n=parseFloat(e.width)||0,r=parseFloat(e.height)||0,i=B(t),o=i?t.offsetWidth:n,l=i?t.offsetHeight:r,f=a(n)!==o||a(r)!==l;return f&&(n=o,r=l),{width:n,height:r,$:f}}function tl(t){return z(t)?t:t.contextElement}function ta(t){let e=tl(t);if(!B(e))return s(1);let n=e.getBoundingClientRect(),{width:r,height:i,$:o}=to(e),l=(o?a(n.width):n.width)/r,f=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),f&&Number.isFinite(f)||(f=1),{x:l,y:f}}let tf=s(0);function ts(t){let e=j(t);return K()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:tf}function tu(t,e,n,r){var i;void 0===e&&(e=!1),void 0===n&&(n=!1);let o=t.getBoundingClientRect(),l=tl(t),a=s(1);e&&(r?z(r)&&(a=ta(r)):a=ta(t));let f=(void 0===(i=n)&&(i=!1),r&&(!i||r===j(l))&&i)?ts(l):s(0),u=(o.left+f.x)/a.x,c=(o.top+f.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let t=j(l),e=r&&z(r)?j(r):r,n=t,i=ti(n);for(;i&&r&&e!==n;){let t=ta(i),e=i.getBoundingClientRect(),r=tt(i),o=e.left+(i.clientLeft+parseFloat(r.paddingLeft))*t.x,l=e.top+(i.clientTop+parseFloat(r.paddingTop))*t.y;u*=t.x,c*=t.y,d*=t.x,p*=t.y,u+=o,c+=l,i=ti(n=j(i))}}return L({width:d,height:p,x:u,y:c})}function tc(t,e){let n=te(t).scrollLeft;return e?e.left+n:tu(M(t)).left+n}function td(t,e,n){void 0===n&&(n=!1);let r=t.getBoundingClientRect();return{x:r.left+e.scrollLeft-(n?0:tc(t,r)),y:r.top+e.scrollTop}}let tp=new Set(["absolute","fixed"]);function th(t,e,n){let r;if("viewport"===e)r=function(t,e){let n=j(t),r=M(t),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,f=0;if(i){o=i.width,l=i.height;let t=K();(!t||t&&"fixed"===e)&&(a=i.offsetLeft,f=i.offsetTop)}return{width:o,height:l,x:a,y:f}}(t,n);else if("document"===e)r=function(t){let e=M(t),n=te(t),r=t.ownerDocument.body,i=l(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),o=l(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+tc(t),f=-n.scrollTop;return"rtl"===tt(r).direction&&(a+=l(e.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:f}}(M(t));else if(z(e))r=function(t,e){let n=tu(t,!0,"fixed"===e),r=n.top+t.clientTop,i=n.left+t.clientLeft,o=B(t)?ta(t):s(1),l=t.clientWidth*o.x,a=t.clientHeight*o.y;return{width:l,height:a,x:i*o.x,y:r*o.y}}(e,n);else{let n=ts(t);r={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return L(r)}function tg(t){return"static"===tt(t).position}function tm(t,e){if(!B(t)||"fixed"===tt(t).position)return null;if(e)return e(t);let n=t.offsetParent;return M(t)===n&&(n=n.ownerDocument.body),n}function ty(t,e){var n;let r=j(t);if(G(t))return r;if(!B(t)){let e=tn(t);for(;e&&!Z(e);){if(z(e)&&!tg(e))return e;e=tn(e)}return r}let i=tm(t,e);for(;i&&(n=i,X.has(N(n)))&&tg(i);)i=tm(i,e);return i&&Z(i)&&tg(i)&&!J(i)?r:i||function(t){let e=tn(t);for(;B(e)&&!Z(e);){if(J(e))return e;if(G(e))break;e=tn(e)}return null}(t)||r}let tw=async function(t){let e=this.getOffsetParent||ty,n=this.getDimensions,r=await n(t.floating);return{reference:function(t,e,n){let r=B(e),i=M(e),o="fixed"===n,l=tu(t,!0,o,e),a={scrollLeft:0,scrollTop:0},f=s(0);if(r||!r&&!o)if(("body"!==N(e)||V(i))&&(a=te(e)),r){let t=tu(e,!0,o,e);f.x=t.x+e.clientLeft,f.y=t.y+e.clientTop}else i&&(f.x=tc(i));o&&!r&&i&&(f.x=tc(i));let u=!i||r||o?s(0):td(i,a);return{x:l.left+a.scrollLeft-f.x-u.x,y:l.top+a.scrollTop-f.y-u.y,width:l.width,height:l.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},tx={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:r,strategy:i}=t,o="fixed"===i,l=M(r),a=!!e&&G(e.floating);if(r===l||a&&o)return n;let f={scrollLeft:0,scrollTop:0},u=s(1),c=s(0),d=B(r);if((d||!d&&!o)&&(("body"!==N(r)||V(l))&&(f=te(r)),B(r))){let t=tu(r);u=ta(r),c.x=t.x+r.clientLeft,c.y=t.y+r.clientTop}let p=!l||d||o?s(0):td(l,f,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-f.scrollLeft*u.x+c.x+p.x,y:n.y*u.y-f.scrollTop*u.y+c.y+p.y}},getDocumentElement:M,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:r,strategy:i}=t,a=[..."clippingAncestors"===n?G(e)?[]:function(t,e){let n=e.get(t);if(n)return n;let r=tr(t,[],!1).filter(t=>z(t)&&"body"!==N(t)),i=null,o="fixed"===tt(t).position,l=o?tn(t):t;for(;z(l)&&!Z(l);){let e=tt(l),n=J(l);n||"fixed"!==e.position||(i=null),(o?!n&&!i:!n&&"static"===e.position&&!!i&&tp.has(i.position)||V(l)&&!n&&function t(e,n){let r=tn(e);return!(r===n||!z(r)||Z(r))&&("fixed"===tt(r).position||t(r,n))}(t,l))?r=r.filter(t=>t!==l):i=e,l=tn(l)}return e.set(t,r),r}(e,this._c):[].concat(n),r],f=a[0],s=a.reduce((t,n)=>{let r=th(e,n,i);return t.top=l(r.top,t.top),t.right=o(r.right,t.right),t.bottom=o(r.bottom,t.bottom),t.left=l(r.left,t.left),t},th(e,f,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:ty,getElementRects:tw,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:n}=to(t);return{width:e,height:n}},getScale:ta,isElement:z,isRTL:function(t){return"rtl"===tt(t).direction}};function tv(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}let tb=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:r,placement:i,rects:a,platform:f,elements:s,middlewareData:u}=e,{element:c,padding:p=0}=d(t,e)||{};if(null==c)return{};let y=T(p),x={x:n,y:r},v=g(w(i)),b=m(v),A=await f.getDimensions(c),R="y"===v,S=R?"clientHeight":"clientWidth",L=a.reference[b]+a.reference[v]-x[v]-a.floating[b],E=x[v]-a.reference[v],C=await (null==f.getOffsetParent?void 0:f.getOffsetParent(c)),O=C?C[S]:0;O&&await (null==f.isElement?void 0:f.isElement(C))||(O=s.floating[S]||a.floating[b]);let P=O/2-A[b]/2-1,k=o(y[R?"top":"left"],P),D=o(y[R?"bottom":"right"],P),H=O-A[b]-D,F=O/2-A[b]/2+(L/2-E/2),N=l(k,o(F,H)),j=!u.arrow&&null!=h(i)&&F!==N&&a.reference[b]/2-(F<k?k:D)-A[b]/2<0,M=j?F<k?F-k:F-H:0;return{[v]:x[v]+M,data:{[v]:N,centerOffset:F-N-M,...j&&{alignmentOffset:M}},reset:j}}}),tA=(t,e,n)=>{let r=new Map,i={platform:tx,...n},o={...i.platform,_c:r};return C(t,e,{...i,platform:o})};var tR=n(47650),tS="undefined"!=typeof document?r.useLayoutEffect:function(){};function tT(t,e){let n,r,i;if(t===e)return!0;if(typeof t!=typeof e)return!1;if("function"==typeof t&&t.toString()===e.toString())return!0;if(t&&e&&"object"==typeof t){if(Array.isArray(t)){if((n=t.length)!==e.length)return!1;for(r=n;0!=r--;)if(!tT(t[r],e[r]))return!1;return!0}if((n=(i=Object.keys(t)).length)!==Object.keys(e).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(e,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!t.$$typeof)&&!tT(t[n],e[n]))return!1}return!0}return t!=t&&e!=e}function tL(t){return"undefined"==typeof window?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function tE(t,e){let n=tL(t);return Math.round(e*n)/n}function tC(t){let e=r.useRef(t);return tS(()=>{e.current=t}),e}let tO=t=>({name:"arrow",options:t,fn(e){let{element:n,padding:r}="function"==typeof t?t(e):t;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?tb({element:n.current,padding:r}).fn(e):{}:n?tb({element:n,padding:r}).fn(e):{}}}),tP=(t,e)=>({...function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,r;let{x:i,y:o,placement:l,middlewareData:a}=e,f=await H(e,t);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+f.x,y:o+f.y,data:{...f,placement:l}}}}}(t),options:[t,e]}),tk=(t,e)=>({...function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){let{x:n,y:r,placement:i}=e,{mainAxis:a=!0,crossAxis:f=!1,limiter:s={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...u}=d(t,e),c={x:n,y:r},h=await O(e,u),m=w(p(i)),y=g(m),x=c[y],v=c[m];if(a){let t="y"===y?"top":"left",e="y"===y?"bottom":"right",n=x+h[t],r=x-h[e];x=l(n,o(x,r))}if(f){let t="y"===m?"top":"left",e="y"===m?"bottom":"right",n=v+h[t],r=v-h[e];v=l(n,o(v,r))}let b=s.fn({...e,[y]:x,[m]:v});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[y]:a,[m]:f}}}}}}(t),options:[t,e]}),tD=(t,e)=>({...function(t){return void 0===t&&(t={}),{options:t,fn(e){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=e,{offset:a=0,mainAxis:f=!0,crossAxis:s=!0}=d(t,e),u={x:n,y:r},c=w(i),h=g(c),m=u[h],y=u[c],x=d(a,e),v="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(f){let t="y"===h?"height":"width",e=o.reference[h]-o.floating[t]+v.mainAxis,n=o.reference[h]+o.reference[t]-v.mainAxis;m<e?m=e:m>n&&(m=n)}if(s){var b,A;let t="y"===h?"width":"height",e=D.has(p(i)),n=o.reference[c]-o.floating[t]+(e&&(null==(b=l.offset)?void 0:b[c])||0)+(e?0:v.crossAxis),r=o.reference[c]+o.reference[t]+(e?0:(null==(A=l.offset)?void 0:A[c])||0)-(e?v.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:m,[c]:y}}}}(t),options:[t,e]}),tH=(t,e)=>({...function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,r,i,o,l;let{placement:a,middlewareData:f,rects:s,initialPlacement:u,platform:c,elements:y}=e,{mainAxis:T=!0,crossAxis:L=!0,fallbackPlacements:E,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:k=!0,...D}=d(t,e);if(null!=(n=f.arrow)&&n.alignmentOffset)return{};let H=p(a),F=w(u),N=p(u)===u,j=await (null==c.isRTL?void 0:c.isRTL(y.floating)),M=E||(N||!k?[S(u)]:function(t){let e=S(t);return[x(t),e,x(e)]}(u)),W="none"!==P;!E&&W&&M.push(...function(t,e,n,r){let i=h(t),o=function(t,e,n){switch(t){case"top":case"bottom":if(n)return e?b:v;return e?v:b;case"left":case"right":return e?A:R;default:return[]}}(p(t),"start"===n,r);return i&&(o=o.map(t=>t+"-"+i),e&&(o=o.concat(o.map(x)))),o}(u,k,P,j));let z=[u,...M],B=await O(e,D),_=[],I=(null==(r=f.flip)?void 0:r.overflows)||[];if(T&&_.push(B[H]),L){let t=function(t,e,n){void 0===n&&(n=!1);let r=h(t),i=g(w(t)),o=m(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return e.reference[o]>e.floating[o]&&(l=S(l)),[l,S(l)]}(a,s,j);_.push(B[t[0]],B[t[1]])}if(I=[...I,{placement:a,overflows:_}],!_.every(t=>t<=0)){let t=((null==(i=f.flip)?void 0:i.index)||0)+1,e=z[t];if(e&&("alignment"!==L||F===w(e)||I.every(t=>w(t.placement)!==F||t.overflows[0]>0)))return{data:{index:t,overflows:I},reset:{placement:e}};let n=null==(o=I.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:o.placement;if(!n)switch(C){case"bestFit":{let t=null==(l=I.filter(t=>{if(W){let e=w(t.placement);return e===F||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:l[0];t&&(n=t);break}case"initialPlacement":n=u}if(a!==n)return{reset:{placement:n}}}return{}}}}(t),options:[t,e]}),tF=(t,e)=>({...function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,r;let i,a,{placement:f,rects:s,platform:u,elements:c}=e,{apply:g=()=>{},...m}=d(t,e),y=await O(e,m),x=p(f),v=h(f),b="y"===w(f),{width:A,height:R}=s.floating;"top"===x||"bottom"===x?(i=x,a=v===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(a=x,i="end"===v?"top":"bottom");let S=R-y.top-y.bottom,T=A-y.left-y.right,L=o(R-y[i],S),E=o(A-y[a],T),C=!e.middlewareData.shift,P=L,k=E;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(k=T),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(P=S),C&&!v){let t=l(y.left,0),e=l(y.right,0),n=l(y.top,0),r=l(y.bottom,0);b?k=A-2*(0!==t||0!==e?t+e:l(y.left,y.right)):P=R-2*(0!==n||0!==r?n+r:l(y.top,y.bottom))}await g({...e,availableWidth:k,availableHeight:P});let D=await u.getDimensions(c.floating);return A!==D.width||R!==D.height?{reset:{rects:!0}}:{}}}}(t),options:[t,e]}),tN=(t,e)=>({...function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){let{rects:n}=e,{strategy:r="referenceHidden",...i}=d(t,e);switch(r){case"referenceHidden":{let t=P(await O(e,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:k(t)}}}case"escaped":{let t=P(await O(e,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:k(t)}}}default:return{}}}}}(t),options:[t,e]}),tj=(t,e)=>({...tO(t),options:[t,e]});var tM=n(97602),tW=n(95155),tz=r.forwardRef((t,e)=>{let{children:n,width:r=10,height:i=5,...o}=t;return(0,tW.jsx)(tM.sG.svg,{...o,ref:e,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:t.asChild?n:(0,tW.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tz.displayName="Arrow";var tB=n(94446),t_=n(3468),tI=n(70222),tV=n(4129),tX=n(84288),tY="Popper",[tG,t$]=(0,t_.A)(tY),[tq,tU]=tG(tY),tJ=t=>{let{__scopePopper:e,children:n}=t,[i,o]=r.useState(null);return(0,tW.jsx)(tq,{scope:e,anchor:i,onAnchorChange:o,children:n})};tJ.displayName=tY;var tK="PopperAnchor",tQ=r.forwardRef((t,e)=>{let{__scopePopper:n,virtualRef:i,...o}=t,l=tU(tK,n),a=r.useRef(null),f=(0,tB.s)(e,a);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,tW.jsx)(tM.sG.div,{...o,ref:f})});tQ.displayName=tK;var tZ="PopperContent",[t0,t1]=tG(tZ),t2=r.forwardRef((t,e)=>{var n,i,a,s,u,c,d,p;let{__scopePopper:h,side:g="bottom",sideOffset:m=0,align:y="center",alignOffset:w=0,arrowPadding:x=0,avoidCollisions:v=!0,collisionBoundary:b=[],collisionPadding:A=0,sticky:R="partial",hideWhenDetached:S=!1,updatePositionStrategy:T="optimized",onPlaced:L,...E}=t,C=tU(tZ,h),[O,P]=r.useState(null),k=(0,tB.s)(e,t=>P(t)),[D,H]=r.useState(null),F=(0,tX.X)(D),N=null!=(d=null==F?void 0:F.width)?d:0,j=null!=(p=null==F?void 0:F.height)?p:0,W="number"==typeof A?A:{top:0,right:0,bottom:0,left:0,...A},z=Array.isArray(b)?b:[b],B=z.length>0,_={padding:W,boundary:z.filter(t6),altBoundary:B},{refs:I,floatingStyles:V,placement:X,isPositioned:Y,middlewareData:G}=function(t){void 0===t&&(t={});let{placement:e="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:f=!0,whileElementsMounted:s,open:u}=t,[c,d]=r.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);tT(p,i)||h(i);let[g,m]=r.useState(null),[y,w]=r.useState(null),x=r.useCallback(t=>{t!==R.current&&(R.current=t,m(t))},[]),v=r.useCallback(t=>{t!==S.current&&(S.current=t,w(t))},[]),b=l||g,A=a||y,R=r.useRef(null),S=r.useRef(null),T=r.useRef(c),L=null!=s,E=tC(s),C=tC(o),O=tC(u),P=r.useCallback(()=>{if(!R.current||!S.current)return;let t={placement:e,strategy:n,middleware:p};C.current&&(t.platform=C.current),tA(R.current,S.current,t).then(t=>{let e={...t,isPositioned:!1!==O.current};k.current&&!tT(T.current,e)&&(T.current=e,tR.flushSync(()=>{d(e)}))})},[p,e,n,C,O]);tS(()=>{!1===u&&T.current.isPositioned&&(T.current.isPositioned=!1,d(t=>({...t,isPositioned:!1})))},[u]);let k=r.useRef(!1);tS(()=>(k.current=!0,()=>{k.current=!1}),[]),tS(()=>{if(b&&(R.current=b),A&&(S.current=A),b&&A){if(E.current)return E.current(b,A,P);P()}},[b,A,P,E,L]);let D=r.useMemo(()=>({reference:R,floating:S,setReference:x,setFloating:v}),[x,v]),H=r.useMemo(()=>({reference:b,floating:A}),[b,A]),F=r.useMemo(()=>{let t={position:n,left:0,top:0};if(!H.floating)return t;let e=tE(H.floating,c.x),r=tE(H.floating,c.y);return f?{...t,transform:"translate("+e+"px, "+r+"px)",...tL(H.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:e,top:r}},[n,f,H.floating,c.x,c.y]);return r.useMemo(()=>({...c,update:P,refs:D,elements:H,floatingStyles:F}),[c,P,D,H,F])}({strategy:"fixed",placement:g+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(t,e,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:u="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=tl(t),h=a||s?[...p?tr(p):[],...tr(e)]:[];h.forEach(t=>{a&&t.addEventListener("scroll",n,{passive:!0}),s&&t.addEventListener("resize",n)});let g=p&&c?function(t,e){let n,r=null,i=M(t);function a(){var t;clearTimeout(n),null==(t=r)||t.disconnect(),r=null}return!function s(u,c){void 0===u&&(u=!1),void 0===c&&(c=1),a();let d=t.getBoundingClientRect(),{left:p,top:h,width:g,height:m}=d;if(u||e(),!g||!m)return;let y=f(h),w=f(i.clientWidth-(p+g)),x={rootMargin:-y+"px "+-w+"px "+-f(i.clientHeight-(h+m))+"px "+-f(p)+"px",threshold:l(0,o(1,c))||1},v=!0;function b(e){let r=e[0].intersectionRatio;if(r!==c){if(!v)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||tv(d,t.getBoundingClientRect())||s(),v=!1}try{r=new IntersectionObserver(b,{...x,root:i.ownerDocument})}catch(t){r=new IntersectionObserver(b,x)}r.observe(t)}(!0),a}(p,n):null,m=-1,y=null;u&&(y=new ResizeObserver(t=>{let[r]=t;r&&r.target===p&&y&&(y.unobserve(e),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var t;null==(t=y)||t.observe(e)})),n()}),p&&!d&&y.observe(p),y.observe(e));let w=d?tu(t):null;return d&&function e(){let r=tu(t);w&&!tv(w,r)&&n(),w=r,i=requestAnimationFrame(e)}(),n(),()=>{var t;h.forEach(t=>{a&&t.removeEventListener("scroll",n),s&&t.removeEventListener("resize",n)}),null==g||g(),null==(t=y)||t.disconnect(),y=null,d&&cancelAnimationFrame(i)}}(...e,{animationFrame:"always"===T})},elements:{reference:C.anchor},middleware:[tP({mainAxis:m+j,alignmentAxis:w}),v&&tk({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?tD():void 0,..._}),v&&tH({..._}),tF({..._,apply:t=>{let{elements:e,rects:n,availableWidth:r,availableHeight:i}=t,{width:o,height:l}=n.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),D&&tj({element:D,padding:x}),t9({arrowWidth:N,arrowHeight:j}),S&&tN({strategy:"referenceHidden",..._})]}),[$,q]=t3(X),U=(0,tI.c)(L);(0,tV.N)(()=>{Y&&(null==U||U())},[Y,U]);let J=null==(n=G.arrow)?void 0:n.x,K=null==(i=G.arrow)?void 0:i.y,Q=(null==(a=G.arrow)?void 0:a.centerOffset)!==0,[Z,tt]=r.useState();return(0,tV.N)(()=>{O&&tt(window.getComputedStyle(O).zIndex)},[O]),(0,tW.jsx)("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:Y?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Z,"--radix-popper-transform-origin":[null==(s=G.transformOrigin)?void 0:s.x,null==(u=G.transformOrigin)?void 0:u.y].join(" "),...(null==(c=G.hide)?void 0:c.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:(0,tW.jsx)(t0,{scope:h,placedSide:$,onArrowChange:H,arrowX:J,arrowY:K,shouldHideArrow:Q,children:(0,tW.jsx)(tM.sG.div,{"data-side":$,"data-align":q,...E,ref:k,style:{...E.style,animation:Y?void 0:"none"}})})})});t2.displayName=tZ;var t5="PopperArrow",t4={top:"bottom",right:"left",bottom:"top",left:"right"},t8=r.forwardRef(function(t,e){let{__scopePopper:n,...r}=t,i=t1(t5,n),o=t4[i.placedSide];return(0,tW.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,tW.jsx)(tz,{...r,ref:e,style:{...r.style,display:"block"}})})});function t6(t){return null!==t}t8.displayName=t5;var t9=t=>({name:"transformOrigin",options:t,fn(e){var n,r,i,o,l;let{placement:a,rects:f,middlewareData:s}=e,u=(null==(n=s.arrow)?void 0:n.centerOffset)!==0,c=u?0:t.arrowWidth,d=u?0:t.arrowHeight,[p,h]=t3(a),g={start:"0%",center:"50%",end:"100%"}[h],m=(null!=(o=null==(r=s.arrow)?void 0:r.x)?o:0)+c/2,y=(null!=(l=null==(i=s.arrow)?void 0:i.y)?l:0)+d/2,w="",x="";return"bottom"===p?(w=u?g:"".concat(m,"px"),x="".concat(-d,"px")):"top"===p?(w=u?g:"".concat(m,"px"),x="".concat(f.floating.height+d,"px")):"right"===p?(w="".concat(-d,"px"),x=u?g:"".concat(y,"px")):"left"===p&&(w="".concat(f.floating.width+d,"px"),x=u?g:"".concat(y,"px")),{data:{x:w,y:x}}}});function t3(t){let[e,n="center"]=t.split("-");return[e,n]}var t7=tJ,et=tQ,ee=t2,en=t8},84288:(t,e,n)=>{n.d(e,{X:()=>o});var r=n(12115),i=n(4129);function o(t){let[e,n]=r.useState(void 0);return(0,i.N)(()=>{if(t){n({width:t.offsetWidth,height:t.offsetHeight});let e=new ResizeObserver(e=>{let r,i;if(!Array.isArray(e)||!e.length)return;let o=e[0];if("borderBoxSize"in o){let t=o.borderBoxSize,e=Array.isArray(t)?t[0]:t;r=e.inlineSize,i=e.blockSize}else r=t.offsetWidth,i=t.offsetHeight;n({width:r,height:i})});return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}n(void 0)},[t]),e}}}]);