try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="5b6d5298-1383-4543-b2a3-999645b3bdd0",e._sentryDebugIdIdentifier="sentry-dbid-5b6d5298-1383-4543-b2a3-999645b3bdd0")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5544],{874:(e,t,a)=>{Promise.resolve().then(a.bind(a,21111))},21111:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>h});var i=a(95155),r=a(20063),o=a(20764),n=a(35626),s=a(95740),d=a(18720),c=a(61289),l=a(52619),u=a.n(l),p=a(79676),m=a(12115);function h(){let e=(0,r.useRouter)(),{user:t}=(0,c.A)(),[a,l]=(0,m.useState)(!1),h=async e=>{try{l(!0),new FormData().append("file",e);let t=await fetch("/api/upload?filename=".concat(e.name),{method:"POST",body:e});if(!t.ok)throw Error("Failed to upload image");return(await t.json()).url}catch(e){return console.error("Error uploading image:",e),d.oR.error("Failed to upload cover image"),null}finally{l(!1)}},y=async a=>{if(!t)return void d.oR.error("Please log in to create courses");try{let i=null;if(a.coverImage&&(d.oR.loading("Uploading cover image...",{id:"course-creation"}),!(i=await h(a.coverImage))))return;d.oR.loading("Creating course...",{id:"course-creation"});let r=await fetch("/api/courses",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:a.name,description:a.description,instructor:a.instructor,courseCode:a.courseCode,type:a.type,enrollmentType:a.enrollmentType,startDate:a.startDate,endDate:a.endDate,teacherId:t.id,institutionId:t.institutionId,coverPicture:i,isPurchasable:"purchase"===a.enrollmentType||"both"===a.enrollmentType,price:a.price,currency:a.currency,previewMode:a.previewMode,admissions:a.admissions,academics:a.academics,tuitionAndFinancing:a.tuitionAndFinancing,careers:a.careers,studentExperience:a.studentExperience})}),o=await r.json();if(!o.success)throw Error(o.error||"Failed to create course");let n=o.course;d.oR.loading("Creating modules...",{id:"course-creation"});let s=[];for(let e=0;e<a.modules.length;e++){let i=a.modules[e],r=await fetch("/api/modules",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:i.name,description:i.description,courseId:n.id,teacherId:t.id,orderIndex:i.orderIndex})}),o=await r.json();if(!o.module)throw Error("Failed to create module: ".concat(i.name));s.push({...o.module,originalData:i})}d.oR.loading("Creating chapters...",{id:"course-creation"});let c=[];for(let e of s){let a=e.originalData;for(let i=0;i<a.chapters.length;i++){let r=a.chapters[i],o=await fetch("/api/chapters",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:r.name,content:r.content,moduleId:e.id,teacherId:t.id,orderIndex:r.orderIndex})}),n=await o.json();if(!n.chapter)throw Error("Failed to create chapter: ".concat(r.name));c.push({...n.chapter,originalData:r})}}if(a.modules.some(e=>e.chapters.some(e=>e.hasChapterQuiz&&e.chapterQuiz)))for(let e of(d.oR.loading("Creating quizzes...",{id:"course-creation"}),c)){let a=e.originalData;if(a.hasChapterQuiz&&a.chapterQuiz){let i=a.chapterQuiz,r=await fetch("/api/quizzes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:i.name,description:i.description,quizType:"chapter",timeLimit:i.timeLimit,minimumScore:i.minimumScore,chapterId:e.id,teacherId:t.id,questions:i.questions.map(e=>({type:e.type,question:e.question,options:e.options,essayAnswer:e.essayAnswer,explanation:e.explanation,points:e.points,orderIndex:e.orderIndex}))})});(await r.json()).quiz||console.warn("Failed to create quiz for chapter: ".concat(a.name))}}if(a.modules.some(e=>e.hasModuleQuiz&&e.moduleQuiz))for(let e of(d.oR.loading("Creating module quizzes...",{id:"course-creation"}),s)){let a=e.originalData;if(a.hasModuleQuiz&&a.moduleQuiz){let i=a.moduleQuiz,r=await fetch("/api/quizzes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:i.name,description:i.description,quizType:"module",timeLimit:i.timeLimit,minimumScore:i.minimumScore,moduleId:e.id,teacherId:t.id,questions:i.questions.map(e=>({type:e.type,question:e.question,options:e.options,essayAnswer:e.essayAnswer,explanation:e.explanation,points:e.points,orderIndex:e.orderIndex}))})});(await r.json()).quiz||console.warn("Failed to create module quiz: ".concat(i.name))}}if(a.finalExam){d.oR.loading("Creating final exam...",{id:"course-creation"});let e=a.finalExam,i=await fetch("/api/quizzes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,description:e.description,quizType:"final",timeLimit:e.timeLimit,minimumScore:e.minimumScore,courseId:n.id,teacherId:t.id,questions:e.questions.map(e=>({type:e.type,question:e.question,options:e.options,essayAnswer:e.essayAnswer,explanation:e.explanation,points:e.points,orderIndex:e.orderIndex}))})});(await i.json()).quiz||console.warn("Failed to create final exam")}d.oR.success("Course created successfully with all modules, chapters, and quizzes!",{id:"course-creation"}),e.push("/dashboard/teacher/courses")}catch(e){console.error("Error creating course:",e),d.oR.error(e instanceof Error?e.message:"Failed to create course",{id:"course-creation"})}};return(0,i.jsxs)("div",{className:"space-y-6","data-sentry-component":"NewCoursePage","data-sentry-source-file":"page.tsx",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)(u(),{href:"/dashboard/teacher/courses","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,i.jsxs)(o.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,i.jsx)(n.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ArrowLeft","data-sentry-source-file":"page.tsx"}),"Back"]})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Create New Course"}),(0,i.jsx)("p",{className:"text-muted-foreground",children:"Create a comprehensive course with our step-by-step wizard"})]})]}),(0,i.jsx)(u(),{href:"/dashboard/teacher/courses/generate","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,i.jsxs)(o.$,{variant:"outline","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,i.jsx)(s.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Sparkles","data-sentry-source-file":"page.tsx"}),"Try AI Generator"]})})]}),(0,i.jsx)(p.q,{onComplete:y,onCancel:()=>e.push("/dashboard/teacher/courses"),"data-sentry-element":"CourseCreationWizard","data-sentry-source-file":"page.tsx"})]})}},61289:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var i=a(12115),r=a(47886),o=a(20063);function n(){let[e,t]=(0,i.useState)(null),[a,n]=(0,i.useState)(!0),s=(0,o.useRouter)();return(0,i.useEffect)(()=>{let e=r.qs.getUser();e&&t(e),n(!1)},[]),{user:e,loading:a,signOut:()=>{r.qs.removeUser(),t(null),s.push("/auth/sign-in")}}}},95740:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(71847).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[5105,4909,7055,4736,660,8720,6093,9568,5239,5667,6464,5439,2413,5364,3500,65,4850,8441,3840,7358],()=>t(874)),_N_E=e.O()}]);