try{let n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},e=(new n.Error).stack;e&&(n._sentryDebugIds=n._sentryDebugIds||{},n._sentryDebugIds[e]="7c63ac9b-48aa-4a16-aa71-faf654182859",n._sentryDebugIdIdentifier="sentry-dbid-7c63ac9b-48aa-4a16-aa71-faf654182859")}catch(n){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2562],{39867:(n,e,t)=>{t.d(e,{A:()=>a});let a=(0,t(71847).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},47886:(n,e,t)=>{t.d(e,{WG:()=>i,cl:()=>o,qs:()=>a});let a={setUser:n=>{localStorage.setItem("auth_user",JSON.stringify(n))},getUser:()=>{{let n=localStorage.getItem("auth_user");return n?JSON.parse(n):null}},removeUser:()=>{localStorage.removeItem("auth_user")},isAuthenticated:()=>null!==a.getUser(),hasRole:n=>{let e=a.getUser();return(null==e?void 0:e.role)===n},isSuperAdmin:()=>a.hasRole("super_admin"),isTeacher:()=>a.hasRole("teacher"),isStudent:()=>a.hasRole("student")},i=n=>{switch(n.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},r=()=>{let n=a.getUser();return n||(window.location.href="/auth/sign-in",null)},o=n=>{let e=r();return e?e.role!==n?(window.location.href=i(e),null):e:null}},99223:(n,e,t)=>{t.d(e,{Ct:()=>o,K2:()=>a,a7:()=>c,hr:()=>s,nE:()=>r});let a=()=>{let n=new Date().getFullYear(),e=Math.floor(1e4*Math.random()).toString().padStart(4,"0");return"CERT-".concat(n,"-").concat(e)},i=n=>'\n  <!DOCTYPE html>\n  <html lang="id">\n  <head>\n      <meta charset="UTF-8">\n      <meta name="viewport" content="width=device-width, initial-scale=1.0">\n      <title>Sertifikat Kelulusan</title>\n      \x3c!-- Font --\x3e\n      <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@700&display=swap" rel="stylesheet">\n      <style>\n          :root{\n              --primary:#4a90e2;\n              --accent:#a370e8;\n              --text:#2c3e50;\n              --muted:#7f8c8d;\n              --light:#f0f4f8;\n              --white:#ffffff;\n          }\n          *{box-sizing:border-box;margin:0;padding:0}\n          body{\n              background:linear-gradient(135deg,var(--light),#e2e8f0);\n              font-family:\'Montserrat\',sans-serif;\n              display:flex;\n              align-items:center;\n              justify-content:center;\n              min-height:100vh;\n              padding:20px;\n          }\n          .certificate{\n              width:100%;\n              max-width:900px;\n              background:var(--white);\n              border-radius:16px;\n              box-shadow:0 20px 40px rgba(0,0,0,.08);\n              position:relative;\n              overflow:hidden;\n              padding:80px 80px 110px;\n          }\n          .certificate::before,\n          .certificate::after{\n              content:\'\';\n              position:absolute;\n              width:300px;\n              height:300px;\n              border-radius:50%;\n              opacity:.05;\n              z-index:0;\n          }\n          .certificate::before{top:-80px;left:-80px;background:radial-gradient(var(--primary),transparent 70%)}\n          .certificate::after{bottom:-80px;right:-80px;background:radial-gradient(var(--accent),transparent 70%)}\n\n          .watermark{\n              position:absolute;\n              top:50%;left:50%;\n              transform:translate(-50%,-50%) rotate(-45deg);\n              font-family:\'Playfair Display\',serif;\n              font-size:150px;\n              color:rgba(0,0,0,.03);\n              font-weight:700;\n              pointer-events:none;\n              z-index:0;\n          }\n\n          .header{text-align:center;margin-bottom:50px}\n          .title{\n              font-family:\'Playfair Display\',serif;\n              font-size:44px;\n              color:var(--text);\n              margin:0;\n          }\n          .subtitle{\n              font-size:16px;\n              color:var(--muted);\n              margin-top:8px;\n          }\n\n          .main-content{\n              text-align:center;\n              margin-bottom:60px;\n          }\n          .awarded-to{\n              font-size:16px;\n              color:var(--muted);\n              margin-bottom:8px;\n          }\n          .student-name{\n              font-family:\'Playfair Display\',serif;\n              font-size:42px;\n              color:var(--text);\n              position:relative;\n              display:inline-block;\n              margin-bottom:20px;\n          }\n          .student-name::after{\n              content:\'\';\n              position:absolute;\n              left:50%;\n              bottom:-6px;\n              transform:translateX(-50%);\n              width:80%;\n              height:3px;\n              background:linear-gradient(90deg,var(--primary),var(--accent));\n              border-radius:2px;\n          }\n          .completion-text{\n              font-size:18px;\n              color:#555;\n              line-height:1.6;\n              max-width:600px;\n              margin:0 auto 25px;\n          }\n          .course-details{\n              display:inline-block;\n              background:var(--light);\n              border-radius:12px;\n              padding:20px 35px;\n              box-shadow:0 4px 15px rgba(0,0,0,.05);\n              margin-bottom:25px;\n          }\n          .course-name{\n              font-size:24px;\n              font-weight:600;\n              color:var(--text);\n              margin:0;\n          }\n          .course-code{\n              font-size:15px;\n              color:var(--muted);\n              margin-top:4px;\n          }\n          .score{\n              font-size:20px;\n              font-weight:700;\n              color:var(--primary);\n          }\n\n          .footer{\n              display:flex;\n              justify-content:space-around;\n              align-items:flex-end;\n              border-top:1px solid #ecf0f1;\n              padding-top:30px;\n          }\n          .signature-section{\n              text-align:center;\n              flex:1;\n          }\n          .signature-line{\n              width:180px;\n              height:1px;\n              background:var(--muted);\n              margin:0 auto 8px;\n          }\n          .signature-label{\n              font-size:14px;\n              color:var(--muted);\n              line-height:1.4;\n          }\n\n          .id-date-row{\n              margin-top:30px;\n              display:flex;\n              justify-content:space-between;\n              font-size:13px;\n              color:#95a5a6;\n          }\n      </style>\n  </head>\n  <body>\n      <div class="certificate">\n          <div class="watermark">TERANG</div>\n\n          \x3c!-- Konten utama --\x3e\n          <div class="header">\n              <h1 class="title">Sertifikat Kelulusan</h1>\n              <p class="subtitle">'.concat(n.institutionName,'</p>\n          </div>\n\n          <div class="main-content">\n              <p class="awarded-to">Dengan bangga mempersembahkan sertifikat ini kepada</p>\n              <h2 class="student-name">').concat(n.studentName,'</h2>\n              <p class="completion-text">\n                  karena telah berhasil menyelesaikan dan lulus dari program\n              </p>\n\n              <div class="course-details">\n                  <h3 class="course-name">').concat(n.courseName,'</h3>\n                  <div class="course-code">Kode Kursus: ').concat(n.courseCode,'</div>\n              </div>\n\n              <p class="score">Nilai Akhir: ').concat(n.finalScore,'%</p>\n          </div>\n\n          <div class="footer">\n              <div class="signature-section">\n                  <div class="signature-line"></div>\n                  <p class="signature-label">').concat(n.instructorName,'<br>Instruktur Kursus</p>\n              </div>\n              <div class="signature-section">\n                  <div class="signature-line"></div>\n                  <p class="signature-label">Tanggal Kelulusan<br>').concat(n.completionDate,'</p>\n              </div>\n          </div>\n\n          <div class="id-date-row">\n              <span>ID Sertifikat: ').concat(n.certificateId,"</span>\n              <span>Diterbitkan pada: ").concat(n.completionDate,"</span>\n          </div>\n      </div>\n  </body>\n  </html>\n  "),r=n=>'\n  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@700&display=swap" rel="stylesheet">\n  <style>\n      .certificate-modal-container {\n          --primary:#4a90e2;\n          --accent:#a370e8;\n          --text:#2c3e50;\n          --muted:#7f8c8d;\n          --light:#f0f4f8;\n          --white:#ffffff;\n          font-family:\'Montserrat\',sans-serif;\n          width: 100%;\n          min-height: 600px;\n          background: var(--white);\n          position: relative;\n      }\n      .certificate-modal{\n          width:100%;\n          background:var(--white);\n          border-radius:16px;\n          box-shadow:0 20px 40px rgba(0,0,0,.08);\n          position:relative;\n          overflow:hidden;\n          padding:60px 60px 80px;\n          margin: 0;\n      }\n      .certificate-modal *{box-sizing:border-box;}\n      \n      .certificate-modal::before,\n      .certificate-modal::after{\n          content:\'\';\n          position:absolute;\n          width:250px;\n          height:250px;\n          border-radius:50%;\n          opacity:.05;\n          z-index:0;\n      }\n      .certificate-modal::before{top:-60px;left:-60px;background:radial-gradient(var(--primary),transparent 70%)}\n      .certificate-modal::after{bottom:-60px;right:-60px;background:radial-gradient(var(--accent),transparent 70%)}\n\n      .certificate-modal .watermark{\n          position:absolute;\n          top:50%;left:50%;\n          transform:translate(-50%,-50%) rotate(-45deg);\n          font-family:\'Playfair Display\',serif;\n          font-size:100px;\n          color:rgba(0,0,0,.03);\n          font-weight:700;\n          pointer-events:none;\n          z-index:0;\n      }\n\n      .certificate-modal .header{text-align:center;margin-bottom:40px;position:relative;z-index:1;}\n      .certificate-modal .title{\n          font-family:\'Playfair Display\',serif;\n          font-size:32px;\n          color:var(--text);\n          margin:0;\n      }\n      .certificate-modal .subtitle{\n          font-size:14px;\n          color:var(--muted);\n          margin-top:8px;\n      }\n\n      .certificate-modal .main-content{\n          text-align:center;\n          margin-bottom:50px;\n          position:relative;\n          z-index:1;\n      }\n      .certificate-modal .awarded-to{\n          font-size:14px;\n          color:var(--muted);\n          margin-bottom:8px;\n      }\n      .certificate-modal .student-name{\n          font-family:\'Playfair Display\',serif;\n          font-size:28px;\n          color:var(--text);\n          position:relative;\n          display:inline-block;\n          margin-bottom:20px;\n      }\n      .certificate-modal .student-name::after{\n          content:\'\';\n          position:absolute;\n          left:50%;\n          bottom:-6px;\n          transform:translateX(-50%);\n          width:80%;\n          height:3px;\n          background:linear-gradient(90deg,var(--primary),var(--accent));\n          border-radius:2px;\n      }\n      .certificate-modal .completion-text{\n          font-size:15px;\n          color:#555;\n          line-height:1.6;\n          max-width:500px;\n          margin:0 auto 20px;\n      }\n      .certificate-modal .course-details{\n          display:inline-block;\n          background:var(--light);\n          border-radius:12px;\n          padding:16px 30px;\n          box-shadow:0 4px 15px rgba(0,0,0,.05);\n          margin-bottom:20px;\n      }\n      .certificate-modal .course-name{\n          font-size:18px;\n          font-weight:600;\n          color:var(--text);\n          margin:0;\n      }\n      .certificate-modal .course-code{\n          font-size:13px;\n          color:var(--muted);\n          margin-top:4px;\n      }\n      .certificate-modal .score{\n          font-size:16px;\n          font-weight:700;\n          color:var(--primary);\n      }\n\n      .certificate-modal .footer{\n          display:flex;\n          justify-content:space-around;\n          align-items:flex-end;\n          border-top:1px solid #ecf0f1;\n          padding-top:25px;\n          position:relative;\n          z-index:1;\n      }\n      .certificate-modal .signature-section{\n          text-align:center;\n          flex:1;\n      }\n      .certificate-modal .signature-line{\n          width:140px;\n          height:1px;\n          background:var(--muted);\n          margin:0 auto 8px;\n      }\n      .certificate-modal .signature-label{\n          font-size:12px;\n          color:var(--muted);\n          line-height:1.4;\n      }\n\n      .certificate-modal .id-date-row{\n          margin-top:25px;\n          display:flex;\n          justify-content:space-between;\n          font-size:11px;\n          color:#95a5a6;\n          position:relative;\n          z-index:1;\n      }\n  </style>\n  \n  <div class="certificate-modal-container">\n      <div class="certificate-modal">\n          <div class="watermark">TERANG</div>\n\n          <div class="header">\n              <h1 class="title">Sertifikat Kelulusan</h1>\n              <p class="subtitle">'.concat(n.institutionName,'</p>\n          </div>\n\n          <div class="main-content">\n              <p class="awarded-to">Dengan bangga mempersembahkan sertifikat ini kepada</p>\n              <h2 class="student-name">').concat(n.studentName,'</h2>\n              <p class="completion-text">\n                  karena telah berhasil menyelesaikan dan lulus dari program\n              </p>\n\n              <div class="course-details">\n                  <h3 class="course-name">').concat(n.courseName,'</h3>\n                  <div class="course-code">Kode Kursus: ').concat(n.courseCode,'</div>\n              </div>\n\n              <p class="score">Nilai Akhir: ').concat(n.finalScore,'%</p>\n          </div>\n\n          <div class="footer">\n              <div class="signature-section">\n                  <div class="signature-line"></div>\n                  <p class="signature-label">').concat(n.instructorName,'<br>Instruktur Kursus</p>\n              </div>\n              <div class="signature-section">\n                  <div class="signature-line"></div>\n                  <p class="signature-label">Tanggal Kelulusan<br>').concat(n.completionDate,'</p>\n              </div>\n          </div>\n\n          <div class="id-date-row">\n              <span>ID Sertifikat: ').concat(n.certificateId,"</span>\n              <span>Diterbitkan pada: ").concat(n.completionDate,"</span>\n          </div>\n      </div>\n  </div>\n  "),o=async n=>{try{let e=i(n),t=await fetch("/api/certificates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({htmlContent:e})});if(!t.ok)throw Error("Failed to generate PDF");let a=await t.blob(),r=URL.createObjectURL(a),o=document.createElement("a");o.href=r,o.download="certificate-".concat(n.certificateId,".pdf"),document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(r)}catch(r){console.error("Error generating PDF:",r);let e=new Blob([i(n)],{type:"text/html"}),t=URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download="certificate-".concat(n.certificateId,".html"),document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(t)}},c=n=>{let e=i(n),t=window.open("","_blank");t&&(t.document.write(e),t.document.close())},s=async n=>{if(navigator.share)try{await navigator.share({title:"Certificate of Completion - ".concat(n.courseName),text:"I've completed ".concat(n.courseName," with a score of ").concat(n.finalScore,"%!"),url:window.location.href})}catch(e){console.error("Error sharing certificate:",e),l("I've completed ".concat(n.courseName," with a score of ").concat(n.finalScore,"%! Certificate ID: ").concat(n.certificateId))}else l("I've completed ".concat(n.courseName," with a score of ").concat(n.finalScore,"%! Certificate ID: ").concat(n.certificateId))},l=n=>{navigator.clipboard.writeText(n).then(()=>{console.log("Certificate details copied to clipboard")}).catch(n=>{console.error("Failed to copy to clipboard:",n)})}}}]);