try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="edb29baf-71d1-4db1-af92-d3ba81082d13",e._sentryDebugIdIdentifier="sentry-dbid-edb29baf-71d1-4db1-af92-d3ba81082d13")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2989],{9005:(e,t,r)=>{"use strict";r.d(t,{EnrollmentProvider:()=>i,q:()=>a});var o=r(95155),l=r(12115),n=r(55542);let s=(0,l.createContext)(void 0),a=()=>{let e=(0,l.useContext)(s);if(!e)throw Error("useEnrollment must be used within an EnrollmentProvider");return e},i=e=>{let{children:t}=e,[r,a]=(0,l.useState)(!1),[i,d]=(0,l.useState)(n.n4),[m,u]=(0,l.useState)([]),c="lms-enrollment-data",g="lms-multiple-enrollment-data";(0,l.useEffect)(()=>{(()=>{try{let e=localStorage.getItem(g);if(e){let t=JSON.parse(e);Date.now()<t.expirationTime?(u(t.enrolledCourses),a(t.enrolledCourses.length>0),t.enrolledCourses.length>0&&d(t.enrolledCourses[0])):localStorage.removeItem(g);return}let t=localStorage.getItem(c);if(t){let e=JSON.parse(t);if(Date.now()<e.expirationTime){a(e.isEnrolled),d(e.courseData),u([e.courseData]);let t={enrolledCourses:[e.courseData],enrollmentTimestamp:e.enrollmentTimestamp,expirationTime:e.expirationTime};localStorage.setItem(g,JSON.stringify(t)),localStorage.removeItem(c)}else localStorage.removeItem(c)}}catch(e){console.error("Failed to load enrollment data:",e),localStorage.removeItem(c),localStorage.removeItem(g)}})()},[]);let f=e=>{let t=Date.now();try{u(r=>{let o,l={enrolledCourses:o=r.some(t=>t.id===e.id)?r.map(t=>t.id===e.id?e:t):[...r,e],enrollmentTimestamp:t,expirationTime:t+6e5};return localStorage.setItem(g,JSON.stringify(l)),o}),setTimeout(()=>{localStorage.removeItem(g),a(!1),u([]),d(n.n4)},6e5)}catch(e){console.error("Failed to persist enrollment data:",e)}};return(0,o.jsx)(s.Provider,{value:{isEnrolled:r,courseData:i,enrollInCourse:()=>{a(!0);let e={...n.n4,status:"in-progress"};d(e),f(e)},enrollInCourseWithPurchase:e=>{a(!0);let t={...e,status:"in-progress",totalProgress:0};d(t),f(t)},updateCourseProgress:e=>{i.id===e.id&&d(e),u(t=>t.map(t=>t.id===e.id?e:t)),r&&f(e)},enrolledCourses:m,isEnrolledInCourse:e=>m.some(t=>t.id===e),getCourseById:e=>m.find(t=>t.id===e)},"data-sentry-element":"EnrollmentContext.Provider","data-sentry-component":"EnrollmentProvider","data-sentry-source-file":"enrollment-context.tsx",children:t})}},26731:(e,t,r)=>{Promise.resolve().then(r.bind(r,99813)),Promise.resolve().then(r.bind(r,7648)),Promise.resolve().then(r.bind(r,37287)),Promise.resolve().then(r.bind(r,28370)),Promise.resolve().then(r.bind(r,9005))}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,7055,4736,660,6093,5239,7971,6464,1675,5521,4983,5542,1118,4850,8441,3840,7358],()=>t(26731)),_N_E=e.O()}]);