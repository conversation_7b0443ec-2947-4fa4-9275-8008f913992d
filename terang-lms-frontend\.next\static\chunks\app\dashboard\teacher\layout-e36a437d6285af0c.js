try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="0c971567-c716-41dd-a8bc-aada73e994d0",e._sentryDebugIdIdentifier="sentry-dbid-0c971567-c716-41dd-a8bc-aada73e994d0")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8091],{47886:(e,t,r)=>{"use strict";r.d(t,{WG:()=>a,cl:()=>l,qs:()=>s});let s={setUser:e=>{localStorage.setItem("auth_user",JSON.stringify(e))},getUser:()=>{{let e=localStorage.getItem("auth_user");return e?JSON.parse(e):null}},removeUser:()=>{localStorage.removeItem("auth_user")},isAuthenticated:()=>null!==s.getUser(),hasRole:e=>{let t=s.getUser();return(null==t?void 0:t.role)===e},isSuperAdmin:()=>s.hasRole("super_admin"),isTeacher:()=>s.hasRole("teacher"),isStudent:()=>s.hasRole("student")},a=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},n=()=>{let e=s.getUser();return e||(window.location.href="/auth/sign-in",null)},l=e=>{let t=n();return t?t.role!==e?(window.location.href=a(t),null):t:null}},69444:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(95155),a=r(12115),n=r(47886);function l(e){let{children:t}=e;return(0,a.useEffect)(()=>{(0,n.cl)("teacher")},[]),(0,s.jsx)(s.Fragment,{children:t})}},71207:(e,t,r)=>{Promise.resolve().then(r.bind(r,69444))}},e=>{var t=t=>e(e.s=t);e.O(0,[4850,8441,3840,7358],()=>t(71207)),_N_E=e.O()}]);